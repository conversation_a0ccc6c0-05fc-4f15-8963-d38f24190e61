{"options": {"validator": {"$jsonSchema": {"required": ["expires"], "properties": {"session": {"bsonType": "string"}, "expires": {"bsonType": "date"}}, "bsonType": "object"}}}, "indexes": [{"v": {"$numberInt": "2"}, "key": {"_id": {"$numberInt": "1"}}, "name": "_id_"}, {"v": {"$numberInt": "2"}, "key": {"expires": {"$numberInt": "1"}}, "name": "expires_1", "background": true, "expireAfterSeconds": {"$numberInt": "0"}}, {"v": {"$numberInt": "2"}, "key": {"user": {"$numberInt": "1"}}, "name": "user_1", "background": true}, {"v": {"$numberInt": "2"}, "key": {"sessionId": {"$numberInt": "1"}}, "name": "sessionId_1", "background": true, "unique": true}, {"v": {"$numberInt": "2"}, "key": {"trustedDevice": {"$numberInt": "1"}}, "name": "trustedDevice_1", "background": true}, {"v": {"$numberInt": "2"}, "key": {"lastActive": {"$numberInt": "1"}}, "name": "lastActive_1", "background": true}, {"v": {"$numberInt": "2"}, "key": {"isValid": {"$numberInt": "1"}}, "name": "isValid_1", "background": true}, {"v": {"$numberInt": "2"}, "key": {"expiresAt": {"$numberInt": "1"}}, "name": "expiresAt_1", "background": true}, {"v": {"$numberInt": "2"}, "key": {"user": {"$numberInt": "1"}, "isValid": {"$numberInt": "1"}}, "name": "user_1_isValid_1", "background": true}], "uuid": "7392d657815645f38b29ce2194050994", "collectionName": "sessions", "type": "collection"}