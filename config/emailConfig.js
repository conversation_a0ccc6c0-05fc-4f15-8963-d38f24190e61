const nodemailer = require("nodemailer");

// Enhanced email configuration with better error handling and validation
console.log("🔧 Initializing Email Configuration...");
console.log("Environment:", process.env.NODE_ENV);
console.log("Email Host:", process.env.EMAIL_HOST);
console.log("Email Port:", process.env.EMAIL_PORT);
console.log("Email User:", process.env.EMAIL_USER ? "✅ Set" : "❌ Not set");
console.log("Email Pass:", process.env.EMAIL_PASS ? "✅ Set" : "❌ Not set");
console.log("Email From Name:", process.env.EMAIL_FROM_NAME || "Not set");

// Validate required environment variables
if (!process.env.EMAIL_HOST || !process.env.EMAIL_USER || !process.env.EMAIL_PASS) {
  console.error("❌ Missing required email environment variables!");
  console.error("Required: EMAIL_HOST, EMAIL_USER, EMAIL_PASS");
  throw new Error("Email configuration incomplete");
}

// Create reusable transporter object using SMTP
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST,
  port: parseInt(process.env.EMAIL_PORT) || 587,
  secure: process.env.EMAIL_SECURE === "true", // false for 587, true for 465
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
  // Add additional configuration for better reliability
  pool: true, // Use pooled connections
  maxConnections: 5, // Limit concurrent connections
  maxMessages: 100, // Limit messages per connection
  rateLimit: 14, // Limit to 14 messages per second
  debug: process.env.NODE_ENV === "development", // Enable debug in development
  logger: process.env.NODE_ENV === "development", // Enable logging in development
});

// Verify connection configuration with detailed logging
transporter.verify(function (error, success) {
  if (error) {
    console.error("Email config error:", error);
    console.log("Email configuration:", {
      host: process.env.EMAIL_HOST,
      port: process.env.EMAIL_PORT,
      secure: process.env.EMAIL_SECURE === "true",
      user: process.env.EMAIL_USER ? "Set" : "Not set",
      pass: process.env.EMAIL_PASS ? "Set" : "Not set",
    });
  } else {
    console.log("Email server is ready to send messages");
    console.log("Using Zoho SMTP configuration with:", {
      host: process.env.EMAIL_HOST,
      port: process.env.EMAIL_PORT,
      user: process.env.EMAIL_USER,
    });
  }
});

// Add a helper function to send emails with retries
transporter.sendMailWithRetry = async function (mailOptions, maxRetries = 3) {
  let lastError;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`Sending email attempt ${attempt}/${maxRetries}`, {
        to: mailOptions.to,
        subject: mailOptions.subject,
      });

      // Ensure proper FROM address formatting
      const fromAddress = process.env.EMAIL_USER;
      const fromName = process.env.EMAIL_FROM_NAME || "Panda Software Solutions Group";

      if (!fromAddress) {
        throw new Error("EMAIL_USER environment variable is required");
      }

      const info = await this.sendMail({
        ...mailOptions,
        from: `"${fromName}" <${fromAddress}>`,
      });

      console.log("Email sent successfully:", {
        messageId: info.messageId,
        to: mailOptions.to,
        attempt,
      });

      return info;
    } catch (error) {
      lastError = error;
      console.error(`Email sending attempt ${attempt} failed:`, error);

      if (attempt < maxRetries) {
        console.log(`Waiting before retry ${attempt + 1}...`);
        await new Promise((resolve) => setTimeout(resolve, 1000 * attempt));
      }
    }
  }

  throw new Error(
    `Failed to send email after ${maxRetries} attempts: ${lastError.message}`
  );
};

// Verify SMTP connection on startup
transporter.verify(function (error, success) {
  if (error) {
    console.error("❌ SMTP Connection Failed:", error.message);
    console.error("Email Configuration Check:");
    console.error("- Host:", process.env.EMAIL_HOST);
    console.error("- Port:", process.env.EMAIL_PORT);
    console.error("- User:", process.env.EMAIL_USER);
    console.error("- Secure:", process.env.EMAIL_SECURE);
  } else {
    console.log("✅ SMTP Connection Verified Successfully");
    console.log("📧 Email service ready for:", process.env.EMAIL_USER);
  }
});

// Add error event listener
transporter.on('error', (error) => {
  console.error("📧 Transporter Error:", error);
});

module.exports = transporter;
