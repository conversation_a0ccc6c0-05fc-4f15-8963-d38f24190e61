const mongoose = require('mongoose');

// Check if the model is already registered
const modelName = 'EFTSettings';
let EFTSettings;

try {
  // Try to retrieve the existing model
  EFTSettings = mongoose.model(modelName);
} catch (error) {
  // If model doesn't exist, create it
  const EFTSettingsSchema = new mongoose.Schema({
    company: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Company',
      required: true
    },
    bankFileFormat: {
      type: String,
      enum: ['absa', 'fnb', 'standard', 'nedbank'],
      required: true
    },
    bankName: {
      type: String,
      required: true
    },
    branchCode: {
      type: String,
      required: true
    },
    accountNumber: {
      type: String,
      required: true
    },
    accountHolder: {
      type: String,
      required: true
    },
    createdAt: {
      type: Date,
      default: Date.now
    },
    updatedAt: {
      type: Date,
      default: Date.now
    }
  });

  // Add a pre-save hook to update the updatedAt field
  EFTSettingsSchema.pre('save', function(next) {
    this.updatedAt = Date.now();
    next();
  });

  EFTSettings = mongoose.model(modelName, EFTSettingsSchema);
}

module.exports = EFTSettings;
