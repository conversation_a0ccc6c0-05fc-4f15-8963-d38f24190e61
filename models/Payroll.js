const mongoose = require("mongoose");
const Schema = mongoose.Schema;
const {
  INPUT_CATEGORIES,
  VALIDATION_RULES,
} = require("../constants/payrollValidation");

const payrollSchema = new mongoose.Schema({
  company: {
    type: Schema.Types.ObjectId,
    ref: "Company",
    required: true,
  },
  employee: {
    type: Schema.Types.ObjectId,
    ref: "Employee",
    required: true,
  },
  payrollPeriod: {
    type: Schema.Types.ObjectId,
    ref: "PayrollPeriod",
    required: false, // Optional for backward compatibility
  },
  month: {
    type: Date,
    required: true,
  },
  finalised: {
    type: Boolean,
    default: false,
  },
  finalisedDate: { type: Date },
  finalisedForFiling: {
    type: Boolean,
    default: false,
  },
  __v: Number,
  data: { type: Map, of: Schema.Types.Mixed },
  commission: {
    type: Number,
    default: 0,
  },
  commissionEnabled: {
    type: Boolean,
    default: false,
  },
  lossOfIncome: {
    type: Number,
    default: 0,
    min: [0, "Loss of income amount cannot be negative"],
    validate: {
      validator: function (value) {
        return value >= 0;
      },
      message: (props) => `${props.value} is not a valid loss of income amount`,
    },
  },
  lossOfIncomeEnabled: {
    type: Boolean,
    default: false,
  },
  travelAllowance: {
    fixedAllowance: { type: Boolean, default: false },
    fixedAllowanceAmount: {
      type: Number,
      default: 0,
      min: [0, "Fixed allowance amount cannot be negative"],
    },
    reimbursedExpenses: { type: Boolean, default: false },
    companyPetrolCard: { type: Boolean, default: false },
    reimbursedPerKmTravelled: { type: Boolean, default: false },
    ratePerKm: {
      type: Number,
      default: 0,
      min: [0, "Rate per km cannot be negative"],
    },
    businessKilometers: {
      type: Number,
      default: 0,
      min: [0, "Business kilometers cannot be negative"],
    },
    petrolCardValue: {
      type: Number,
      default: 0,
      min: [0, "Petrol card value cannot be negative"],
    },
    only20PercentTax: { type: Boolean, default: false },
    lastUpdated: { type: Date },
    calculationDetails: {
      totalAllowance: {
        type: Number,
        default: 0,
        min: [0, "Total allowance cannot be negative"],
      },
      taxableAmount: {
        type: Number,
        default: 0,
        min: [0, "Taxable amount cannot be negative"],
      },
      nonTaxableAmount: {
        type: Number,
        default: 0,
        min: [0, "Non-taxable amount cannot be negative"],
      },
      sarsPrescribedRate: {
        type: Number,
        default: 4.48, // 2024 default rate
        min: [0, "SARS prescribed rate cannot be negative"],
      },
      irpCodes: {
        fixedAllowance: { type: Number, default: 3701 },
        reimbursement: { type: Number, default: 3702 },
        petrolCard: { type: Number, default: 3703 },
      },
    },
  },
  annualBonus: {
    amount: {
      type: Number,
      default: 0,
    },
    enabled: {
      type: Boolean,
      default: false,
    },
    date: {
      type: Date,
    },
  },
  annualBonusEnabled: {
    type: Boolean,
    default: false,
  },
  annualPayment: {
    amount: {
      type: Number,
      default: 0,
    },
    enabled: {
      type: Boolean,
      default: false,
    },
    date: {
      type: Date,
    },
  },
  annualPaymentEnabled: {
    type: Boolean,
    default: false,
  },
  accommodationBenefit: {
    type: Number,
    default: 0,
  },
  bursariesAndScholarships: {
    type: {
      type: String,
      enum: [
        "Basic Education (Grades R to 12 and NQF levels 1 to 4)",
        "Further Education (NQF levels 5 to 10)",
      ],
    },
    taxablePortion: {
      type: Number,
      default: 0,
    },
    exemptPortion: {
      type: Number,
      default: 0,
    },
    employeeHandlesPayment: {
      type: Boolean,
      default: false,
    },
    toDisabledPerson: {
      type: Boolean,
      default: false,
    },
    date: {
      type: Date,
    },
    enabled: {
      type: Boolean,
      default: false,
    },
  },
  onceOffBursariesAndScholarships: {
    type: {
      type: String,
      enum: ["basic_education", "further_education"],
    },
    taxablePortion: {
      type: Number,
      default: 0,
    },
    exemptPortion: {
      type: Number,
      default: 0,
    },
    employeeHandlesPayment: {
      type: Boolean,
      default: false,
    },
    toDisabledPerson: {
      type: Boolean,
      default: false,
    },
    date: {
      type: Date,
    },
    enabled: {
      type: Boolean,
      default: false,
    },
  },
  companyCar: {
    deemedValue: { type: Number, default: null },
    includesMaintenancePlan: { type: Boolean, default: false },
    taxablePercentage: {
      type: String,
      enum: ["80%", "20%", "100%", null],
      default: null,
    },
  },
  companyCarUnderOperatingLease: {
    amount: { type: Number, default: 0 },
    taxablePercentage: { type: String, default: "0%" },
  },
  savings: {
    regularDeduction: {
      type: Number,
      default: 0,
      min: [0, "Regular deduction amount cannot be negative"],
      validate: {
        validator: function (value) {
          return value >= 0;
        },
        message: (props) =>
          `${props.value} is not a valid regular deduction amount`,
      },
    },
    balanceIncrease: {
      type: Number,
      default: 0,
      min: [0, "Balance increase amount cannot be negative"],
    },
    onceOffDeduction: {
      type: Number,
      default: 0,
      min: [0, "Once-off deduction amount cannot be negative"],
    },
    lastUpdated: { type: Date },
  },
  incomeProtection: {
    amountPaidByEmployee: { type: Number, default: 0 },
    amountDeductedFromEmployee: { type: Number, default: 0 },
    amountPaidByEmployer: { type: Number, default: 0 },
    employerOwnsPolicy: { type: Boolean, default: false },
    month: { type: Date, required: false }, // Add this line
  },
  garnishee: Number,
  basicSalary: {
    type: Number,
    default: 0,
  },
  hourlyPaid: {
    type: Boolean,
    default: false,
  },
  dontAutoPayPublicHolidays: {
    type: Boolean,
    default: false,
  },
  paidForAdditionalHours: {
    type: Boolean,
    default: false,
  },
  overrideCalculatedHourlyRate: {
    type: Boolean,
    default: false,
  },
  rateOverride: {
    type: Number,
    default: null,
  },
  maintenanceOrder: { type: Number, default: 0 },
  medical: {
    medicalAid: Number,
    members: Number,
    employerContribution: {
      type: Number,
      default: 0,
    },
    employeeHandlesPayment: {
      type: Boolean,
      default: false,
    },
    dontApplyTaxCredits: {
      type: Boolean,
      default: false,
    },
  },
  RFIDetermination: String,
  selectedComponents: [String],
  percentageComponents: [
    {
      component: String,
      percentage: Number,
    },
  ],
  pensionFund: {
    fixedContributionEmployee: {
      type: Number,
    },
    fixedContributionEmployer: {
      type: Number,
    },
    rfiEmployee: {
      type: Number,
      default: 0,
    },
    rfiEmployer: {
      type: Number,
      default: 0,
    },
    contributionCalculation: {
      type: String,
      enum: ["fixedAmount", "percentageRFI"],
    },
    categoryFactor: {
      type: Number,
      default: 1,
    },
    beneficiary: {
      type: String,
      default: "",
    },
  },
  providentFund: {
    contributionCalculation: {
      type: String,
      enum: ["fixedAmount", "percentageRFI"],
      default: "fixedAmount",
    },
    employeeContribution: {
      type: Number,
      default: 0,
    },
    employerContribution: {
      type: Number,
      default: 0,
    },
    beneficiary: {
      type: String,
      default: "",
    },
    categoryFactor: {
      type: Number,
      default: 1,
    },
    fixedContributionEmployee: {
      type: Number,
      default: 0,
    },
    fixedContributionEmployer: {
      type: Number,
      default: 0,
    },
    rfiEmployee: {
      type: Number,
      default: 0,
    },
    rfiEmployer: {
      type: Number,
      default: 0,
    },
  },
  // ... existing schema ...
  retirementAnnuityFund: {
    employeeContribution: {
      type: Number,
      default: 0,
      min: [0, "Employee contribution cannot be negative"],
    },
    employerContribution: {
      type: Number,
      default: 0,
      min: [0, "Employer contribution cannot be negative"],
    },
    employeeHandlesPayment: {
      type: Boolean,
      default: false,
    },
    beneficiary: {
      type: String,
      default: "",
    },
    lastUpdated: {
      type: Date,
      default: Date.now,
    },
    month: {
      type: Date,
      required: false
    },
    enabled: {
      type: Boolean,
      default: true
    }
  },
  voluntaryTaxOverDeduction: {
    type: Number,
    default: 0,
    min: [0, "Voluntary tax over deduction amount cannot be negative"],
    validate: {
      validator: function(value) {
        return value >= 0;
      },
      message: props => `${props.value} is not a valid voluntary tax over deduction amount`
    }
  },
  unionMembershipFee: { type: Number, default: 0 },
  employerLoan: {
    interestRate: {
      type: Number,
      default: 0,
      min: [0, "Interest rate cannot be negative"],
      max: [100, "Interest rate cannot exceed 100%"],
      validate: {
        validator: function (value) {
          return value >= 0 && value <= 100;
        },
        message: (props) =>
          `${props.value} is not a valid interest rate percentage`,
      },
    },
    regularRepayment: {
      type: Number,
      default: 0,
      min: [0, "Regular repayment amount cannot be negative"],
      validate: {
        validator: function (value) {
          return value >= 0;
        },
        message: (props) => `${props.value} is not a valid repayment amount`,
      },
    },
    calculateInterestBenefit: {
      type: Boolean,
      default: false,
    },
    balanceIncrease: {
      type: Number,
      default: 0,
      min: [0, "Balance increase amount cannot be negative"],
    },
    onceOffRepayment: {
      type: Number,
      default: 0,
      min: [0, "Once-off repayment amount cannot be negative"],
    },
    lastUpdated: { type: Date },
  },
  foreignServiceIncome: {
    foreignServiceTaxExemption: {
      type: Boolean,
      default: false,
    },
    nonForeignServiceIncome: {
      type: Number,
      default: 0,
      min: [0, "Non-foreign service income amount cannot be negative"],
      validate: {
        validator: function (value) {
          return value >= 0;
        },
        message: (props) => `${props.value} is not a valid income amount`,
      },
    },
    lastUpdated: { type: Date },
  },
  taxDirective: {
    directiveNumber: { type: String },
    directiveType: {
      type: String,
      enum: [
        "Fixed Amount - IRP3(c)",
        "Fixed Percentage - IRP3(b) and IRP3(pa)",
      ],
    },
    percentage: {
      type: Number,
      min: 0,
      max: 100,
      validate: {
        validator: function(value) {
          if (this.directiveType === "Fixed Percentage - IRP3(b) and IRP3(pa)") {
            return value >= 0 && value <= 100;
          }
          return true;
        },
        message: props => `${props.value} is not a valid percentage (must be between 0 and 100)`
      }
    },
    directiveIssueDate: { type: Date },
    directiveIncomeSourceCode: {
      type: String,
      enum: ["3707", "3908"],
      validate: {
        validator: function(value) {
          if (this.directiveType === "Fixed Amount - IRP3(c)") {
            return ["3707", "3908"].includes(value);
          }
          return true;
        },
        message: props => `${props.value} is not a valid directive income source code`
      }
    },
    directiveIncomeAmount: {
      type: Number,
      min: [0, "Directive income amount cannot be negative"],
      validate: {
        validator: function(value) {
          if (this.directiveType === "Fixed Amount - IRP3(c)") {
            return value >= 0;
          }
          return true;
        },
        message: props => `${props.value} is not a valid directive income amount`
      }
    },
    amountToDeduct: {
      type: Number,
      min: [0, "Amount to deduct cannot be negative"],
      validate: {
        validator: function(value) {
          if (this.directiveType === "Fixed Amount - IRP3(c)") {
            return value >= 0;
          }
          return true;
        },
        message: props => `${props.value} is not a valid amount to deduct`
      }
    }
  },
  longServiceAward: {
    cashPortion: { type: Number, default: 0 },
    nonCashPortion: { type: Number, default: 0 },
  },
  donations: { type: Number, default: 0 },
  extraPay: { type: Number, default: 0 },
  medicalCosts: {
    amount: {
      type: Number,
      default: 0,
    },
    beneficiary: {
      type: String,
      enum: ["employee_spouse_child", "other_relatives_dependants"],
    },
    date: {
      type: Date,
    },
    enabled: {
      type: Boolean,
      default: false,
    },
  },
  expenseClaim: { type: Number, default: 0 },
  tersPayout: { type: Number, default: 0 },
  gainVesting: {
    directiveNumber: { type: String },
    directiveIssueDate: { type: Date },
    taxDeductAmount: {
      type: Number,
      default: 0,
      min: [0, "Tax deduct amount cannot be negative"],
    },
    directiveIncomeAmount: {
      type: Number,
      default: 0,
      min: [0, "Directive income amount cannot be negative"],
    },
    date: { type: Date },
  },
  leavePaidOut: {
    leaveType: String,
    days: { type: Number, default: 0 },
    ratePerDay: { type: Number, default: 0 },
    date: Date,
  },
  toolAllowance: { type: Number, default: 0 },
  phoneAllowance: { type: Number, default: 0 },
  staffPurchases: { type: Number, default: 0 },
  arbitrationAward: {
    directiveIncomeAmount: {
      type: Number,
      default: 0,
    },
    taxDeductAmount: {
      type: Number,
      default: 0,
    },
    directiveNumber: String,
    directiveIssueDate: Date,
    amount: {
      type: Number,
      default: 0,
    },
  },
  dividendsSubject: {
    directiveNumber: String,
    directiveIssueDate: Date,
    directiveIncomeSourceCode: {
      type: String,
      enum: ["3719", "3720", "3721"],
    },
    directiveIncomeAmount: {
      type: Number,
      default: 0,
    },
    taxDeductAmount: {
      type: Number,
      default: 0,
    },
    amount: {
      type: Number,
      default: 0,
    },
  },
  restraintOfTrade: { type: Number, default: 0 },
  broadBasedEmployeeSharePlan: { type: Number, default: 0 },
  uniformAllowance: {
    type: Number,
    default: 0,
  },
  computerAllowance: { type: Number, default: 0 },
  repaymentOfAdvance: { type: Number, default: 0 },
  relocationAllowance: {
    taxableAmount: { type: Number, default: 0 },
    nonTaxableAmount: { type: Number, default: 0 },
    taxableItemsPaidByEmployer: { type: Number, default: 0 },
  },
  employeeDebtBenefit: {
    amount: {
      type: Number,
      default: 0,
    },
    date: {
      type: Date,
    },
    enabled: {
      type: Boolean,
      default: false,
    },
  },
  subsistenceAllowanceInternational: {
    totalPaidToEmployee: { type: Number, default: 0 },
    maximumDailyDeemedAmount: { type: Number, default: 0 },
    numberOfDays: Number,
  },
  subsistenceAllowanceLocal: {
    costsForReimbursement: {
      type: String,
      enum: ["incidental_only", "meals_and_incidental"],
    },
    fullAmountPaidToEmployee: {
      type: Number,
      default: 0,
      min: [0, "Amount paid cannot be negative"],
    },
    numberOfDays: {
      type: Number,
      default: 0,
      min: [0, "Number of days cannot be negative"],
    },
  },
  covid19DisasterRelief: {
    amount: { type: Number, default: 0 },
    affectsWage: Boolean,
  },
  terminationLumpSums: {
    directiveNumber: String,
    directiveIssueDate: Date,
    directiveIncomeSourceCode: String,
    amountOfTaxToDeduct: { type: Number, default: 0 },
    directiveIncomeAmount: { type: Number, default: 0 },
  },
  payRun: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "PayRun",
  },
  payruns: [{ type: mongoose.Schema.Types.ObjectId, ref: "Payrun" }],
  payPeriods: [
    {
      startDate: Date,
      endDate: Date,
      isFinalized: {
        type: Boolean,
        default: false,
      },
      isPartial: Boolean,
      basicSalary: Number,
      paye: Number,
      uif: Number,
      total: Number,
    },
  ],
  status: {
    type: String,
    enum: ["draft", "processing", "finalized", "locked"],
    default: "draft",
  },
  payComponents: [
    {
      componentId: {
        type: Schema.Types.ObjectId,
        ref: "PayComponent",
        required: true,
      },
      amount: {
        type: Number,
        required: true,
        validate: {
          validator: async function (value) {
            const component = await mongoose
              .model("PayComponent")
              .findById(this.componentId);
            if (!component) return false;

            const validation = component.validateAmount(
              value,
              this.parent().basicSalary
            );
            return validation.isValid;
          },
          message: (props) => `Invalid amount for component`,
        },
      },
      effectiveDate: {
        type: Date,
        required: true,
      },
      endDate: Date,
      status: {
        type: String,
        enum: ["active", "suspended", "terminated"],
        default: "active",
      },
      metadata: {
        lastUpdated: Date,
        updatedBy: {
          type: Schema.Types.ObjectId,
          ref: "User",
        },
      },
    },
  ],
  calculations: {
    grossIncome: {
      type: Number,
      default: 0,
    },
    totalDeductions: {
      type: Number,
      default: 0,
    },
    taxableIncome: {
      type: Number,
      default: 0,
    },
    netPay: {
      type: Number,
      default: 0,
    },
    periodTax: {
      type: Number,
      default: 0,
    },
  },
  validationState: {
    isValid: {
      type: Boolean,
      default: false,
    },
    errors: [
      {
        code: String,
        message: String,
        component: String,
        timestamp: {
          type: Date,
          default: Date.now,
        },
      },
    ],
    warnings: [
      {
        code: String,
        message: String,
        component: String,
        timestamp: Date,
      },
    ],
    lastValidated: Date,
  },
  processingMetadata: {
    lastCalculated: Date,
    calculatedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
    },
    version: String,
    changes: [
      {
        field: String,
        oldValue: Schema.Types.Mixed,
        newValue: Schema.Types.Mixed,
        timestamp: Date,
        user: {
          type: Schema.Types.ObjectId,
          ref: "User",
        },
      },
    ],
  },
  employeeContribution: {
    type: Number,
    default: 0,
  },
  employerContribution: {
    type: Number,
    default: 0,
  },
  covidRelief: {
    amount: {
      type: Number,
      default: 0,
    },
    affectsWage: {
      type: Boolean,
      default: false,
    },
    date: {
      type: Date,
    },
    enabled: {
      type: Boolean,
      default: false,
    },
  },
});

payrollSchema.add({
  oidEarnings: { type: Number, default: 0 },
  oidExcludedEarnings: [
    {
      type: { type: String },
      amount: { type: Number },
    },
  ],
  // Custom income items from the custom income feature
  customIncomeItems: [
    {
      customIncomeId: {
        type: Schema.Types.ObjectId,
        ref: "CustomItem",
        required: true
      },
      name: {
        type: String,
        required: true
      },
      inputType: {
        type: String,
        required: true
      },
      amount: Number,
      percentage: Number,
      quantity: Number,
      hoursWorkedFactor: Number,
      customRate: Number,
      monthlyAmount: Number,
      incomeItems: [String],
      enableProRata: {
        type: Boolean,
        default: false
      },
      differentRateForEveryEmployee: {
        type: Boolean,
        default: false
      },
      calculatedAmount: {
        type: Number,
        default: 0
      },
      createdAt: {
        type: Date,
        default: Date.now
      }
    }
  ],
});

payrollSchema.methods.calculateOIDEarnings = function () {
  let totalEarnings = this.totalEarnings || 0;

  let oidEarnings = totalEarnings;

  if (Array.isArray(this.oidExcludedEarnings)) {
    this.oidExcludedEarnings.forEach((exclusion) => {
      if (exclusion && typeof exclusion.amount === "number") {
        oidEarnings -= exclusion.amount;
      }
    });
  }

  // Ensure we don't set NaN
  this.oidEarnings = Math.max(oidEarnings || 0, 0);
};

// Add method to remove medical aid
payrollSchema.methods.removeMedicalAid = async function() {
  // Reset medical aid fields to default values
  this.medical = {
    medicalAid: 0,
    members: 0,
    employerContribution: 0,
    employeeHandlesPayment: false,
    dontApplyTaxCredits: false
  };

  // Save the changes
  await this.save();
  return this;
};

payrollSchema.pre("save", function (next) {
  this.calculateOIDEarnings();
  next();
});

payrollSchema.index({ employee: 1, company: 1, month: 1 }, { unique: true });
payrollSchema.index({ status: 1 });
payrollSchema.index({ "payComponents.effectiveDate": 1 });

const Payroll =
  mongoose.models.Payroll || mongoose.model("Payroll", payrollSchema);
module.exports = Payroll;
