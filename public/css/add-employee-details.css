/* Reset base layout styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Layout structure */
.layout-wrapper {
  display: flex;
  min-height: 100vh;
  width: 100%;
}

.content-wrapper {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
}

/* Main container positioning */
main {
  flex: 1;
  margin-left: 260px;
  margin-top: 80px;
  padding: 24px;
  width: calc(100vw - 280px);
  min-height: calc(100vh - 80px);
  transition: all 0.3s ease;
  position: relative;
  background: #f8fafc;
}

/* Title Section Styling */
.title-section {
  background: linear-gradient(
    135deg,
    rgba(10, 15, 28, 0.03),
    rgba(20, 27, 45, 0.05)
  );
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.03);
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.banner-header {
  color: #0f172a;
}

.banner-header h1 {
  font-size: 1.875rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #0f172a;
  background: linear-gradient(to right, #0f172a, #334155);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.description {
  color: #64748b;
  font-size: 0.975rem;
  max-width: 600px;
}

.company-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
  padding: 8px 16px;
  background: rgba(59, 130, 246, 0.08);
  border-radius: 100px;
  color: #3b82f6;
  font-size: 0.875rem;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

/* Tabs Section */
#tabs-section {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  padding: 4px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.tab-button {
  padding: 12px 20px;
  border: none;
  background: transparent;
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tab-button:hover {
  color: #8B5CF6;
  background: rgba(139, 92, 246, 0.08);
}

.tab-button.active {
  background: rgba(139, 92, 246, 0.08);
  color: #8B5CF6;
  font-weight: 600;
}

/* Content Section */
#content-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Filters Section */
.filters-section {
  display: flex;
  gap: 16px;
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
}

#statusFilter {
  width: 200px;
  min-width: auto;
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #f8fafc;
  color: #0f172a;
  font-size: 0.875rem;
}

#searchInputEmployee {
  width: 300px;
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #f8fafc;
  color: #0f172a;
  font-size: 0.875rem;
}

/* Table Styling */
.table-container {
  padding: 16px;
  overflow-x: auto;
}

.my-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.my-table th {
  padding: 12px 16px;
  background: #f8fafc;
  color: #0f172a;
  font-weight: 500;
  text-align: left;
  border-bottom: 2px solid #e5e7eb;
}

.my-table td {
  padding: 12px 16px;
  color: #334155;
  border-bottom: 1px solid #e5e7eb;
}

.my-table tr:hover {
  background: #f8fafc;
}

/* Status Badges */
td[data-status] {
  font-weight: 500;
}

td[data-status="Active"] {
  color: #10b981;
}

td[data-status="Inactive"] {
  color: #ef4444;
}

/* Pagination */
.pagination-info {
  padding: 16px;
  color: #64748b;
  font-size: 0.875rem;
}

.pagination-buttons {
  display: flex;
  justify-content: space-between;
  padding: 16px;
  border-top: 1px solid #e5e7eb;
}

.pagination-buttons button {
  padding: 8px 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  color: #0f172a;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-buttons button:hover {
  background: #f8fafc;
  border-color: #3b9eff;
  color: #3b9eff;
}

/* Adjust for collapsed sidebar */
.sidebar.collapsed ~ .content-wrapper main {
  margin-left: 64px;
  width: calc(100vw - 64px);
}

/* Responsive adjustments - Enhanced */
@media screen and (max-width: 1400px) {
  .main-container {
    padding: 1.5rem;
  }

  .form-grid {
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  }
}

@media screen and (max-width: 1024px) {
  .main-container {
    padding: 1rem;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .action-tabs {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
  }

  .action-tabs::-webkit-scrollbar {
    display: none;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .step-indicator {
    padding: 1rem 1rem 2.5rem 1rem; /* Adjusted for mobile */
    min-height: 100px;
    justify-content: space-around; /* Better distribution on mobile */
  }

  .step-indicator::before {
    top: calc(1rem + 1.25rem); /* Adjust for smaller step numbers */
    left: 20%;
    right: 20%;
  }

  .step-number {
    width: 2.5rem;
    height: 2.5rem;
  }

  .step-label {
    font-size: 0.7rem;
    min-width: 60px;
    max-width: 90px;
  }
}

@media screen and (max-width: 992px) {
  .step-indicator {
    padding: 1.25rem 1.5rem 2.75rem 1.5rem;
    min-height: 110px;
  }

  .step-indicator::before {
    left: 22%;
    right: 22%;
  }

  .step-label {
    font-size: 0.7rem;
    min-width: 70px;
    max-width: 100px;
  }
}

@media screen and (max-width: 768px) {
  .employee-section {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .form-section {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .step-indicator {
    padding: 1rem 0.75rem 2.5rem 0.75rem;
    min-height: 95px;
    margin: 0 -0.5rem 1rem -0.5rem;
  }

  .step-indicator::before {
    left: 18%;
    right: 18%;
  }

  .step-number {
    width: 2.25rem;
    height: 2.25rem;
    font-size: 0.8rem;
  }

  .step-label {
    font-size: 0.65rem;
    min-width: 45px;
    max-width: 75px;
    top: calc(100% + 0.4rem);
  }

  .form-actions {
    position: static;
    margin-top: 2rem;
    padding: 1rem 0;
    background: transparent;
    box-shadow: none;
    border-top: 1px solid var(--border-color);
  }

  .form-actions > div {
    flex-direction: column;
    gap: 0.75rem;
  }

  .form-actions button {
    width: 100%;
    padding: 1rem;
    font-size: 1rem;
  }

  /* Banking Details Responsive */
  .banking-details-section {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .payment-method-group,
  .banking-details-container {
    padding: 1rem;
  }

  .bank-selection-group,
  .account-details-group {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 1rem;
  }

  .banking-info-header {
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
  }

  .banking-info-header h5 {
    font-size: 1rem;
  }

  /* Identification Section Responsive */
  .identification-section {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .identification-container {
    gap: 1rem;
  }

  .id-type-group,
  .id-field-group,
  .tax-number-group {
    padding: 1rem;
  }

  .identification-section .section-title {
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
  }

  .identification-section .section-title h4 {
    font-size: 1.125rem;
  }

  .id-fields-container {
    min-height: 100px;
  }

  /* Pay Details and Regular Hours Responsive */
  .pay-details-section,
  .regular-hours-section {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .pay-details-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .pay-frequency-group,
  .appointment-date-group,
  .payment-type-group,
  .working-days-section,
  .days-calculation-section {
    padding: 1rem;
  }

  .hours-config-group {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 1rem;
  }

  .pay-details-section .section-title,
  .regular-hours-section .section-title {
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
  }

  .pay-details-section .section-title h4,
  .regular-hours-section .section-title h4 {
    font-size: 1.125rem;
  }

  /* Position Details Responsive */
  .position-details-section {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .position-details-container {
    gap: 1rem;
  }

  .job-title-group,
  .department-group {
    padding: 1rem;
  }

  .department-group {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .position-details-section .section-title {
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
  }

  .position-details-section .section-title h4 {
    font-size: 1.125rem;
  }
}

/* Extra small screens */
@media screen and (max-width: 480px) {
  .step-indicator {
    padding: 0.75rem 0.5rem 2.25rem 0.5rem;
    min-height: 85px;
    gap: 0.25rem;
  }

  .step-indicator::before {
    left: 15%;
    right: 15%;
  }

  .step-number {
    width: 2rem;
    height: 2rem;
    font-size: 0.75rem;
  }

  .step-label {
    font-size: 0.6rem;
    min-width: 40px;
    max-width: 65px;
    top: calc(100% + 0.3rem);
    line-height: 1.2;
  }

  /* Handle very long labels by allowing them to wrap on 2 lines max */
  .step-label {
    white-space: normal;
    word-break: break-word;
    max-height: 2.4em; /* Approximately 2 lines */
    overflow: hidden;
  }
}

/* Use the same variables as onboarding */
:root {
  --primary-color: #6366f1;
  --secondary-color: #818cf8;
  --success-color: #22c55e;
  --danger-color: #ef4444;
  --warning-color: #f59e0b;
  --background-color: #f8fafc;
  --card-background: #ffffff;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --border-color: #e2e8f0;
}

/* Page Layout */
.main-container {
  padding: 2rem;
  background: var(--background-color);
}

/* Page Header - Modernized */
.page-header {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color);
  font-family: 'Inter', sans-serif;
}

.header-content h1 {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-family: 'Inter', sans-serif;
  letter-spacing: -0.025em;
}

.description {
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-family: 'Inter', sans-serif;
  line-height: 1.5;
  max-width: 600px;
}

.company-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: rgba(99, 102, 241, 0.08);
  border-radius: 8px;
  color: var(--primary-color);
  font-size: 0.875rem;
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  border: 1px solid rgba(99, 102, 241, 0.1);
}

/* Action Tabs - Modernized */
.action-tabs {
  display: flex;
  gap: 1rem;
  margin-top: 6.5rem; /* Add space below the header */
  margin-bottom: 2rem;
  background: white;
  padding: 1rem;
  border-radius: 0.75rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 1001; /* Higher than header z-index to ensure visibility */
  font-family: 'Inter', sans-serif;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  background: transparent;
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Inter', sans-serif;
  white-space: nowrap;
}

.tab-button:hover {
  background: rgba(139, 92, 246, 0.08);
  color: #8B5CF6;
}

.tab-button.active {
  background: rgba(139, 92, 246, 0.08);
  color: #8B5CF6;
  font-weight: 600;
}

.tab-button i {
  font-size: 1rem;
}

/* Filters Section */
.filters-section {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  background: white;
  padding: 1.5rem;
  border-radius: 0.75rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.filter-group {
  flex: 1;
}

.filter-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.filter-select,
.search-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-primary);
  transition: all 0.3s ease;
}

.filter-select:focus,
.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* Table Styles */
.table-container {
  background: white;
  border-radius: 0.75rem;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow-x: auto;
}

.modern-table {
  width: 100%;
  border-collapse: collapse;
}

.modern-table th {
  background: var(--background-color);
  padding: 1rem;
  text-align: left;
  font-weight: 500;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.modern-table td {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
  font-size: 0.875rem;
}

.employee-name {
  font-weight: 500;
  color: var(--primary-color);
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge.active {
  background: rgba(34, 197, 94, 0.1);
  color: var(--success-color);
}

.status-badge.inactive {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
  padding: 1rem;
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.pagination-info {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.pagination-buttons {
  display: flex;
  gap: 1rem;
}

.pagination-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  background: white;
  color: var(--text-primary);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pagination-button:hover {
  background: var(--background-color);
  border-color: var(--primary-color);
}

/* Step Indicator Styles - Fixed Overlapping Issue */
.step-indicator {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  position: relative;
  padding: 1.5rem 2rem 3rem 2rem; /* Added bottom padding for labels */
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color);
  min-height: 120px; /* Ensure enough height for labels */
}

.step-indicator::before {
  content: '';
  position: absolute;
  top: calc(1.5rem + 1.5rem); /* Align with step number centers */
  left: 25%;
  right: 25%;
  height: 2px;
  background: var(--border-color);
  z-index: 1;
  transition: background 0.3s ease;
}

/* Progress line states - can be controlled by JavaScript */
.step-indicator[data-current-step="1"]::before {
  background: linear-gradient(to right, var(--border-color) 0%, var(--border-color) 100%);
}

.step-indicator[data-current-step="2"]::before {
  background: linear-gradient(to right, var(--primary-color) 0%, var(--primary-color) 50%, var(--border-color) 50%, var(--border-color) 100%);
}

.step-indicator[data-current-step="3"]::before {
  background: linear-gradient(to right, var(--primary-color) 0%, var(--primary-color) 100%);
}

.step-number {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: #ffffff;
  border: 2px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
  color: var(--text-secondary);
  font-family: 'Inter', sans-serif;
  font-size: 0.875rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-direction: column;
  flex-shrink: 0;
}

.step-number.active {
  border-color: var(--primary-color);
  background: var(--primary-color);
  color: #ffffff;
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(99, 102, 241, 0.3);
}

.step-number.completed {
  border-color: var(--primary-color);
  background: var(--primary-color);
  color: #ffffff;
  box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);
}

.step-number .check-mark {
  font-size: 1rem;
  color: #ffffff;
}

.step-label {
  position: absolute;
  top: calc(100% + 0.75rem);
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
  font-size: 0.75rem;
  color: var(--text-secondary);
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  text-align: center;
  min-width: 80px; /* Ensure minimum width for proper spacing */
  max-width: 120px; /* Prevent labels from getting too wide */
}

.step-number.active .step-label {
  color: var(--primary-color);
  font-weight: 600;
}

.step-number.completed .step-label {
  color: var(--primary-color);
  font-weight: 600;
}

/* Form Navigation Buttons - Modernized */
.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
  padding: 1.5rem 0;
  border-top: 1px solid var(--border-color);
}

.form-actions > div {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.form-actions.single-button {
  justify-content: flex-end;
}

.form-actions button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-width: 140px;
  padding: 0.875rem 1.75rem;
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 1.25rem;
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.btn-secondary,
.form-actions .prev-step {
  color: var(--text-primary);
  background-color: #ffffff;
  border: 1px solid var(--border-color);
}

.btn-secondary:hover,
.form-actions .prev-step:hover {
  background-color: var(--background-color);
  border-color: var(--text-secondary);
  transform: translateY(-1px);
}

.btn-primary,
.form-actions .next-step,
.form-actions button[type="submit"] {
  color: #ffffff;
  background-color: var(--primary-color);
  border: 1px solid var(--primary-color);
  box-shadow: 0 1px 2px rgba(99, 102, 241, 0.2);
}

.btn-primary:hover,
.form-actions .next-step:hover,
.form-actions button[type="submit"]:hover {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(99, 102, 241, 0.3);
}

.form-actions button i {
  font-size: 1rem;
}

.form-actions button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
  transform: none;
}

/* Form Section Styling - Modernized */
.employee-section {
  position: relative;
  z-index: 10;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color);
  padding: 2rem;
  margin-bottom: 1.5rem;
  width: 100%;
  font-family: 'Inter', sans-serif;
}

.employee-section form {
  position: relative;
  z-index: 11;
}

.form-section {
  background: rgba(248, 250, 252, 0.5);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  width: 100%;
  border: 1px solid rgba(226, 232, 240, 0.5);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
  font-family: 'Inter', sans-serif;
}

.section-header i {
  color: var(--primary-color);
  font-size: 1.25rem;
}

.section-header h3 {
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
  font-family: 'Inter', sans-serif;
  letter-spacing: -0.025em;
}

.subsection-header {
  color: var(--text-secondary);
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 1rem;
  font-family: 'Inter', sans-serif;
}

/* Form Grid and Controls - Modernized */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.form-group {
  margin-bottom: 1.25rem;
  opacity: 1;
  visibility: visible;
}

.form-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
  font-weight: 500;
  font-size: 0.875rem;
  font-family: 'Inter', sans-serif;
}

.form-group label i {
  color: var(--primary-color);
  font-size: 1rem;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 0.875rem;
  color: var(--text-primary);
  background: white;
  transition: all 0.2s ease;
  font-family: 'Inter', sans-serif;
}

.form-group input:focus,
.form-group select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  outline: none;
}

.form-group input::placeholder {
  color: #94a3b8;
  font-family: 'Inter', sans-serif;
}

/* Checkbox Styling */
.checkbox-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0.5rem 0;
}

.checkbox-group input[type="checkbox"] {
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 0.25rem;
  border: 2px solid var(--border-color);
  cursor: pointer;
  transition: all 0.2s ease;
}

.checkbox-group input[type="checkbox"]:checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

/* Help Text */
.help-text {
  display: block;
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

/* Form Navigation */
.form-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
  padding: 1rem 0;
  border-top: 1px solid #e2e8f0;
}

.form-navigation.single-button {
  justify-content: flex-end;
}

.form-navigation button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-width: 120px;
  padding: 0.75rem 1.5rem;
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 1.25rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

.form-navigation .prev-step {
  margin-right: auto;
  color: #1e293b;
  background-color: #ffffff;
  border: 1px solid #e2e8f0;
}

.form-navigation .prev-step:hover {
  background-color: #f8fafc;
  border-color: #64748b;
}

.form-navigation .next-step,
.form-navigation button[type="submit"] {
  color: #ffffff;
  background-color: #6366f1;
  border: 1px solid #6366f1;
}

.form-navigation .next-step:hover,
.form-navigation button[type="submit"]:hover {
  background-color: #818cf8;
  border-color: #818cf8;
  transform: translateY(-1px);
}

.form-navigation button i {
  font-size: 1.25rem;
}

.form-navigation button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Preview Section */
.preview-section {
  position: sticky;
  top: 2rem;
}

.preview-card {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Working Days Grid - Enhanced */
.working-days-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.day-row {
  background: white;
  padding: 1rem;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  transition: all 0.2s ease;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.day-row:hover {
  border-color: var(--primary-color);
  background: rgba(99, 102, 241, 0.02);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Working Days Specific Checkbox Styling */
.working-days-section .modern-checkbox {
  margin: 0;
  width: 100%;
}

.working-days-section .checkbox-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  cursor: pointer;
  font-family: 'Inter', sans-serif;
  width: 100%;
  min-height: 44px; /* Minimum touch target */
}

.working-days-section .checkbox-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
  font-family: 'Inter', sans-serif;
  text-align: center;
  flex: 1;
  order: 2;
}

.working-days-section .checkmark {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid var(--border-color);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  background: white;
  flex-shrink: 0;
  order: 1;
}

.working-days-section .checkbox-wrapper input[type="checkbox"] {
  display: none;
}

.working-days-section .checkbox-wrapper input[type="checkbox"]:checked + .checkbox-label + .checkmark {
  background: var(--primary-color);
  border-color: var(--primary-color);
  box-shadow: 0 1px 2px rgba(99, 102, 241, 0.2);
}

.working-days-section .checkbox-wrapper input[type="checkbox"]:checked + .checkbox-label + .checkmark::after {
  content: "✓";
  color: white;
  font-size: 0.875rem;
  font-weight: 600;
}

/* Responsive Working Days Grid */
@media (max-width: 1024px) {
  .working-days-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.875rem;
  }
}

@media (max-width: 768px) {
  .working-days-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .day-row {
    padding: 0.875rem;
    min-height: 55px;
  }
}

@media (max-width: 480px) {
  .working-days-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .day-row {
    padding: 1rem;
    min-height: 50px;
  }

  .working-days-section .checkbox-wrapper {
    justify-content: flex-start;
    gap: 0.75rem;
  }

  .working-days-section .checkbox-label {
    text-align: left;
  }
}

/* Modern Form Section Styling - Enhanced */
.section-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  font-family: 'Inter', sans-serif;
}

.section-title i {
  font-size: 1.5rem;
  color: var(--primary-color);
}

.section-title h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  font-family: 'Inter', sans-serif;
  letter-spacing: -0.025em;
}

/* Modern Input Styling - Enhanced */
.input-wrapper {
  position: relative;
  width: 100%;
}

.input-wrapper input {
  padding-right: 2.5rem;
  font-family: 'Inter', sans-serif;
}

.input-icon {
  position: relative;
  width: 100%;
}

.input-icon input {
  padding-left: 2.5rem;
  font-family: 'Inter', sans-serif;
}

.input-icon i {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--primary-color);
  font-size: 1rem;
  pointer-events: none;
}

.input-hint {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  cursor: help;
}

.input-hint:hover .hint-text {
  opacity: 1;
  visibility: visible;
}

.hint-text {
  position: absolute;
  right: 0;
  top: calc(100% + 5px);
  background: white;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  z-index: 10;
  font-family: 'Inter', sans-serif;
}

.form-hint {
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
  font-family: 'Inter', sans-serif;
}

/* Modern Checkbox Styling - Enhanced */
.modern-checkbox {
  margin: 1rem 0;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  font-family: 'Inter', sans-serif;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-primary);
  font-weight: 500;
  font-family: 'Inter', sans-serif;
}

.checkbox-wrapper input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid var(--border-color);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  background: white;
}

.checkbox-wrapper
  input[type="checkbox"]:checked
  + .checkbox-label
  + .checkmark {
  background: var(--primary-color);
  border-color: var(--primary-color);
  box-shadow: 0 1px 2px rgba(99, 102, 241, 0.2);
}

.checkbox-wrapper
  input[type="checkbox"]:checked
  + .checkbox-label
  + .checkmark::after {
  content: "✓";
  color: white;
  font-size: 0.875rem;
  font-weight: 600;
}

/* Modern Select Styling - Enhanced */
select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236366f1'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1rem;
  padding-right: 2.5rem;
  cursor: pointer;
  font-family: 'Inter', sans-serif;
}

.select-wrapper {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
}

.select-wrapper select {
  width: 100%;
  padding-right: 40px;
  appearance: none;
}

.select-wrapper .ph-caret-down {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--primary-color);
  pointer-events: none;
  font-size: 1rem;
}

/* Update form navigation */
.btn-next {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  font-weight: 500;
  padding: 0.875rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s;
  border: none;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.btn-next:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
}

/* Add ripple effect */
.btn-next::after {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  background-image: radial-gradient(circle, #fff 10%, transparent 10.01%);
  background-repeat: no-repeat;
  background-position: 50%;
  transform: scale(10, 10);
  opacity: 0;
  transition: transform 0.5s, opacity 1s;
}

.btn-next:active::after {
  transform: scale(0, 0);
  opacity: 0.3;
  transition: 0s;
}

/* Update the form step visibility */
.form-step {
  display: none; /* Hidden by default */
}

.form-step.active {
  display: block; /* Show when active */
}

/* Update the employee section container */
.employee-section {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 2rem;
  width: 100%; /* Ensure full width */
}

/* Update the grid layout */
.employee-sections-grid {
  display: block; /* Change from grid to block since we removed the preview section */
  width: 100%;
}

/* Form section styling */
.form-section {
  background: var(--background-color);
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  width: 100%;
}

/* Ensure form groups are visible */
.form-group {
  margin-bottom: 1.25rem;
  opacity: 1;
  visibility: visible;
}

/* Update container width */
.employee-container {
  width: 100%;
  /* Removed max-width constraint for consistency with other pages */
}

/* Make sure form elements are visible */
.form-step[data-step="1"].active,
.form-step[data-step="2"].active,
.form-step[data-step="3"].active {
  display: block;
  opacity: 1;
  visibility: visible;
}

/* Form Step Visibility */
.form-step {
  display: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.form-step.active {
  display: block;
  opacity: 1;
}

/* Container Layout */
.employee-sections-grid {
  width: 100%;
  /* Removed max-width constraint for full-width consistency */
}

.employee-section {
  width: 100%;
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Form Section Layout */
.form-section {
  margin-bottom: 2rem;
  background: var(--background-color);
  border-radius: 0.75rem;
  padding: 1.5rem;
}

/* Ensure Form Groups are Visible */
.form-group {
  display: block;
  margin-bottom: 1.5rem;
}

/* Add !important to force visibility for debugging */
.form-step.active {
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.form-group {
  display: block !important;
  margin-bottom: 1.5rem !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Form Step Visibility - Add more specific selectors */
.employee-section .form-step {
  display: none !important;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.employee-section .form-step.active {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Form Groups - Make more specific */
.employee-section .form-step.active .form-group {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  margin-bottom: 1.5rem;
}

/* Form Section - Ensure visibility */
.employee-section .form-step.active .form-section {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  background: var(--background-color);
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

/* Form Step Transitions */
.form-step {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.form-step.active {
  opacity: 1;
}

/* Form Validation Styles */
.error-message {
  color: #dc3545;
  font-size: 12px;
  margin-top: 4px;
  display: block;
}

.form-text {
  color: #6c757d;
  font-size: 12px;
  margin-top: 4px;
  display: block;
}

.required {
  color: #dc3545;
  margin-left: 2px;
}

/* Date input specific styles */
input[type="date"].flatpickr {
  background-color: #fff;
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 8px 12px;
  width: 100%;
  font-size: 14px;
}

input[type="date"].flatpickr:invalid {
  border-color: #dc3545;
}

.input-wrapper {
  position: relative;
  width: 100%;
}

.input-wrapper small {
  position: absolute;
  bottom: -20px;
  left: 0;
}

/* Add validation styles */
.form-group input:required {
  border-left: 3px solid var(--primary-color);
}

.form-group input:required:invalid {
  border-left: 3px solid var(--danger-color);
}

/* Bank Details Styling */
.input-wrapper {
  position: relative;
}

.input-wrapper select,
.input-wrapper input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  color: #1e293b;
  background: #ffffff;
  transition: all 0.2s ease;
}

.input-wrapper select {
  appearance: none;
  padding-right: 2.5rem;
  cursor: pointer;
}

.input-wrapper .ph {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #64748b;
  pointer-events: none;
}

.input-hint {
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: #64748b;
}

/* Auto-populated branch code styling */
.auto-populated {
  background-color: #f8fafc !important;
  color: #64748b !important;
  border-color: #e2e8f0 !important;
}

.auto-populated + .input-hint small {
  color: #6366f1;
}

/* Form grid for banking section */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

/* Submit Button and Auto-Populated Fields */
.submit-button {
  background-color: #6366f1;
  color: white;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.375rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.submit-button:hover {
  background-color: #4f46e5;
}

.submit-button i {
  font-size: 1.25rem;
}

.auto-populated {
  background-color: #f8fafc;
  color: #64748b;
  cursor: not-allowed;
}

/* Add these styles for smooth transitions */
#idNumberContainer,
#passportNumberContainer {
  transition: all 0.3s ease;
  opacity: 1;
  max-height: 100px;
  overflow: hidden;
}

#idNumberContainer[style*="display: none"],
#passportNumberContainer[style*="display: none"] {
  opacity: 0;
  max-height: 0;
  margin: 0;
  padding: 0;
}

/* Flatpickr Modern Theme */
.flatpickr-calendar {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color);
  font-family: inherit;
}

.flatpickr-day {
  border-radius: 0.5rem;
}

.flatpickr-day.selected {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.flatpickr-day:hover {
  background: var(--background-color);
}

.flatpickr-months .flatpickr-month {
  background: var(--primary-color);
  color: white;
  border-radius: 0.75rem 0.75rem 0 0;
}

.flatpickr-current-month {
  font-size: 1rem;
  padding: 0.75rem 0;
}

.flatpickr-monthDropdown-months {
  background: var(--primary-color);
  color: white;
}

.flatpickr-weekday {
  color: var(--text-secondary);
}

/* Select wrapper with settings link */
.select-wrapper {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
}

.select-wrapper select {
  width: 100%;
  padding-right: 40px; /* Reduced padding since we removed the caret */
  appearance: none;
}

.select-icons {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  padding-right: 10px;
}

.select-icons .settings-link {
  color: #666;
  text-decoration: none;
  display: flex;
  align-items: center;
  position: relative;
}

.select-icons .settings-link:hover {
  color: #333;
}

/* Tooltip styling */
[data-tooltip] {
  position: relative;
}

[data-tooltip]:before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  right: 0;
  background: #333;
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 1000;
  margin-bottom: 5px;
}

[data-tooltip]:hover:before {
  visibility: visible;
  opacity: 1;
}

/* Add arrow to tooltip */
[data-tooltip]:after {
  content: '';
  position: absolute;
  bottom: 100%;
  right: 10px;
  margin-bottom: -5px;
  border: 5px solid transparent;
  border-top-color: #333;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.2s ease;
}

[data-tooltip]:hover:after {
  visibility: visible;
  opacity: 1;
}

/* Toggle Container Styling */
.toggle-container {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  margin-bottom: 1rem;
}

.toggle-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
  font-family: 'Inter', sans-serif;
}

.settings-link {
  color: var(--primary-color);
  text-decoration: none;
  display: flex;
  align-items: center;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.settings-link:hover {
  background: rgba(99, 102, 241, 0.08);
  color: var(--secondary-color);
}

/* Info Message Styling */
.info-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: rgba(59, 130, 246, 0.08);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 6px;
  color: var(--primary-color);
  font-size: 0.875rem;
  font-family: 'Inter', sans-serif;
  margin-top: 1rem;
}

.info-message i {
  font-size: 1rem;
  color: var(--primary-color);
}

/* Banking Details Section - Enhanced Organization */
.banking-details-section {
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(255, 255, 255, 0.9) 100%);
  border: 1px solid rgba(99, 102, 241, 0.1);
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
}

.banking-details-section .section-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid rgba(99, 102, 241, 0.1);
}

.banking-details-section .section-title i {
  font-size: 1.5rem;
  color: var(--primary-color);
}

.banking-details-section .section-title h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  font-family: 'Inter', sans-serif;
  margin: 0;
}

/* Payment Method Group */
.payment-method-group {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: white;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Banking Details Container */
.banking-details-container {
  display: none; /* Hidden by default, shown when EFT is selected */
  background: white;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.banking-details-container.show {
  display: block;
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Banking Info Header */
.banking-info-header {
  margin-bottom: 2rem;
  text-align: center;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.banking-info-header h5 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  font-family: 'Inter', sans-serif;
  margin: 0 0 0.5rem 0;
}

.banking-info-header .info-text {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-family: 'Inter', sans-serif;
  margin: 0;
  line-height: 1.5;
}

/* Form Groups Organization */
.bank-selection-group,
.account-details-group,
.account-holder-group {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: rgba(248, 250, 252, 0.5);
  border-radius: 8px;
  border: 1px solid rgba(226, 232, 240, 0.5);
}

.bank-selection-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.account-details-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.account-holder-group {
  display: block;
}

/* Enhanced Form Hints */
.form-hint {
  display: block;
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
  font-family: 'Inter', sans-serif;
  line-height: 1.4;
}

.auto-hint {
  color: var(--primary-color);
  font-weight: 500;
}

/* Auto-populated Field Styling */
.auto-populated {
  background-color: rgba(248, 250, 252, 0.8) !important;
  color: var(--text-secondary) !important;
  border-color: rgba(226, 232, 240, 0.8) !important;
  cursor: not-allowed;
}

.auto-populated::placeholder {
  color: var(--primary-color);
  font-style: italic;
}

/* Enhanced Select Styling for Banking Section */
.banking-details-section .select-wrapper {
  position: relative;
}

.banking-details-section .select-wrapper select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236366f1'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1rem;
  padding-right: 2.5rem;
  cursor: pointer;
}

.banking-details-section .select-wrapper .ph-caret-down {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--primary-color);
  pointer-events: none;
  font-size: 1rem;
}

/* Focus States for Banking Fields */
.banking-details-section input:focus,
.banking-details-section select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  outline: none;
}

/* Required Field Indicators */
.banking-details-section label[for] {
  position: relative;
}

.banking-details-section label[for]:has(+ .select-wrapper select[required])::after,
.banking-details-section label[for]:has(+ .input-wrapper input[required])::after {
  content: '*';
  color: var(--danger-color);
  margin-left: 0.25rem;
  font-weight: 600;
}

/* Validation States */
.banking-details-section input:invalid:not(:placeholder-shown),
.banking-details-section select:invalid:not(:placeholder-shown) {
  border-color: var(--danger-color);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.banking-details-section input:valid:not(:placeholder-shown),
.banking-details-section select:valid:not(:placeholder-shown) {
  border-color: var(--success-color);
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

/* Loading State for Auto-populated Fields */
.auto-populated.loading {
  background-image: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Identification Section - Modernized Styling */
.identification-section {
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(255, 255, 255, 0.9) 100%);
  border: 1px solid rgba(99, 102, 241, 0.1);
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
}

.identification-section .section-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid rgba(99, 102, 241, 0.1);
}

.identification-section .section-title i {
  font-size: 1.5rem;
  color: var(--primary-color);
}

.identification-section .section-title h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  font-family: 'Inter', sans-serif;
  margin: 0;
}

/* Identification Container */
.identification-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* ID Type Group */
.id-type-group {
  padding: 1.5rem;
  background: white;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* ID Fields Container */
.id-fields-container {
  min-height: 120px; /* Reserve space for dynamic fields */
  position: relative;
}

.id-field-group {
  padding: 1.5rem;
  background: white;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(-10px);
}

.id-field-group[style*="block"] {
  opacity: 1;
  transform: translateY(0);
  animation: slideInDown 0.3s ease-out;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Tax Number Group */
.tax-number-group {
  padding: 1.5rem;
  background: rgba(248, 250, 252, 0.5);
  border-radius: 8px;
  border: 1px solid rgba(226, 232, 240, 0.5);
}

/* Enhanced Input Styling for Identification */
.identification-section .form-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
  font-weight: 500;
  font-size: 0.875rem;
  font-family: 'Inter', sans-serif;
}

.identification-section .form-group label i {
  color: var(--primary-color);
  font-size: 1rem;
}

.identification-section .input-icon {
  position: relative;
  width: 100%;
}

.identification-section .input-icon input {
  padding-left: 2.5rem;
  font-family: 'Inter', sans-serif;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.identification-section .input-icon i {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--primary-color);
  font-size: 1rem;
  pointer-events: none;
}

/* Focus States */
.identification-section input:focus,
.identification-section select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  outline: none;
}

/* Validation States */
.identification-section input:invalid:not(:placeholder-shown) {
  border-color: var(--danger-color);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.identification-section input:valid:not(:placeholder-shown) {
  border-color: var(--success-color);
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

/* Form Hints for Identification */
.identification-section .form-hint {
  display: block;
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
  font-family: 'Inter', sans-serif;
  line-height: 1.4;
}

/* Select Wrapper Styling */
.identification-section .select-wrapper {
  position: relative;
}

.identification-section .select-wrapper select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236366f1'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1rem;
  padding-right: 2.5rem;
  cursor: pointer;
  border-radius: 4px;
}

.identification-section .select-wrapper .ph-caret-down {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--primary-color);
  pointer-events: none;
  font-size: 1rem;
}

/* Pay Details Section - Modernized Styling */
.pay-details-section {
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(255, 255, 255, 0.9) 100%);
  border: 1px solid rgba(99, 102, 241, 0.1);
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
}

.pay-details-section .section-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid rgba(99, 102, 241, 0.1);
}

.pay-details-section .section-title i {
  font-size: 1.5rem;
  color: var(--primary-color);
}

.pay-details-section .section-title h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  font-family: 'Inter', sans-serif;
  margin: 0;
}

/* Pay Details Container */
.pay-details-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

/* Pay Frequency Group */
.pay-frequency-group,
.appointment-date-group {
  padding: 1.5rem;
  background: white;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Regular Hours Section - Modernized Styling */
.regular-hours-section {
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(255, 255, 255, 0.9) 100%);
  border: 1px solid rgba(99, 102, 241, 0.1);
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
}

.regular-hours-section .section-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid rgba(99, 102, 241, 0.1);
}

.regular-hours-section .section-title i {
  font-size: 1.5rem;
  color: var(--primary-color);
}

.regular-hours-section .section-title h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  font-family: 'Inter', sans-serif;
  margin: 0;
}

/* Regular Hours Container */
.regular-hours-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Payment Type Group */
.payment-type-group {
  padding: 1.5rem;
  background: white;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Payment Type Group Checkbox Styling */
.payment-type-group .modern-checkbox {
  margin: 0.75rem 0;
}

.payment-type-group .checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  font-family: 'Inter', sans-serif;
  min-height: 44px; /* Minimum touch target */
  padding: 0.5rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.payment-type-group .checkbox-wrapper:hover {
  background: rgba(99, 102, 241, 0.02);
}

.payment-type-group .checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-primary);
  font-weight: 500;
  font-family: 'Inter', sans-serif;
  font-size: 0.875rem;
  flex: 1;
}

.payment-type-group .checkbox-label i {
  color: var(--primary-color);
  font-size: 1rem;
}

.payment-type-group .checkmark {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid var(--border-color);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  background: white;
  flex-shrink: 0;
}

.payment-type-group .checkbox-wrapper input[type="checkbox"] {
  display: none;
}

.payment-type-group .checkbox-wrapper input[type="checkbox"]:checked + .checkbox-label + .checkmark {
  background: var(--primary-color);
  border-color: var(--primary-color);
  box-shadow: 0 1px 2px rgba(99, 102, 241, 0.2);
}

.payment-type-group .checkbox-wrapper input[type="checkbox"]:checked + .checkbox-label + .checkmark::after {
  content: "✓";
  color: white;
  font-size: 0.875rem;
  font-weight: 600;
}

/* Hours Configuration Group */
.hours-config-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  padding: 1.5rem;
  background: white;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Working Days Section */
.working-days-section {
  padding: 1.5rem;
  background: white;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.working-days-header {
  margin-bottom: 1.25rem;
}

.working-days-header label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
  font-family: 'Inter', sans-serif;
  margin-bottom: 0.5rem;
}

.working-days-header label i {
  color: var(--primary-color);
  font-size: 1rem;
}

.section-description {
  font-size: 0.75rem;
  color: var(--text-secondary);
  font-family: 'Inter', sans-serif;
  margin: 0;
  line-height: 1.4;
}

/* Days Calculation Section */
.days-calculation-section {
  padding: 1.5rem;
  background: white;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Enhanced Form Elements for Pay Details and Regular Hours */
.pay-details-section .form-group label,
.regular-hours-section .form-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
  font-weight: 500;
  font-size: 0.875rem;
  font-family: 'Inter', sans-serif;
}

.pay-details-section .form-group label i,
.regular-hours-section .form-group label i {
  color: var(--primary-color);
  font-size: 1rem;
}

.pay-details-section .input-icon,
.regular-hours-section .input-icon {
  position: relative;
  width: 100%;
}

.pay-details-section .input-icon input,
.regular-hours-section .input-icon input {
  padding-left: 2.5rem;
  font-family: 'Inter', sans-serif;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.pay-details-section .input-icon i,
.regular-hours-section .input-icon i {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--primary-color);
  font-size: 1rem;
  pointer-events: none;
}

/* Focus States */
.pay-details-section input:focus,
.pay-details-section select:focus,
.regular-hours-section input:focus,
.regular-hours-section select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  outline: none;
}

/* Form Hints */
.pay-details-section .form-hint,
.regular-hours-section .form-hint {
  display: block;
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
  font-family: 'Inter', sans-serif;
  line-height: 1.4;
}

.warning-hint {
  color: #f59e0b !important;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.warning-hint i {
  color: #f59e0b;
  font-size: 0.875rem;
}

/* Select Wrapper Styling */
.pay-details-section .select-wrapper,
.regular-hours-section .select-wrapper {
  position: relative;
}

.pay-details-section .select-wrapper select,
.regular-hours-section .select-wrapper select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236366f1'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1rem;
  padding-right: 2.5rem;
  cursor: pointer;
  border-radius: 4px;
}

.pay-details-section .select-wrapper .ph-caret-down,
.regular-hours-section .select-wrapper .ph-caret-down {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--primary-color);
  pointer-events: none;
  font-size: 1rem;
}

/* Enhanced Checkbox Styling for Regular Hours */
.regular-hours-section .modern-checkbox {
  margin: 0.5rem 0;
}

.regular-hours-section .checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  font-family: 'Inter', sans-serif;
}

.regular-hours-section .checkbox-wrapper input[type="checkbox"] {
  display: none;
}

.regular-hours-section .checkmark {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid var(--border-color);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  background: white;
}

.regular-hours-section .checkbox-wrapper input[type="checkbox"]:checked + .checkbox-label + .checkmark {
  background: var(--primary-color);
  border-color: var(--primary-color);
  box-shadow: 0 1px 2px rgba(99, 102, 241, 0.2);
}

.regular-hours-section .checkbox-wrapper input[type="checkbox"]:checked + .checkbox-label + .checkmark::after {
  content: "✓";
  color: white;
  font-size: 0.875rem;
  font-weight: 600;
}

/* Position Details Section - Modernized Styling */
.position-details-section {
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(255, 255, 255, 0.9) 100%);
  border: 1px solid rgba(99, 102, 241, 0.1);
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
}

.position-details-section .section-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid rgba(99, 102, 241, 0.1);
}

.position-details-section .section-title i {
  font-size: 1.5rem;
  color: var(--primary-color);
}

.position-details-section .section-title h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  font-family: 'Inter', sans-serif;
  margin: 0;
}

/* Position Details Container */
.position-details-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Job Title Group */
.job-title-group {
  padding: 1.5rem;
  background: white;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Department Group */
.department-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  padding: 1.5rem;
  background: rgba(248, 250, 252, 0.5);
  border-radius: 8px;
  border: 1px solid rgba(226, 232, 240, 0.5);
}

/* Enhanced Form Elements for Position Details */
.position-details-section .form-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
  font-weight: 500;
  font-size: 0.875rem;
  font-family: 'Inter', sans-serif;
}

.position-details-section .form-group label i {
  color: var(--primary-color);
  font-size: 1rem;
}

.position-details-section .input-icon {
  position: relative;
  width: 100%;
}

.position-details-section .input-icon input {
  padding-left: 2.5rem;
  font-family: 'Inter', sans-serif;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.position-details-section .input-icon i {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--primary-color);
  font-size: 1rem;
  pointer-events: none;
}

/* Focus States */
.position-details-section input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  outline: none;
}

/* Validation States */
.position-details-section input:invalid:not(:placeholder-shown) {
  border-color: var(--danger-color);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.position-details-section input:valid:not(:placeholder-shown) {
  border-color: var(--success-color);
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

/* Form Hints for Position Details */
.position-details-section .form-hint {
  display: block;
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
  font-family: 'Inter', sans-serif;
  line-height: 1.4;
}

/* Days Per Week Display */
.days-per-week-display {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  background: white;
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.calculated-days {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-family: 'Inter', sans-serif;
}

.calculated-days span {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--primary-color);
}

.calculated-days small {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.btn-override {
  padding: 0.5rem 0.75rem;
  background: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  color: var(--text-primary);
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'Inter', sans-serif;
}

.btn-override:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.override-input {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-cancel-override {
  padding: 0.25rem;
  background: var(--danger-color);
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}
