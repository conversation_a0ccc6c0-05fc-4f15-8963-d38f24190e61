/* Card Styles */

/* Onboarding Theme Variables */
:root {
  --primary-color: #6366f1;
  --secondary-color: #818cf8;
  --success-color: #22c55e;
  --background-color: #f8fafc;
  --card-background: #ffffff;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --border-color: #e2e8f0;
  --primary-blue: #3b82f6;
  --primary-blue-light: #60a5fa;
}

/* Base Styles */
body {
  font-family: "Inter", sans-serif;
  background-color: var(--background-color);
  color: var(--text-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  margin: 0;
  padding: 0;
}

.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 24px;
  margin-top: 24px;
}

/* Form Styles */
.modern-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.info-section {
  margin-bottom: 24px;
}

.info-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #f8fafc;
  border-radius: 8px;
  color: #64748b;
  font-size: 14px;
}

.info-badge i {
  color: #3b82f6;
}

/* Switch Styles */
.switch-container {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
}

.switch {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #e2e8f0;
  transition: .4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .4s;
}

input:checked + .slider {
  background-color: #3b82f6;
}

input:focus + .slider {
  box-shadow: 0 0 1px #3b82f6;
}

input:checked + .slider:before {
  transform: translateX(24px);
}

.slider.round {
  border-radius: 24px;
}

.slider.round:before {
  border-radius: 50%;
}

.switch-label {
  font-weight: 500;
  color: #1e293b;
}

/* Input Group Styles */
.input-group {
  margin-bottom: 24px;
}

.input-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #1e293b;
}

.input-with-icon {
  position: relative;
}

.input-with-icon i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #64748b;
}

.input-with-icon input {
  width: 100%;
  padding: 10px 12px 10px 36px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.input-with-icon input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* Change Messages Styles */
.change-messages {
  margin: 16px 0;
}

.message-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: #f8fafc;
  border-radius: 6px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #1e293b;
}

.message-item i {
  color: #3b82f6;
}

/* Checkbox Styles */
.checkbox-option {
  margin-bottom: 16px;
}

.checkbox-container {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  user-select: none;
}

.checkbox-container input {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid #e2e8f0;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.checkbox-container input:checked + .checkmark {
  background-color: #3b82f6;
  border-color: #3b82f6;
}

.checkbox-container input:checked + .checkmark::after {
  content: "";
  width: 6px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
  margin-bottom: 2px;
}

.nested-options {
  margin-left: 26px;
  margin-top: 12px;
  padding-left: 12px;
  border-left: 2px solid #e2e8f0;
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e2e8f0;
}

.button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.button.primary {
  background-color: #3b82f6;
  color: white;
  border: none;
}

.button.primary:hover {
  background-color: #2563eb;
  transform: translateY(-1px);
}

.button.secondary {
  background-color: #f1f5f9;
  color: #64748b;
  border: none;
}

.button.secondary:hover {
  background-color: #e2e8f0;
  transform: translateY(-1px);
}

/* Loading States */
.button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.button:disabled:hover {
  transform: none !important;
}

/* Loading Overlay */
.form-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: none;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  backdrop-filter: blur(2px);
}

.form-loading-overlay.show {
  display: flex;
}

.loading-content {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  max-width: 300px;
  width: 90%;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e2e8f0;
  border-top-color: var(--primary-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

.loading-text {
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.loading-subtext {
  color: var(--text-secondary);
  font-size: 14px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Button Loading State */
.button.loading {
  position: relative;
  pointer-events: none;
}

.button.loading .button-content {
  opacity: 0;
}

.button.loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Toast Notifications */
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.toast {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 16px 20px;
  min-width: 300px;
  max-width: 400px;
  display: flex;
  align-items: center;
  gap: 12px;
  transform: translateX(120%);
  transition: all 0.3s ease-in-out;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  line-height: 1.4;
  border-left: 4px solid #e2e8f0;
}

.toast.show {
  transform: translateX(0);
}

.toast i {
  font-size: 20px;
  flex-shrink: 0;
}

.toast-success {
  border-left-color: #10b981;
}

.toast-success i {
  color: #10b981;
}

.toast-error {
  border-left-color: #ef4444;
}

.toast-error i {
  color: #ef4444;
}

.toast-info {
  border-left-color: var(--primary-blue);
}

.toast-info i {
  color: var(--primary-blue);
}
