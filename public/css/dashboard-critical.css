/* Critical CSS for Dashboard - Above the Fold Only */
:root {
  --primary-color: #3b82f6;
  --secondary-color: #2563eb;
  --background-color: #f9fafb;
  --surface-color: #ffffff;
  --text-primary: #111827;
  --text-secondary: #4b5563;
  --border-color: #e5e7eb;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --space-4: 1rem;
  --space-6: 1.5rem;
  --radius-lg: 0.75rem;
}

/* Layout Structure - Critical */
.layout-wrapper {
  display: flex;
  min-height: 100vh;
  background: var(--background-color);
}

.content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-left: 280px;
}

.main-container {
  flex: 1;
  padding: var(--space-6);
  max-width: 100%;
  overflow-x: hidden;
}

/* Greeting Card - Above the Fold */
.greeting-card {
  background: var(--surface-color);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  margin-bottom: var(--space-6);
  border: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.greeting-text h1 {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.greeting-text p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 14px;
}

.wave {
  animation: wave 2s infinite;
  transform-origin: 70% 70%;
}

@keyframes wave {
  0%, 100% { transform: rotate(0deg); }
  10%, 30% { transform: rotate(14deg); }
  20% { transform: rotate(-8deg); }
  40% { transform: rotate(-4deg); }
  50% { transform: rotate(10deg); }
  60% { transform: rotate(0deg); }
}

/* Key Metrics - Critical Above Fold */
.key-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-6);
}

.metric-card {
  background: var(--surface-color);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  border: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.card-icon.employees {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.card-icon.terminations {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.card-icon.payroll {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.card-content h3 {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 4px 0;
}

.card-content p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

/* Mobile Responsive - Critical */
@media (max-width: 768px) {
  .content-wrapper {
    margin-left: 0;
  }
  
  .main-container {
    padding: var(--space-4);
  }
  
  .greeting-card {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-4);
  }
  
  .greeting-text h1 {
    font-size: 1.25rem;
  }
  
  .key-metrics {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
}

/* Loading States */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}
