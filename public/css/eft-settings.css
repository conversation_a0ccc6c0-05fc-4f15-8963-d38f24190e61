/* Modern Theme Variables */
:root {
  --primary: #6366f1;
  --primary-hover: #818cf8;
  --background: #f8fafc;
  --surface: #ffffff;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --border: #e2e8f0;
  --success: #22c55e;
  --error: #ef4444;
}

/* Main Layout */
.main-container {
  padding: 2rem;
  background-color: var(--background);
  max-width: 1400px;
  margin: 0 auto;
}

/* EFT Settings Styles */
.settings-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.settings-card {
  background: var(--surface);
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border);
}

.card-header {
  margin-bottom: 1.5rem;
}

.card-header h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-family: 'Inter', sans-serif;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.card-header h3 i {
  color: var(--primary);
  font-size: 1.25rem;
}

/* Step Indicator */
.step-indicator {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 2rem 0;
  position: relative;
  padding: 0 1rem;
}

.step-indicator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 2rem;
  right: 2rem;
  height: 2px;
  background-color: var(--border);
  z-index: 1;
}

.step-number {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background-color: #f1f5f9;
  border: 2px solid var(--border);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
  position: relative;
  z-index: 2;
  transition: all 0.2s ease;
}

.step-number.active {
  background-color: var(--primary);
  border-color: var(--primary);
  color: #ffffff;
}

.step-number.completed {
  background-color: var(--success);
  border-color: var(--success);
  color: #ffffff;
}

/* Settings Tabs */
.settings-tabs {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid var(--border);
  padding-bottom: 0.5rem;
}

.tab-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
}

.tab-item i {
  font-size: 1.25rem;
}

.tab-item:hover {
  color: var(--primary);
  background-color: #f1f5f9;
}

.tab-item.active {
  color: var(--primary);
  background-color: #eff6ff;
}

/* Main Tabs */
#tabs-section {
  margin: 1.5rem 0;
}

.tab-row {
  display: flex;
  gap: 0.5rem;
  border-bottom: 1px solid var(--border);
  margin-bottom: 2rem;
}

.main-tabs {
  padding-bottom: 0;
}

.tab-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab-button i {
  font-size: 1.25rem;
}

.tab-button:hover {
  color: var(--primary);
}

.tab-button.active {
  color: var(--primary);
  border-bottom-color: var(--primary);
}

/* Form Styles */
.form-step {
  display: none;
}

.form-step.active {
  display: block;
  animation: fadeIn 0.3s ease-out;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.form-group select,
.form-group input {
  width: 100%;
  padding: 0.625rem;
  border: 1px solid var(--border);
  border-radius: 0.375rem;
  font-size: 0.875rem;
  color: var(--text-primary);
  background-color: var(--surface);
  transition: all 0.2s ease;
}

.form-group select:focus,
.form-group input:focus {
  outline: none;
  border-color: var(--primary-hover);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-group select:hover,
.form-group input:hover {
  border-color: var(--primary-hover);
}

/* Error States */
.form-group select.error,
.form-group input.error {
  border-color: var(--error);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-group select.error:focus,
.form-group input.error:focus {
  border-color: var(--error);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Success States */
.form-group select.success,
.form-group input.success {
  border-color: var(--success);
}

/* Loading States */
.btn .ph-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Toast Notifications - PandaPayroll Design System */
.toast {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  background: #fff;
  color: #333;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 10000;
  font-size: 0.875rem;
  font-weight: 500;
  min-width: 300px;
  max-width: 400px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-family: 'Inter', sans-serif;
}

.toast.show {
  transform: translateX(0);
  opacity: 1;
}

.toast i {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.toast-success {
  background: var(--success);
  color: #fff;
  border-left: 4px solid #16a34a;
}

.toast-error {
  background: var(--error);
  color: #fff;
  border-left: 4px solid #dc2626;
}

.toast-warning {
  background: #f59e0b;
  color: #fff;
  border-left: 4px solid #d97706;
}

.toast-info {
  background: var(--primary);
  color: #fff;
  border-left: 4px solid #4f46e5;
}

/* Mobile responsive toast */
@media (max-width: 768px) {
  .toast {
    left: 1rem;
    right: 1rem;
    top: auto;
    bottom: 1rem;
    min-width: 0;
    max-width: none;
    transform: translateY(100%);
  }

  .toast.show {
    transform: translateY(0);
  }
}

/* Additional Bank Accounts Styles */
.additional-accounts-card {
  grid-column: 1 / -1; /* Span full width */
  margin-top: 1.5rem;
}

.additional-accounts-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.8125rem;
}

.additional-accounts-list {
  margin-top: 1rem;
}

.account-item {
  background: var(--background);
  border: 1px solid var(--border);
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1rem;
  transition: all 0.2s ease;
}

.account-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.account-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.account-header h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.account-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-icon {
  background: none;
  border: none;
  padding: 0.5rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-icon:hover {
  background: var(--border);
}

.btn-icon.btn-danger:hover {
  background: var(--error);
  color: white;
}

.btn-icon i {
  font-size: 1rem;
}

.account-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.detail-label {
  font-size: 0.8125rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.detail-value {
  font-size: 0.875rem;
  color: var(--text-primary);
  font-weight: 500;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--text-secondary);
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: var(--border);
}

.empty-state p {
  margin: 0.5rem 0;
}

.empty-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Modal Styles - Bank Account Modal Specific */
#bankAccountModal.xpay-modal {
  display: none !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
  z-index: 10000 !important;
  justify-content: center !important;
  align-items: center !important;
  padding: 20px !important;
  box-sizing: border-box !important;
}

#bankAccountModal.xpay-modal.show {
  display: flex !important;
}

#bankAccountModal .xpay-modal-content {
  background-color: #fff !important;
  border-radius: 12px !important;
  width: 90% !important;
  max-width: 600px !important;
  max-height: 90vh !important;
  overflow-y: auto !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  margin: auto !important;
  padding: 0 !important;
  border: none !important;
  position: relative !important;
  transform: none !important;
  opacity: 1 !important;
  visibility: visible !important;
}

#bankAccountModal .xpay-modal-header {
  padding: 1.5rem !important;
  border-bottom: 1px solid var(--border) !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: 0 !important;
}

#bankAccountModal .xpay-modal-header h3 {
  margin: 0 !important;
  font-size: 1.125rem !important;
  font-weight: 600 !important;
  color: var(--text-primary) !important;
}

#bankAccountModal .close-button {
  background: none !important;
  border: none !important;
  font-size: 1.5rem !important;
  color: var(--text-secondary) !important;
  cursor: pointer !important;
  padding: 0.25rem !important;
  border-radius: 0.25rem !important;
  transition: all 0.2s ease !important;
}

#bankAccountModal .close-button:hover {
  background: var(--border) !important;
  color: var(--text-primary) !important;
}

#bankAccountModal .xpay-modal-body {
  padding: 1.5rem !important;
}

#bankAccountModal .xpay-modal-actions {
  padding: 1rem 1.5rem !important;
  border-top: 1px solid var(--border) !important;
  display: flex !important;
  justify-content: flex-end !important;
  gap: 1rem !important;
}

/* Form Row for Modal */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .settings-grid {
    grid-template-columns: 1fr;
  }

  .account-details {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  #bankAccountModal .xpay-modal-content {
    width: 95% !important;
    margin: 1rem auto !important;
  }
}



  .account-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .account-actions {
    align-self: flex-end;
  }
}

/* Button Styles */
.button-group {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

.btn {
  padding: 0.625rem 1.25rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-secondary {
  background-color: #f1f5f9;
  color: var(--text-secondary);
  border: 1px solid var(--border);
}

.btn-secondary:hover {
  background-color: var(--border);
  color: var(--text-primary);
}

.btn-primary {
  background-color: var(--primary);
  color: #ffffff;
  border: none;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
}

.btn-primary:disabled {
  background-color: #c7d2fe;
  cursor: not-allowed;
}

/* EFT Summary Section */
.eft-summary {
  background-color: var(--background);
  border-radius: 0.375rem;
  padding: 1rem;
  margin-top: 1rem;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--border);
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.summary-value {
  font-size: 0.875rem;
  color: var(--text-primary);
  font-weight: 500;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
