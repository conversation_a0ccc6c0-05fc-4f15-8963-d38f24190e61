/* Reset base layout styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Layout structure */
.layout-wrapper {
  display: flex;
  min-height: 100vh;
  width: 100%;
}

.content-wrapper {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
}

/* Main container positioning */
main {
  flex: 1;
  margin-left: 10px;
  margin-top: 80px;
  padding: 24px;
  width: calc(100vw - 280px);
  min-height: calc(100vh - 80px);
  transition: all 0.3s ease;
  position: relative;
  background: #f8fafc;
}

/* Title Section Styling */
.title-section {
  background: linear-gradient(
    135deg,
    rgba(10, 15, 28, 0.03),
    rgba(20, 27, 45, 0.05)
  );
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.03);
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.banner-header {
  color: #0f172a;
}

.banner-header h1 {
  font-size: 1.875rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #0f172a;
  background: linear-gradient(to right, #0f172a, #334155);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.description {
  color: #64748b;
  font-size: 0.975rem;
  max-width: 600px;
}

.company-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
  padding: 8px 16px;
  background: rgba(59, 130, 246, 0.08);
  border-radius: 100px;
  color: #3b82f6;
  font-size: 0.875rem;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

/* Tabs Section */
#tabs-section {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  padding: 4px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.tab-button {
  padding: 12px 20px;
  border: none;
  background: transparent;
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tab-button:hover {
  color: #8B5CF6;
  background: rgba(139, 92, 246, 0.08);
}

.tab-button.active {
  background: rgba(139, 92, 246, 0.08);
  color: #8B5CF6;
  font-weight: 600;
}

/* Content Section */
#content-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Filters Section */
.filters-section {
  display: flex;
  gap: 16px;
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
}

#statusFilter {
  width: 200px;
  min-width: auto;
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #f8fafc;
  color: #0f172a;
  font-size: 0.875rem;
}

#searchInputEmployee {
  width: 300px;
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #f8fafc;
  color: #0f172a;
  font-size: 0.875rem;
}

/* Table Styling */
.table-container {
  padding: 16px;
  overflow-x: auto;
}

.my-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.my-table th {
  padding: 12px 16px;
  background: #f8fafc;
  color: #0f172a;
  font-weight: 500;
  text-align: left;
  border-bottom: 2px solid #e5e7eb;
}

.my-table td {
  padding: 12px 16px;
  color: #334155;
  border-bottom: 1px solid #e5e7eb;
}

.my-table tr:hover {
  background: #f8fafc;
}

/* Status Badges */
td[data-status] {
  font-weight: 500;
}

td[data-status="Active"] {
  color: #10b981;
}

td[data-status="Inactive"] {
  color: #ef4444;
}

/* Pagination */
.pagination-info {
  padding: 16px;
  color: #64748b;
  font-size: 0.875rem;
}

.pagination-buttons {
  display: flex;
  justify-content: space-between;
  padding: 16px;
  border-top: 1px solid #e5e7eb;
}

.pagination-buttons button {
  padding: 8px 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  color: #0f172a;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-buttons button:hover {
  background: #f8fafc;
  border-color: #3b9eff;
  color: #3b9eff;
}

/* Adjust for collapsed sidebar */
.sidebar.collapsed ~ .content-wrapper main {
  margin-left: 64px;
  width: calc(100vw - 64px);
}

/* Floating Action Button - Mobile */
.fab-add-employee {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 3.5rem;
  height: 3.5rem;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1000;
  display: none;
}

.fab-add-employee:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 12px 35px rgba(139, 92, 246, 0.4);
}

.fab-add-employee:active {
  transform: translateY(0) scale(0.95);
}

/* Responsive Design */
@media screen and (max-width: 1400px) {
  .main-container {
    padding: 100px 1.5rem 2rem 1.5rem;
  }
}

@media screen and (max-width: 1024px) {
  .main-container {
    padding: 100px 1rem 2rem 1rem;
  }

  .action-tabs {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .tab-button {
    padding: 0.625rem 1rem;
    font-size: 0.8125rem;
  }

  .filters-section {
    flex-direction: column;
    gap: 1rem;
  }

  .filter-group {
    flex: none;
  }
}

@media screen and (max-width: 768px) {
  .main-container {
    padding: 100px 0.75rem 2rem 0.75rem;
  }

  .action-tabs {
    display: none; /* Hide on mobile, use FAB instead */
  }

  .fab-add-employee {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .filters-section {
    padding: 1rem;
  }

  .table-container {
    margin: 0 -0.75rem;
    border-radius: 0;
    border-left: none;
    border-right: none;
  }

  .modern-table th,
  .modern-table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.8125rem;
  }

  .pagination-container {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .pagination-buttons {
    justify-content: center;
  }
}

/* Dashboard-aligned color scheme with purple theme */
:root {
  --primary-color: #8b5cf6;
  --secondary-color: #a855f7;
  --accent-color: #c4b5fd;
  --success-color: #10b981;
  --danger-color: #ef4444;
  --warning-color: #f59e0b;
  --background-color: #f9fafb;
  --surface-color: #ffffff;
  --text-primary: #111827;
  --text-secondary: #6b7280;
  --text-muted: #64748b;
  --border-color: #e5e7eb;
  --border-light: #f3f4f6;
  --purple-50: #faf5ff;
  --purple-100: #f3e8ff;
  --purple-500: #8b5cf6;
  --purple-600: #7c3aed;
  --space-4: 1rem;
  --space-6: 1.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
}

/* Page Layout - Dashboard aligned */
.main-container {
  padding: 100px 2rem 2rem 2rem; /* Exact header height (80px) + 20px margin */
  background: var(--background-color);
  min-height: calc(100vh - 80px);
  position: relative;
}

/* Page Header */
.page-header {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  margin-bottom: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.header-content h1 {
  font-size: 1.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.description {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.company-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--background-color);
  border-radius: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Action Tabs - Dashboard style */
.action-tabs {
  display: flex;
  gap: 0.5rem;
  margin-top: 6.5rem; /* Add space below the header */
  margin-bottom: 2rem;
  background: var(--surface-color);
  padding: 0.75rem;
  border-radius: var(--radius-xl);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--border-color);
  position: relative;
  z-index: 1001; /* Higher than header z-index to ensure visibility */
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border: none;
  border-radius: var(--radius-lg);
  background: transparent;
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  white-space: nowrap;
}

.tab-button:hover {
  background: var(--purple-50);
  color: var(--primary-color);
  transform: translateY(-1px);
}

.tab-button.active {
  background: linear-gradient(135deg, var(--purple-100) 0%, var(--purple-50) 100%);
  color: var(--primary-color);
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(139, 92, 246, 0.15);
}

.tab-button .ph {
  font-size: 1.1em;
  transition: transform 0.2s ease;
}

.tab-button:hover .ph,
.tab-button.active .ph {
  transform: scale(1.1);
}

/* Filters Section - Dashboard style */
.filters-section {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 2rem;
  background: var(--surface-color);
  padding: 1.5rem;
  border-radius: var(--radius-xl);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--border-color);
}

.filter-group {
  flex: 1;
}

.filter-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-muted);
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.filter-group label .ph {
  color: var(--primary-color);
  font-size: 1.1em;
}

.filter-select,
.search-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  color: var(--text-primary);
  background: var(--surface-color);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.filter-select:focus,
.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
  transform: translateY(-1px);
}

.filter-select:hover,
.search-input:hover {
  border-color: var(--accent-color);
}

/* Table Styles - Dashboard aligned */
.table-container {
  background: var(--surface-color);
  border-radius: var(--radius-xl);
  padding: 0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--border-color);
  overflow: hidden;
  overflow-x: auto;
}

.modern-table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
}

.modern-table th {
  background: linear-gradient(135deg, var(--purple-50) 0%, var(--purple-100) 100%);
  padding: 1.25rem 1.5rem;
  text-align: left;
  font-weight: 600;
  color: var(--primary-color);
  font-size: 0.875rem;
  letter-spacing: 0.025em;
  text-transform: uppercase;
  border-bottom: 2px solid var(--accent-color);
  position: sticky;
  top: 0;
  z-index: 10;
}

.modern-table th:first-child {
  border-top-left-radius: var(--radius-xl);
}

.modern-table th:last-child {
  border-top-right-radius: var(--radius-xl);
}

.modern-table td {
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid var(--border-light);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.modern-table tr:hover td {
  background: var(--purple-50);
  transform: scale(1.001);
}

.modern-table tr:last-child td {
  border-bottom: none;
}

.modern-table tr:last-child td:first-child {
  border-bottom-left-radius: var(--radius-xl);
}

.modern-table tr:last-child td:last-child {
  border-bottom-right-radius: var(--radius-xl);
}

.employee-name {
  font-weight: 600;
  color: var(--text-primary);
  transition: color 0.2s ease;
}

.modern-table tr:hover .employee-name {
  color: var(--primary-color);
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.375rem 0.875rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: all 0.2s ease;
}

.status-badge.active {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%);
  color: var(--success-color);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-badge.inactive {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.1) 100%);
  color: var(--danger-color);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.status-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Pagination - Dashboard style */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
  padding: 1.5rem;
  background: var(--surface-color);
  border-radius: var(--radius-xl);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--border-color);
}

.pagination-info {
  color: var(--text-muted);
  font-size: 0.875rem;
  font-weight: 500;
}

.pagination-buttons {
  display: flex;
  gap: 0.75rem;
}

.pagination-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  background: var(--surface-color);
  color: var(--text-primary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.pagination-button:hover {
  background: var(--purple-50);
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(139, 92, 246, 0.15);
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.pagination-button:disabled:hover {
  background: var(--surface-color);
  border-color: var(--border-color);
  color: var(--text-primary);
  transform: none;
  box-shadow: none;
}

.pagination-button .ph {
  transition: transform 0.2s ease;
}

.pagination-button:hover .ph {
  transform: scale(1.1);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header,
.filters-section,
.table-container {
  animation: fadeIn 0.5s ease-out forwards;
}

/* Action tabs should be immediately visible without animation */
.action-tabs {
  opacity: 1;
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .action-tabs {
    flex-wrap: wrap;
  }

  .tab-button {
    flex: 1;
    min-width: 200px;
  }

  .filters-section {
    flex-direction: column;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .modern-table {
    font-size: 0.75rem;
  }

  .pagination-container {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}

/* Action Cards */
.action-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 1.5rem;
}

.card-header {
  padding: 1.25rem;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.card-header h3 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #0f172a;
  font-size: 1.125rem;
  font-weight: 600;
}

.card-content {
  padding: 1rem;
}

.action-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.action-list li {
  margin-bottom: 0.5rem;
}

.action-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  color: #64748b;
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.action-link:hover {
  background: #f1f5f9;
  color: #3b82f6;
}

.action-link i {
  font-size: 1.25rem;
}

/* Table Styles */
.table-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-top: 1.5rem;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.modern-table {
  width: 100%;
  border-collapse: collapse;
}

.modern-table th {
  background: rgba(139, 92, 246, 0.08);
  padding: 12px 16px;
  text-align: left;
  font-weight: 500;
  color: #8B5CF6;
  font-size: 0.875rem;
  border-bottom: 1px solid #e5e7eb;
}

.modern-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
  color: #1f2937;
}

/* Status Badge */
.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-badge.completed {
  background: #dcfce7;
  color: #166534;
}

.status-badge.pending {
  background: #fff7ed;
  color: #9a3412;
}

/* Payment Method Badges */
.payment-methods {
  display: flex;
  gap: 8px;
}

.method-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.875rem;
}

.method-badge.eft {
  background: #dbeafe;
  color: #1e40af;
}

.method-badge.cash {
  background: #dcfce7;
  color: #166534;
}

.method-badge.cheque {
  background: #fef3c7;
  color: #92400e;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 8px;
}

.icon-button {
  background: none;
  border: none;
}

/* Additional modern enhancements */
.modern-table tbody tr {
  transition: all 0.2s ease;
}

/* Loading states */
.table-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--text-secondary);
}

.table-loading .ph {
  animation: spin 1s linear infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Empty state */
.table-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--text-secondary);
}

.table-empty .ph {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

/* Focus styles for accessibility */
.tab-button:focus,
.filter-select:focus,
.search-input:focus,
.pagination-button:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}
  padding: 4px;
  cursor: pointer;
  color: #6b7280;
  border-radius: 4px;
}

.icon-button:hover {
  background: #f3f4f6;
  color: #374151;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
}

.empty-state-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.empty-state-content i {
  font-size: 2rem;
  color: #9ca3af;
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.pagination-info {
  color: #6b7280;
  font-size: 0.875rem;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.pagination-button {
  background: none;
  border: 1px solid #e5e7eb;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-number {
  color: #4b5563;
  font-size: 0.875rem;
}
