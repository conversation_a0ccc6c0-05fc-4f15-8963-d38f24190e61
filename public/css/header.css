/* Import Design System Variables */
@import url('./dashboard.css');

/* Unified Header with Light Purple Gradient */
.main-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 var(--space-4); /* Reduced from space-6 to space-4 for better desktop fit */
  background: linear-gradient(135deg, rgba(230, 230, 250, 0.95) 0%, rgba(221, 214, 254, 0.95) 50%, rgba(196, 181, 253, 0.95) 100%);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
  width: 100%;
  max-width: 100vw; /* Prevent overflow */
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 80px;
  z-index: 1000;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
  overflow: visible; /* Allow dropdown to be visible */
}

/* Header Navigation Container */
.header-nav {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  flex: 1;
  max-width: 600px; /* Further reduced to give more space for right side icons */
  min-width: 0; /* Allow shrinking */
}

/* Navigation Tabs */
.nav-tabs {
  display: flex;
  align-items: center;
  gap: 4px; /* Reduced gap between tabs for more compact layout */
  margin-left: var(--space-3); /* Reduced left margin for more compact header */
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: 6px; /* Reduced gap between icon and text for more compact tabs */
  padding: var(--space-2) var(--space-3); /* Reduced padding for more compact tabs */
  border-radius: var(--radius-lg);
  text-decoration: none;
  color: rgba(75, 85, 99, 0.9);
  font-weight: 500;
  font-size: 13px; /* Slightly smaller font for more compact tabs */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.12);
  backdrop-filter: blur(10px);
}

.nav-tab:hover {
  background: rgba(255, 255, 255, 0.15);
  color: rgba(55, 65, 81, 1);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  border-color: rgba(255, 255, 255, 0.2);
}

.nav-tab.active {
  background: transparent; /* Remove card/bubble background */
  color: #8B5CF6; /* Purple text color for active tab */
  border-color: transparent; /* Remove border */
  box-shadow: none; /* Remove shadow */
  font-weight: 600;
}

.nav-tab.active i {
  color: #8B5CF6; /* Purple icon color for active tab */
}

.nav-tab.active::before {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 3px;
  background: linear-gradient(90deg, #8B5CF6, #A855F7, #9333EA);
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.4);
}

.nav-tab i {
  font-size: var(--text-base);
}

/* Navigation Badge */
.nav-badge {
  background: #ff4757;
  color: white;
  font-size: 11px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 4px;
  min-width: 18px;
  text-align: center;
  line-height: 1.2;
}

/* Brand Logo in Header */
.header-brand {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  color: white;
  font-weight: 700;
  font-size: var(--text-xl);
}

.header-brand i {
  font-size: var(--text-2xl);
  color: rgba(255, 255, 255, 0.9);
}

/* Create Reports Button */
.create-reports-btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  background: #8B5CF6;
  color: white;
  padding: var(--space-3) var(--space-5);
  border-radius: var(--radius-lg);
  text-decoration: none;
  font-weight: 600;
  font-size: var(--text-sm);
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
}

.create-reports-btn:hover {
  background: #7C3AED;
  color: white;
}

/* Unified Header Layout */
.header-left {
  display: flex;
  align-items: center;
  gap: var(--space-2); /* Reduced gap in header-left for more compact layout */
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--space-2); /* Reduced gap to bring icons closer */
  flex-shrink: 0; /* Prevent shrinking */
  min-width: 0; /* Allow content to shrink if needed */
  margin-right: var(--space-2); /* Reduced margin for more compact header */
}

.logo-company-switcher {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
  transition: all var(--transition-base);
  cursor: pointer;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  user-select: none; /* Prevent text selection */
}

.logo-company-switcher:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.logo-company-switcher:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* Focus state for accessibility */
.logo-company-switcher:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
}

.logo-company-switcher i {
  color: rgba(75, 85, 99, 0.9);
  font-size: var(--text-base);
}

.logo-company-switcher .ph-caret-down {
  font-size: var(--text-sm);
  margin-left: var(--space-1);
}

/* Responsive Navigation */
@media screen and (max-width: 1500px) {
  .nav-tabs {
    gap: var(--space-1);
  }

  .nav-tab {
    padding: var(--space-2) var(--space-3);
    font-size: var(--text-xs);
  }
}

@media screen and (max-width: 1200px) {
  .nav-tab span {
    display: none;
  }

  .nav-tab i {
    font-size: var(--text-lg);
  }

  .nav-tab {
    padding: var(--space-2);
    min-width: 44px;
    justify-content: center;
  }
}

@media screen and (max-width: 900px) {
  .main-header {
    padding: 0 var(--space-3);
  }

  .create-reports-btn {
    padding: var(--space-2) var(--space-3);
    font-size: var(--text-xs);
  }

  .logo-company-switcher {
    padding: var(--space-1) var(--space-2);
  }

  #currentCompanyName {
    font-size: var(--text-xs);
  }
}

@media screen and (max-width: 768px) {
  .main-header {
    padding: 0.75rem 1rem;
  }

  .nav-tabs {
    display: none;
  }

  .header-left,
  .header-right {
    gap: 1rem;
  }

  .search-container {
    display: none;
  }

  .sub-menu-wrap {
    width: 240px;
  }

  .logo-company-switcher {
    padding: 0.375rem 0.75rem;
  }

  #currentCompanyName {
    font-size: 0.8125rem;
  }

  .mobile-menu-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-md);
    color: rgba(75, 85, 99, 0.9);
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .mobile-menu-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
  }

  .mobile-menu-toggle i {
    font-size: var(--text-lg);
  }

  .create-reports-btn span {
    display: none;
  }

  .create-reports-btn {
    padding: var(--space-2);
    min-width: 44px;
    justify-content: center;
  }

  /* Mobile Navigation Menu */
  .mobile-nav-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    visibility: hidden;
    opacity: 0;
    transition: all 0.3s ease;
  }

  .mobile-nav-menu.open {
    visibility: visible;
    opacity: 1;
  }

  .mobile-nav-content {
    position: absolute;
    top: 0;
    left: 0;
    width: 280px;
    height: 100%;
    background: white;
    box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    overflow-y: auto;
  }

  .mobile-nav-menu.open .mobile-nav-content {
    transform: translateX(0);
  }

  .mobile-nav-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    cursor: pointer;
  }

  .mobile-nav-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-4);
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, rgba(230, 230, 250, 0.95) 0%, rgba(221, 214, 254, 0.95) 50%, rgba(196, 181, 253, 0.95) 100%);
  }

  .mobile-nav-header h3 {
    margin: 0;
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: rgba(55, 65, 81, 0.9);
  }

  .mobile-nav-close {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: var(--radius-sm);
    color: rgba(75, 85, 99, 0.9);
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .mobile-nav-close:hover {
    background: rgba(255, 255, 255, 0.3);
  }

  .mobile-nav-links {
    padding: var(--space-4) 0;
  }

  .mobile-nav-link {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3) var(--space-4);
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
  }

  .mobile-nav-link:hover {
    background: var(--gray-50);
    color: var(--primary-600);
  }

  .mobile-nav-link.active {
    background: var(--primary-50);
    color: var(--primary-600);
    border-left-color: var(--primary-600);
  }

  .mobile-nav-link i {
    font-size: var(--text-lg);
    width: 24px;
    text-align: center;
  }

  .mobile-nav-link .nav-badge {
    margin-left: auto;
    background: var(--primary-600);
    color: white;
    font-size: var(--text-xs);
    padding: 2px 6px;
    border-radius: var(--radius-full);
    min-width: 18px;
    text-align: center;
  }

  body.mobile-nav-open {
    overflow: hidden;
  }
}

/* Hide mobile menu toggle on desktop */
.mobile-menu-toggle {
  display: none;
}

#currentCompanyName {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: rgba(55, 65, 81, 0.9);
}

.company-select-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-sm);
  background: rgba(255, 255, 255, 0.15);
  color: white;
  font-size: var(--text-sm);
  text-decoration: none;
  transition: all var(--transition-base);
  font-weight: var(--font-medium);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.company-select-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Unified Search Container */
.search-container {
  position: relative;
}

.search-container input {
  padding: var(--space-3) var(--space-10) var(--space-3) var(--space-4);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  width: 280px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  transition: all var(--transition-base);
  backdrop-filter: blur(10px);
}

.search-container input:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

.search-container input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.search-container .ph {
  position: absolute;
  right: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.7);
  font-size: var(--text-base);
}

/* Unified User Menu */
.user-menu {
  position: relative;
}

.user-pic {
  width: var(--space-10);
  height: var(--space-10);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  transition: all var(--transition-base);
  font-size: var(--text-lg);
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.user-pic:hover {
  transform: scale(1.05);
  background: rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

/* Clean User Menu Dropdown */
.sub-menu-wrap {
  position: absolute;
  top: calc(100% + 0.75rem);
  right: 0;
  width: 260px;
  background-color: #ffffff;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  z-index: 999999; /* Extremely high z-index to ensure dropdown appears above all elements */
  transform-origin: top right;
  transform: translateY(-8px);
}

.sub-menu-wrap.open {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.sub-menu {
  padding: 1rem;
}

.user-info {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #f3f4f6;
  margin: -1rem -1rem 0.5rem -1rem;
  background: #f9fafb;
  border-radius: 0.75rem 0.75rem 0 0;
}

.user-info h3 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.sub-menu-link {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  color: #6b7280;
  text-decoration: none;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
  font-weight: 500;
}

.sub-menu-link span {
  margin-left: 0.75rem;
  color: inherit;
}

.sub-menu-link:hover {
  background-color: #f3f4f6;
  color: #1f2937;
}

.sub-menu-link i {
  font-size: 1rem;
  color: #9ca3af;
  width: 1.25rem;
  text-align: center;
}

/* Responsive Design */
@media screen and (max-width: 1024px) {
  .search-container input {
    width: 200px;
  }
}



@media screen and (max-width: 480px) {
  .main-header {
    padding: 0.5rem 0.75rem;
  }

  .header-left,
  .header-right {
    gap: 0.75rem;
  }

  .company-select-btn {
    padding: 0.375rem 0.5rem;
    font-size: 0.8125rem;
  }

  .user-pic {
    width: 2rem;
    height: 2rem;
    font-size: 1rem;
  }

  .sub-menu-wrap {
    width: 220px;
    right: -0.5rem;
  }
}
