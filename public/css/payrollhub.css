/* Frequency Section Styles */
.frequency-section {
  margin-bottom: 2rem;
  background: var(--surface-ground);
  border-radius: 8px;
  padding: 1rem;
}

.frequency-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.frequency-header h4 {
  margin: 0;
  font-size: 1.2rem;
  color: #333;
}

.frequency-header .review-btn {
  margin-left: 1rem;
}

.frequency-header h3 {
  font-size: 1.25rem;
  color: var(--text-color);
  margin: 0;
}

.frequency-stats {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.frequency-stats .count {
  font-weight: 600;
  color: #666;
}

.frequency-stats .total-amount {
  color: #28a745;
  font-weight: 600;
}

.frequency-stats {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.period-section {
  background: var(--surface-card);
  border-radius: 6px;
  margin: 1rem 0;
  padding: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.period-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.period-header h4 {
  font-size: 1.1rem;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.period-stats {
  font-size: 0.875rem;
  color: var(--text-color-secondary);
}

.period-actions {
  display: flex;
  gap: 0.5rem;
}

/* Summary Stats Styles */
.period-summary {
  background: var(--surface-hover);
  border-radius: 6px;
  padding: 1rem;
  margin: 0.75rem 0;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.summary-stats .stat-item {
  background: var(--surface-card);
  padding: 0.75rem;
  border-radius: 4px;
  transition: transform 0.2s ease;
}

.summary-stats .stat-item:hover {
  transform: translateY(-2px);
}

/* Employee List Styles */
.employee-list-container {
  border-top: 1px solid var(--surface-border);
  margin-top: 1rem;
  padding-top: 1rem;
}

.employee-list-container .payslips-list {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;
}

.employee-list-container[style*="display: block"] .payslips-list {
  max-height: 1000px;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .frequency-stats,
  .period-header {
    flex-direction: column;
    gap: 0.5rem;
  }

  .period-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .summary-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .summary-stats {
    grid-template-columns: 1fr;
  }

  .frequency-stats {
    flex-direction: column;
    gap: 0.5rem;
  }
}





.header-content .description {
  color: var(--text-color-secondary);
  margin-top: 0.5rem;
}

.company-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--surface-card);
  border-radius: 8px;
  box-shadow: var(--card-shadow);
}

/* Progress Tracker */
.progress-tracker {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--surface-card);
  border-radius: 12px;
  box-shadow: var(--card-shadow);
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 1rem;
  position: relative;
}

.step:not(:last-child)::after {
  content: "";
  position: absolute;
  right: -0.5rem;
  top: 2rem;
  width: 100%;
  height: 2px;
  background: var(--surface-border);
  z-index: 1;
}

.step.active .step-icon,
.step.completed .step-icon {
  background: var(--primary-color);
  color: white;
}

.step.active::after,
.step.completed::after {
  background: var(--primary-color);
}

.step-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: var(--surface-ground);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
  z-index: 2;
}

.step-icon i {
  font-size: 1.25rem;
}

.step-label {
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.step-action {
  margin-top: 0.5rem;
}

/* Pending Payslips Card */
.pending-payslips-card {
  background: var(--surface-card);
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  margin-bottom: 2rem;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid var(--surface-border);
}

.card-header h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
  font-size: 1.25rem;
  color: var(--text-color);
}

.pending-count {
  background: var(--primary-color);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-weight: 500;
}

.payslips-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.payslip-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--surface-border);
}

.payslip-item:last-child {
  border-bottom: none;
}

.employee-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.employee-info .name {
  font-weight: 500;
  color: var(--text-color);
}

.employee-info .period {
  color: var(--text-color-secondary);
  font-size: 0.875rem;
}

.amount {
  font-weight: 600;
  color: var(--text-color);
}

/* Pay Runs History */
.pay-runs-history {
  background: var(--surface-card);
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  overflow: hidden;
}

.table-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--surface-border);
}

.table-header h3 {
  margin: 0;
  font-size: 1.25rem;
  color: var(--text-color);
}

.modern-table {
  width: 100%;
  border-collapse: collapse;
}

.modern-table th {
  background: var(--surface-ground);
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: var(--text-color);
}

.modern-table td {
  padding: 1rem;
  border-bottom: 1px solid var(--surface-border);
}

.modern-table tr:last-child td {
  border-bottom: none;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-badge.draft {
  background: var(--surface-ground);
  color: var(--text-color-secondary);
}

.status-badge.finalized {
  background: var(--success-color);
  color: white;
}

.status-badge.released {
  background: var(--info-color);
  color: white;
}

.icon-button {
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  color: var(--text-color-secondary);
  border-radius: 4px;
  transition: all 0.2s;
}

.icon-button:hover {
  background: var(--surface-ground);
  color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .progress-tracker {
    grid-template-columns: repeat(2, 1fr);
  }

  .step:nth-child(2)::after {
    display: none;
  }
}

/* Main container with proper header clearance */
.main-container {
  padding: 8.5rem 2rem 2rem 2rem; /* Increased top padding to prevent header overlap */
}

@media (max-width: 768px) {
  .main-container {
    padding: 10.5rem 2rem 2rem 2rem; /* Maintain top padding on mobile */
  }



  .progress-tracker {
    grid-template-columns: 1fr;
  }

  .step::after {
    display: none;
  }

  .modern-table {
    display: block;
    overflow-x: auto;
  }
}

/* Action Buttons */
.action-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.action-button:hover {
  background: var(--primary-color-darker);
}

.action-button.secondary {
  background: var(--surface-ground);
  color: var(--text-color);
}

.action-button.secondary:hover {
  background: var(--surface-hover);
}

/* Quick Actions Grid */
.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.action-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card-header {
  padding: 1.25rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--surface-card);
}

.card-header h3 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--text-color);
  font-size: 1.125rem;
  font-weight: 600;
}

.card-content {
  padding: 1.5rem;
}

.card-description {
  color: var(--text-color-secondary);
  margin-bottom: 1.25rem;
  font-size: 0.875rem;
}

/* Recent Activity Section */
.recent-activity-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.section-header {
  padding: 1.25rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--surface-card);
}

.section-header h3 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--text-color);
  font-size: 1.125rem;
  font-weight: 600;
}

.activity-list {
  padding: 1.5rem;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: var(--surface-card);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
}

.activity-details {
  flex: 1;
}

.activity-description {
  color: var(--text-color);
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.activity-time {
  color: var(--text-color-secondary);
  font-size: 0.75rem;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem;
  color: var(--text-color-secondary);
}

.empty-state i {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: var(--text-color-secondary);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .quick-actions-grid {
    grid-template-columns: 1fr;
  }

  .activity-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
}

/* Pending Payslips Card */
.pending-payslips-card {
  background: var(--surface-card);
  border-radius: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 1.5rem;
}

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.header-title h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
  color: var(--text-color);
  font-size: 1.125rem;
  font-weight: 600;
}

.pending-count {
  background: var(--primary-color);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

/* Payslip Items */
.payslips-list {
  padding: 1rem;
}

.payslip-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: var(--surface-card);
  border-radius: 0.75rem;
  margin-bottom: 0.75rem;
  transition: all 0.3s ease;
}

.payslip-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.payslip-main {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.employee-avatar {
  width: 2.5rem;
  height: 2.5rem;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
}

.payslip-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.employee-info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.name {
  font-weight: 600;
  color: var(--text-color);
}

.employee-number {
  color: var(--text-color-secondary);
  font-size: 0.875rem;
}

.payslip-meta {
  display: flex;
  gap: 1rem;
  color: var(--text-color-secondary);
  font-size: 0.875rem;
}

.period,
.amount {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.payslip-actions {
  display: flex;
  gap: 0.5rem;
}

/* Action Buttons */
.action-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-button.primary {
  background-color: var(--primary-color);
  color: white;
  border: none;
  transition: background-color 0.2s;
}

.action-button.primary:hover {
  background-color: var(--primary-color-darker);
}

.action-button.secondary {
  background-color: var(--surface-ground);
  color: var(--text-color);
  border: 1px solid var(--border-color);
  transition: all 0.2s;
}

.action-button.secondary:hover {
  background-color: var(--surface-hover);
}

.action-button.disabled {
  background-color: var(--surface-ground);
  color: var(--text-color-secondary);
  cursor: not-allowed;
  border: 1px solid var(--border-color);
}

.action-button.disabled:hover {
  background-color: var(--surface-ground);
  transform: none;
}

/* Empty State */
.empty-state {
  padding: 3rem 1rem;
  text-align: center;
}

.empty-state-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.empty-state-content i {
  font-size: 2.5rem;
  color: var(--text-color-secondary);
}

.empty-state-content p {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 500;
  color: var(--text-color);
}

.empty-state-description {
  color: var(--text-color-secondary);
  font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .payslip-item {
    flex-direction: column;
    gap: 1rem;
  }

  .payslip-actions {
    width: 100%;
    justify-content: flex-end;
  }
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  animation: fadeIn 0.2s ease-in-out;
}

.modal-content {
  background-color: #ffffff;
  margin: 10% auto;
  max-width: 600px;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  animation: slideIn 0.3s ease-in-out;
}

.large-modal {
  max-width: 90%;
  max-height: 80vh;
  margin: 5% auto;
}

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-header h2 {
  color: var(--text-color);
  font-size: 1.25rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.close-modal {
  font-size: 1.5rem;
  font-weight: 700;
  color: #6c757d;
  cursor: pointer;
  transition: color 0.2s;
}

.close-modal:hover {
  color: #343a40;
}

.modal-header i {
  color: var(--primary-color);
}

.modal-body {
  padding: 1.5rem;
}

.warning-text {
  color: var(--text-color-secondary);
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.pending-list-container {
  margin: 1.5rem 0;
}

.pending-list {
  margin-top: 1rem;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 0.5rem;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.period-header h4 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 0;
  color: var(--text-color);
  font-size: 1rem;
  font-weight: 600;
}

.period-range {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.875rem;
  font-weight: 400;
  color: var(--text-color-secondary);
  padding-left: 0.75rem;
  border-left: 2px solid var(--border-color);
}

.period-meta {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  background-color: var(--surface-card);
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--text-color-secondary);
}

/* Frequency-specific colors */
.frequency-section[data-frequency="weekly"] .period-meta {
  background-color: var(--surface-card);
  color: var(--primary-color);
}

.frequency-section[data-frequency="biweekly"] .period-meta {
  background-color: var(--surface-card);
  color: var(--primary-color);
}

/* Modal Header Styles */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 1rem;
}

.modal-header h2 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
  font-size: 1.25rem;
}

.modal-header i {
  color: var(--primary-color);
}

/* Status Cell Styles */
.status-cell {
  font-weight: 500;
  padding: 6px 12px;
  border-radius: 4px;
  text-align: center;
}

.status-draft {
  background-color: var(--surface-ground);
  color: var(--text-color-secondary);
}

.status-processing {
  background-color: var(--surface-card);
  color: var(--text-color-secondary);
}

.status-review {
  background-color: var(--surface-card);
  color: var(--text-color-secondary);
}

.status-finalized {
  background-color: var(--surface-card);
  color: var(--text-color-secondary);
}

/* Notifications */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 15px 25px;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  z-index: 10000;
  display: none;
  animation: slideIn 0.3s ease-out;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.notification.success {
  background-color: var(--success-color);
}

.notification.error {
  background-color: var(--error-color);
}

.notification.processing {
  background-color: var(--primary-color);
  display: flex;
  align-items: center;
  gap: 10px;
}

.notification.processing::before {
  content: "";
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #fff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Loading Overlay */
.loading-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  color: white;
  margin-top: 10px;
  font-size: 16px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Notification */
.notification {
  display: none;
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 15px 25px;
  border-radius: 4px;
  color: white;
  font-weight: 500;
  z-index: 10000;
  animation: slideIn 0.3s ease-out;
}

.notification.success {
  background-color: var(--success-color);
}

.notification.error {
  background-color: var(--error-color);
}

.notification.processing {
  background-color: var(--primary-color);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Bank File Modal */
#bankFileModal.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.4);
}

#bankFileModal .modal-content {
  background-color: #fefefe;
  margin: 15% auto;
  padding: 20px;
  border: 1px solid #888;
  width: 80%;
  max-width: 500px;
  border-radius: 8px;
  position: relative;
}

#bankFileModal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

#bankFileModal .modal-header h2 {
  margin: 0;
  font-size: 1.5em;
  color: var(--text-color);
}

#bankFileModal .close {
  color: #aaa;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

#bankFileModal .close:hover,
#bankFileModal .close:focus {
  color: black;
  text-decoration: none;
  cursor: pointer;
}

#bankFileModal .modal-body {
  text-align: center;
}

#bankFileModal .modal-body p {
  margin-bottom: 20px;
  color: var(--text-color-secondary);
}

#bankFileModal .loading-spinner {
  margin: 0 auto;
}

/* Status Badge Styles */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  margin-left: 8px;
}

.status-badge.unfinalized {
  background-color: var(--surface-ground);
  color: var(--text-color-secondary);
}

.period-header .status-badge {
  margin-left: auto;
}

/* Period Summary Styles */
.period-summary {
  background: var(--surface-card);
  border-radius: 8px;
  padding: 1rem;
  margin: 0.75rem 0;
  border: 1px solid var(--border-color);
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.summary-stats .stat-item {
  display: flex;
  flex-direction: column;
  padding: 0.75rem;
  background: var(--surface-hover);
  border-radius: 6px;
  transition: all 0.2s ease;
}

.summary-stats .stat-item:hover {
  background: var(--surface-hover);
  transform: translateY(-1px);
}

.summary-stats .stat-label {
  font-size: 0.875rem;
  color: var(--text-color-secondary);
  margin-bottom: 0.25rem;
}

.summary-stats .stat-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
}

/* Employee List Container */
.employee-list-container {
  margin-top: 1rem;
  border-top: 1px solid var(--border-color);
  padding-top: 1rem;
}

.employee-list-container .payslips-list {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;
}

.employee-list-container[style*="display: block"] .payslips-list {
  max-height: 1000px; /* Adjust based on your needs */
}

/* Toggle Button Styles */
.toggle-employees {
  position: relative;
  overflow: hidden;
}

.toggle-employees i {
  transition: transform 0.2s ease;
}

.toggle-employees[aria-expanded="true"] i {
  transform: rotate(180deg);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .summary-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .period-summary {
    padding: 0.75rem;
  }

  .summary-stats .stat-item {
    padding: 0.5rem;
  }
}

@media (max-width: 480px) {
  .summary-stats {
    grid-template-columns: 1fr;
  }

  .summary-stats .stat-value {
    font-size: 1.125rem;
  }
}

.pending-payslips {
  margin-top: 32px;
}

.frequency-group {
  background: #fff;
  border-radius: 12px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.frequency-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.frequency-header h4 {
  margin: 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 500;
}

.frequency-stats {
  display: flex;
  gap: 16px;
}

.count {
  background: #e3f2fd;
  color: #1976d2;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.85rem;
  font-weight: 500;
}

.total-amount {
  color: #2e7d32;
  font-weight: 500;
  font-size: 0.95rem;
}

.payslips-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.checkbox-container {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.checkbox-container input[type="checkbox"] {
  margin-right: 8px;
}

.bulk-finalize-btn {
  background-color: #3b82f6;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.bulk-finalize-btn:hover {
  background-color: #2563eb;
}

.payslip-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  background-color: #f8fafc;
  margin-bottom: 10px;
}

.payslip-item:hover {
  background-color: #f1f5f9;
}

.employee-info {
  flex: 1;
  margin-left: 10px;
}

.employee-info .name {
  font-weight: 500;
  color: #1e293b;
  display: block;
  margin-bottom: 3px;
}

.employee-info .period {
  font-size: 13px;
  color: #64748b;
}

.payslip-amounts {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-right: 15px;
}

.payslip-amounts .amount {
  font-weight: 600;
  color: #1e293b;
}

.payslip-amounts .deductions {
  font-size: 13px;
  color: #64748b;
}

.payslip-actions {
  display: flex;
  gap: 10px;
}

.action-icon {
  background: none;
  border: none;
  color: #64748b;
  cursor: pointer;
  font-size: 18px;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.2s, color 0.2s;
}

.action-icon:hover {
  background-color: #e2e8f0;
  color: #1e293b;
}

.total-summary {
  background-color: #f8fafc;
  padding: 15px 20px;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

/* Pay Run History */
.pay-runs-history {
  margin-bottom: 30px;
}

.modern-table {
  width: 100%;
  border-collapse: collapse;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.modern-table th {
  background-color: #f8fafc;
  padding: 15px;
  text-align: left;
  font-weight: 600;
  color: #64748b;
  border-bottom: 1px solid #e2e8f0;
}

.modern-table td {
  padding: 15px;
  border-bottom: 1px solid #e2e8f0;
  color: #1e293b;
}

.modern-table tr:last-child td {
  border-bottom: none;
}

.status-badge {
  display: inline-block;
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
}

.status-badge.draft {
  background-color: #fef3c7;
  color: #d97706;
}

.status-badge.finalized {
  background-color: #dcfce7;
  color: #16a34a;
}

.status-badge.released {
  background-color: #e0f2fe;
  color: #0284c7;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;
  padding: 5px 0;
}

.action-buttons .icon-button {
  margin: 2px;
  flex: 0 0 auto;
}

.icon-button {
  background: none;
  border: none;
  color: #64748b;
  cursor: pointer;
  font-size: 18px;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.2s, color 0.2s;
}

.icon-button:hover {
  background-color: #e2e8f0;
  color: #1e293b;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  text-align: center;
}

.empty-state i {
  font-size: 48px;
  color: #cbd5e1;
  margin-bottom: 15px;
}

.empty-state p {
  font-size: 16px;
  color: #64748b;
  margin-bottom: 20px;
}

/* Modals */
.xpay-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  justify-content: center;
  align-items: center;
}

.xpay-modal.show {
  display: flex;
}

.xpay-modal-content {
  background-color: #fff;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.xpay-modal-header {
  padding: 20px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.xpay-modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  color: #64748b;
  cursor: pointer;
}

.xpay-modal-body {
  padding: 20px;
}

.modal-note {
  font-size: 14px;
  color: #64748b;
  margin-top: 10px;
}

.xpay-modal-actions {
  padding: 15px 20px;
  border-top: 1px solid #e2e8f0;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* Payslip View */
.payslip-view {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1rem;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.employee-section {
  padding: 1rem;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.employee-section h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  color: #333;
}

.employee-section p {
  margin: 0.25rem 0;
  color: #666;
}

.payslip-section {
  padding: 1rem;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.payslip-row {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem 0;
  border-bottom: 1px solid #eee;
}

.payslip-row:last-child {
  border-bottom: none;
}

.payslip-row.deduction {
  color: #dc3545;
  font-size: 0.95rem;
  padding-left: 1rem;
}

.payslip-row.total {
  font-weight: 700;
  font-size: 1.1rem;
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 2px solid #eee;
}

/* Dashboard Summary Cards */
.dashboard-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.summary-card {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.summary-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  margin-right: 1rem;
  font-size: 1.5rem;
}

.pending-card .card-icon {
  background-color: #fff3cd;
  color: #856404;
}

.finalized-card .card-icon {
  background-color: #d4edda;
  color: #155724;
}

.amount-card .card-icon {
  background-color: #cce5ff;
  color: #004085;
}

.payruns-card .card-icon {
  background-color: #e2e3e5;
  color: #383d41;
}

.card-content h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
}

.card-content p {
  margin: 0.25rem 0 0 0;
  color: #6c757d;
  font-size: 0.9rem;
}

/* Section Headers */
.section-header {
  margin: 2rem 0 1rem 0;
}

.section-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: #333;
}

.section-header p {
  margin: 0.25rem 0 0 0;
  color: #6c757d;
}

/* Responsive Styles for Payslip View */
@media (max-width: 768px) {
  .payslip-view {
    padding: 0.5rem;
  }

  .employee-section,
  .payslip-section {
    padding: 0.75rem;
  }

  .dashboard-summary {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .dashboard-summary {
    grid-template-columns: 1fr;
  }

  .summary-card {
    padding: 1rem;
  }

  .card-icon {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1.25rem;
  }

  .card-content h3 {
    font-size: 1.25rem;
  }
}

/* Progress Tracker Status Pill */
.tracker-status .progress-status-pill {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.25;
  gap: 6px;
}

.progress-status-pill.draft {
  background-color: #fef3c7;
  color: #92400e;
}

.progress-status-pill.pending {
  background-color: #fef3c7;
  color: #92400e;
}

.progress-status-pill.processing {
  background-color: #dbeafe;
  color: #1e40af;
}

.progress-status-pill.finalized {
  background-color: #d1fae5;
  color: #065f46;
}

.progress-status-pill.released {
  background-color: #f3e8ff;
  color: #6b21a8;
}

.progress-status-pill i {
  font-size: 1rem;
}

.progress-status-pill.new {
  background-color: #e5f6fd;
  color: #0891b2;
}

/* Payslips Review Modal */
.payslips-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 0.875rem;
  color: #6c757d;
  margin-bottom: 0.25rem;
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
}

.stat-value.currency {
  color: #28a745;
}

.payslips-table-container {
  max-height: 400px;
  overflow-y: auto;
  margin-top: 1rem;
  border: 1px solid #e9ecef;
  border-radius: 8px;
}

.payslips-table {
  width: 100%;
  border-collapse: collapse;
}

.payslips-table th,
.payslips-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.payslips-table th {
  background-color: #f8f9fa;
  position: sticky;
  top: 0;
  z-index: 10;
}

.payslips-table .text-right {
  text-align: right;
}

.loading-indicator {
  text-align: center;
  padding: 2rem 0;
}

.loading-indicator .spinner {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #3b82f6;
  animation: spin 1s ease-in-out infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.summary-actions {
  display: flex;
  gap: 0.5rem;
}

.modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

/* Checkbox styling */
.checkbox-wrapper {
  position: relative;
  display: inline-block;
}

.checkbox-wrapper input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkbox-wrapper label {
  position: relative;
  cursor: pointer;
  padding-left: 25px;
  user-select: none;
}

.checkbox-wrapper label:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 18px;
  height: 18px;
  border: 1px solid #ccc;
  background-color: #fff;
  border-radius: 3px;
}

.checkbox-wrapper input[type="checkbox"]:checked + label:before {
  background-color: #3b82f6;
  border-color: #3b82f6;
}

.checkbox-wrapper input[type="checkbox"]:checked + label:after {
  content: '';
  position: absolute;
  left: 6px;
  top: 3px;
  width: 6px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.checkbox-wrapper input[type="checkbox"]:disabled + label:before {
  background-color: #e9ecef;
  border-color: #ced4da;
}

.checkbox-wrapper input[type="checkbox"]:disabled + label {
  color: #6c757d;
  cursor: not-allowed;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.icon-button {
  background: none;
  border: none;
  color: #3b82f6;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.icon-button:hover {
  background-color: rgba(59, 130, 246, 0.1);
}

.no-data {
  text-align: center;
  padding: 2rem;
  color: #6c757d;
}

.error-row {
  text-align: center;
  padding: 1rem;
  color: #dc3545;
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1.5rem;
  padding: 1rem;
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.pagination-info {
  color: var(--text-secondary, #64748b);
  font-size: 0.875rem;
}

.pagination-buttons {
  display: flex;
  gap: 1rem;
}

.pagination-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color, #e2e8f0);
  border-radius: 0.5rem;
  background: white;
  color: var(--text-primary, #1e293b);
  font-size: 0.875rem;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.3s ease;
}

.pagination-button:hover:not(.disabled) {
  background: var(--background-color, #f8fafc);
  border-color: var(--primary-color, #3b82f6);
  color: var(--primary-color, #3b82f6);
}

.pagination-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}
