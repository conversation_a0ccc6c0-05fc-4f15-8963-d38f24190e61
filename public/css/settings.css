/* Global Mobile Responsiveness */
* {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

html, body {
  max-width: 100%;
  overflow-x: hidden;
  touch-action: manipulation;
}

@media screen and (max-width: 768px) {
  /* Reset main container for mobile */
  .main-container {
    width: 100vw;
    max-width: 100vw;
    margin: 2rem 0 0 0 !important;
    padding: 1rem;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow-x: hidden;
    position: relative;
  }

  /* Adjust page header for mobile */
  .page-header {
    width: 100%;
    max-width: 100%;
    margin: 0 0 1rem 0;
    padding: 1rem;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
  }

  .page-header .header-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .page-header h1 {
    width: 100%;
    text-align: center;
    margin-bottom: 0.5rem;
  }

  .page-header .description {
    width: 100%;
    text-align: center;
    margin-bottom: 1rem;
  }

  .company-badge {
    align-self: center;
    margin-top: 0.5rem;
  }

  /* Prevent any horizontal scrolling */
  html, body {
    max-width: 100%;
    overflow-x: hidden;
  }

  * {
    max-width: 100%;
    box-sizing: border-box;
  }
}

/* Ensure desktop styles don't interfere with mobile */
@media screen and (min-width: 769px) {
  .main-container {
    margin-left: 280px !important;
  }
}

/* Smaller Mobile Devices */
@media screen and (max-width: 375px) {
  .main-container {
    padding: 0.5rem;
  }

  .page-header h1 {
    font-size: 1.3rem;
  }

  .page-header .description {
    font-size: 0.8rem;
  }
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-group select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.form-group select:focus {
  outline: none;
  border-color: #4caf50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

/* Add these styles */
.beneficiary-form {
  max-width: 600px;
  margin: 2rem auto;
  padding: 2rem;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  align-items: center;
}

.btn-submit {
  background: #4caf50;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
}

.btn-cancel {
  background: #f44336;
  color: white;
  text-decoration: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
}

.btn-submit:hover {
  background: #45a049;
}

.btn-cancel:hover {
  background: #da190b;
}

/* Add these styles */
.beneficiary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #eee;
}

.beneficiary-item:last-child {
  border-bottom: none;
}

.beneficiary-name {
  font-size: 1rem;
  color: #333;
}

.beneficiary-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-edit {
  background: #2196f3;
  color: white;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 0.875rem;
}

.btn-edit:hover {
  background: #1976d2;
}

.beneficiary-list {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-top: 1rem;
}

.beneficiary-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.btn-delete {
  background: #dc3545;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  margin-left: auto;
}

.btn-delete:hover {
  background: #c82333;
}

/* Custom Items Form Styling */
.custom-item-form {
  max-width: 800px;
  margin: 2rem auto;
  padding: 2rem;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Form Sections */
.form-section {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #eee;
}

.form-section:last-child {
  border-bottom: none;
}

.form-section h3 {
  color: #333;
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
  font-weight: 600;
}

/* Form Groups */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.form-group small {
  display: block;
  margin-top: 0.25rem;
  color: #666;
  font-size: 0.875rem;
}

/* Form Inputs */
.form-group input[type="text"],
.form-group input[type="number"],
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #4caf50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

/* Checkbox Styling */
.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  width: 18px;
  height: 18px;
  margin: 0;
}

/* Checkbox Group */
.checkbox-group {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
  margin-top: 0.5rem;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: normal;
  margin: 0;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #eee;
}

.btn-submit {
  background: #4caf50;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.2s;
}

.btn-submit:hover {
  background: #45a049;
}

.btn-cancel {
  background: #f44336;
  color: white;
  text-decoration: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-size: 1rem;
  transition: background-color 0.2s;
}

.btn-cancel:hover {
  background: #da190b;
}

/* Specific Field Styling */
#formulaField textarea {
  font-family: monospace;
  min-height: 100px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .custom-item-form {
    margin: 1rem;
    padding: 1rem;
  }

  .checkbox-group {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  }

  .form-actions {
    flex-direction: column;
  }

  .btn-submit,
  .btn-cancel {
    width: 100%;
    text-align: center;
  }
}

/* Error States */
.form-group.has-error input,
.form-group.has-error select,
.form-group.has-error textarea {
  border-color: #f44336;
}

.form-group.has-error small {
  color: #f44336;
}

/* Success States */
.form-group.is-valid input,
.form-group.is-valid select,
.form-group.is-valid textarea {
  border-color: #4caf50;
}

/* Loading State */
.btn-submit.loading {
  background-color: #999;
  cursor: not-allowed;
  position: relative;
}

.btn-submit.loading::after {
  content: "";
  position: absolute;
  width: 20px;
  height: 20px;
  border: 2px solid #fff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
  margin-left: 10px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Modal Styles */
.xpay-modal-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  background-color: rgba(30, 41, 59, 0.5); /* Primary text color with opacity */
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.xpay-modal-content {
  background-color: #ffffff; /* Card background */
  width: 400px;
  max-width: 90%;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 10px 15px rgba(99, 102, 241, 0.1); /* Primary color shadow */
  border: 1px solid #e2e8f0; /* Border color */
  transform: scale(0.7);
  opacity: 0;
  transition: all 0.3s ease;
}

.xpay-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.xpay-modal-header h2 {
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.xpay-modal-header .xpay-close {
  cursor: pointer;
  color: #64748b;
  font-size: 24px;
  line-height: 1;
}

.xpay-modal-body .eft-details {
  background-color: #f8fafc;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 20px;
  text-align: center;
}

.xpay-modal-body .eft-details p {
  color: #1e293b;
  margin: 0;
}

.xpay-modal-body .form-group {
  margin-bottom: 20px;
}

.xpay-modal-body .form-group label {
  display: block;
  margin-bottom: 8px;
  color: #1e293b;
  font-weight: 500;
}

.xpay-modal-body .form-group input[type="date"] {
  width: 100%;
  padding: 10px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  color: #1e293b;
  background-color: #ffffff;
}

.xpay-modal-body .button-group {
  display: flex;
  justify-content: space-between;
  gap: 12px;
}

.xpay-modal-body .btn-secondary,
.xpay-modal-body .btn-primary {
  flex-grow: 1;
  padding: 12px;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.xpay-modal-body .btn-secondary {
  background-color: #818cf8;
}

.xpay-modal-body .btn-primary {
  background-color: #6366f1;
}

/* Custom Items List Styling */
.custom-items-container {
  padding: 1rem;
}

.custom-items-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 2rem;
}

.custom-items-section {
  margin-bottom: 2rem;
}

.custom-items-section h3 {
  color: #333;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #eee;
  margin-bottom: 1rem;
}

.custom-item {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.custom-item-details {
  flex: 1;
}

.custom-item-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.custom-item-header h4 {
  margin: 0;
  color: #333;
  font-size: 1.1rem;
}

.item-code {
  color: #666;
  font-size: 0.9rem;
  padding: 0.2rem 0.5rem;
  background: #f5f5f5;
  border-radius: 4px;
}

.custom-item-info {
  display: flex;
  gap: 2rem;
  margin-bottom: 0.5rem;
}

.calculation-type,
.frequency {
  color: #666;
  font-size: 0.9rem;
}

.description {
  color: #777;
  font-size: 0.9rem;
  margin: 0.5rem 0 0 0;
}

.custom-item-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-edit,
.btn-delete {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-edit {
  background: #2196f3;
  color: white;
  text-decoration: none;
}

.btn-edit:hover {
  background: #1976d2;
}

.btn-delete {
  background: #dc3545;
  color: white;
  border: none;
}

.btn-delete:hover {
  background: #c82333;
}

.no-items {
  color: #666;
  font-style: italic;
  padding: 1rem;
  text-align: center;
  background: #f9f9f9;
  border-radius: 4px;
}

/* Custom Items Table Styling */
.custom-items-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.custom-items-table th,
.custom-items-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.custom-items-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.custom-items-table tr:last-child td {
  border-bottom: none;
}

.custom-items-table .actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-start;
}

.custom-items-table .btn-edit,
.custom-items-table .btn-delete {
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  transition: opacity 0.2s;
}

.custom-items-table .btn-edit i {
  color: #2196f3;
}

.custom-items-table .btn-delete i {
  color: #dc3545;
}

.custom-items-table .btn-edit:hover,
.custom-items-table .btn-delete:hover {
  opacity: 0.7;
}

.custom-items-table .no-items {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 2rem;
}

.custom-items-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.custom-items-header h2 {
  margin: 0;
}

/* Main Container */
.main-container {
  padding: 2rem;
  background: #f8fafc;
  min-height: calc(100vh - 80px);
  margin-left: 280px;
  margin-top: 80px;
}

/* Page Header */
.page-header {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.03);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content h1 {
  font-size: 1.875rem;
  font-weight: 600;
  color: #0f172a;
  margin-bottom: 0.5rem;
}

.description {
  color: #64748b;
  font-size: 0.975rem;
}

.company-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(59, 130, 246, 0.08);
  border-radius: 100px;
  color: #3b82f6;
  font-size: 0.875rem;
}

/* Tab Navigation */
.tab-row {
  display: flex;
  gap: 1rem;
  padding: 0 1rem;
  border-bottom: 1px solid #e2e8f0;
  margin-bottom: 2rem;
}

.tab-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  color: #64748b;
  text-decoration: none;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab-button i {
  font-size: 1.25rem;
}

.tab-button span {
  font-weight: 500;
}

.tab-button:hover {
  color: #8B5CF6;
}

.tab-button.active {
  color: #8B5CF6;
  border-bottom-color: #8B5CF6;
}

/* Sub Tabs */
.sub-tabs {
  margin-top: -1rem;
  background-color: #f8fafc;
  border-bottom: none;
}

.sub-tabs .tab-button {
  color: #64748b;
  padding: 0.5rem 1rem;
}

.sub-tabs .tab-button:hover {
  color: #8B5CF6;
}

.sub-tabs .tab-button.active {
  color: #8B5CF6;
  border-bottom-color: #8B5CF6;
  background-color: #fff;
}

/* Settings Tabs */
.settings-tabs {
  display: flex;
  gap: 0.5rem;
  background: white;
  padding: 0.5rem;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border: none;
  background: transparent;
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tab-button:hover {
  background: rgba(139, 92, 246, 0.08);
  color: #8B5CF6;
}

.tab-button.active {
  background: rgba(139, 92, 246, 0.08);
  color: #8B5CF6;
  font-weight: 600;
}

/* Settings Grid */
.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

/* Settings Card */
.settings-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.card-header {
  padding: 1.25rem;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.card-header h3 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #0f172a;
  font-size: 1.125rem;
  font-weight: 600;
}

.card-content {
  padding: 1rem;
}

/* Settings Links */
.settings-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.settings-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  color: #64748b;
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.settings-link:hover {
  background: #f1f5f9;
  color: #3b82f6;
}

.settings-link span {
  flex: 1;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.settings-card {
  animation: fadeIn 0.5s ease-out forwards;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .settings-tabs {
    flex-wrap: wrap;
  }

  .tab-button {
    flex: 1;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .settings-grid {
    grid-template-columns: 1fr;
  }
}

/* Mobile Responsiveness */
@media screen and (max-width: 768px) {
  .main-container {
    padding: 1rem;
    margin-bottom: 80px; /* Account for bottom nav */
    width: 100%; /* Ensure full width */
    max-width: 100%; /* Prevent overflow */
    box-sizing: border-box; /* Include padding in width calculation */
    margin-left: auto; /* Center the container */
    margin-right: auto; /* Center the container */
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    width: 100%; /* Ensure full width */
    max-width: 100%; /* Prevent overflow */
  }

  .page-header .header-content {
    width: 100%;
    max-width: 100%;
  }

  .page-header h1 {
    font-size: 1.5rem;
  }

  .page-header .description {
    font-size: 0.9rem;
  }

  .company-badge {
    align-self: flex-start;
  }

  .tab-row.main-tabs {
    flex-direction: column;
    gap: 0.5rem;
    width: 100%; /* Ensure full width */
    max-width: 100%; /* Prevent overflow */
  }

  .tab-row.main-tabs .tab-button {
    width: 100%;
    text-align: left;
    padding: 0.75rem 1rem;
  }

  .tab-content {
    padding: 1rem 0;
    width: 100%; /* Ensure full width */
    max-width: 100%; /* Prevent overflow */
  }

  /* Additional centering for other elements */
  #tabs-section,
  #tab-content {
    width: 100%;
    max-width: 100%;
    margin-left: auto;
    margin-right: auto;
  }
}

/* Added CSS for missing bank details modal */
.xpay-modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0,0,0,0.4);
}

.xpay-modal-content {
  background-color: #fefefe;
  margin: 15% auto;
  padding: 20px;
  border: 1px solid #888;
  width: 80%;
  max-width: 500px;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.xpay-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 10px;
  margin-bottom: 15px;
}

.xpay-close {
  color: #aaa;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.xpay-close:hover,
.xpay-close:focus {
  color: #000;
  text-decoration: none;
}

.xpay-modal-body {
  max-height: 300px;
  overflow-y: auto;
}

#missingEmployeesDetails {
  white-space: pre-line;
  color: #d32f2f;
  font-family: monospace;
  background-color: #f8f8f8;
  padding: 10px;
  border-radius: 4px;
}

/* Bank File Generation Modal Styles */
.action-date-modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0,0,0,0.4);
  opacity: 0;  /* Start with 0 opacity */
  transition: opacity 0.3s ease;  /* Smooth transition */
  pointer-events: none;  /* Prevent interactions when invisible */
}

.action-date-modal.show {
  opacity: 1;
  pointer-events: auto;
}

.action-date-modal .xpay-modal-content {
  background-color: #fefefe;
  margin: 15% auto;  /* Centered vertically */
  padding: 20px;
  border: 1px solid #888;
  width: 400px;
  max-width: 90%;  /* Responsive width */
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  transform: scale(0.7);  /* Start smaller */
  opacity: 0;
  transition: all 0.3s ease;
}

.action-date-modal.show .xpay-modal-content {
  transform: scale(1);  /* Full size */
  opacity: 1;
}

.action-date-modal .xpay-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 10px;
  margin-bottom: 15px;
}

.action-date-modal .xpay-modal-header h2 {
  margin: 0;
  font-size: 1.2rem;
  color: #333;
}

.action-date-modal .xpay-close {
  color: #aaa;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.action-date-modal .xpay-close:hover,
.action-date-modal .xpay-close:focus {
  color: #000;
  text-decoration: none;
}

.action-date-modal .eft-details {
  background-color: #f4f4f4;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
  text-align: center;
}

.action-date-modal .form-group {
  margin-bottom: 15px;
}

.action-date-modal .form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.action-date-modal .form-group input[type="date"] {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.action-date-modal .button-group {
  display: flex;
  justify-content: space-between;
}

.action-date-modal .button-group button {
  flex-grow: 1;
  margin: 0 5px;
  padding: 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.action-date-modal .button-group .btn-primary {
  background-color: #007bff;
  color: white;
}

.action-date-modal .button-group .btn-secondary {
  background-color: #6c757d;
  color: white;
}

.action-date-modal .button-group .btn-primary:hover {
  background-color: #0056b3;
}

.action-date-modal .button-group .btn-secondary:hover {
  background-color: #545b62;
}

/* Dropdown Styles Removed */

.settings-card.disabled {
  position: relative;
  cursor: not-allowed;
  opacity: 0.7;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
}

.settings-card .feature-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
}

.settings-card .feature-overlay i {
  font-size: 2rem;
  color: #6c757d;
}
