/* Import Design System Variables */
@import url('./dashboard.css');

/* Hide Sidebar - Navigation moved to header */
.sidebar {
  display: none;
}

.sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  right: -1px;
  width: 1px;
  height: var(--header-height);
  background: linear-gradient(180deg, var(--primary-500) 0%, var(--primary-300) 100%);
  z-index: 1001;
}

/* Unified Brand Area */
.sidebar-brand {
  padding: var(--space-6) var(--space-4);
  border-bottom: 1px solid var(--border-light);
  margin-bottom: var(--space-2);
  background: linear-gradient(135deg, var(--primary-50) 0%, var(--surface-color) 100%);
}

.brand-content {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.brand-icon {
  width: var(--space-10);
  height: var(--space-10);
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: var(--text-xl);
  flex-shrink: 0;
  box-shadow: var(--shadow-md);
}

.brand-text {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  transition: all var(--transition-base);
}

.sidebar.collapsed {
  width: var(--sidebar-collapsed-width);
}

.sidebar.collapsed .item-text,
.sidebar.collapsed .section-header,
.sidebar.collapsed .brand-text {
  opacity: 0;
  transform: translateX(-10px);
  pointer-events: none;
}

.sidebar ul {
  list-style: none;
  padding: var(--space-6) 0;
  margin: 0;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

/* Unified Sidebar Items */
.sidebar-item {
  position: relative;
  display: flex;
  align-items: center;
  padding: var(--space-3) var(--space-4);
  margin: 0 var(--space-4);
  cursor: pointer;
  transition: all var(--transition-base);
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
  font-size: var(--text-sm);
  overflow: hidden;
  border: 1px solid transparent;
}

.sidebar-item:hover {
  background: var(--primary-50);
  color: var(--text-primary);
  border-color: var(--primary-100);
  transform: translateX(2px);
}

.sidebar-item.active {
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  color: var(--white);
  font-weight: var(--font-semibold);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-400);
}

.sidebar-item.active:hover {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
  transform: translateX(2px);
  box-shadow: var(--shadow-lg);
}

/* Unified Icon Containers */
.icon-container {
  width: var(--space-6);
  height: var(--space-6);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--space-3);
  flex-shrink: 0;
  border-radius: var(--radius-sm);
  transition: all var(--transition-base);
  background: transparent;
}

.icon-container i {
  font-size: var(--text-lg);
  transition: all var(--transition-base);
}

.sidebar-item:hover .icon-container {
  background: var(--primary-100);
  transform: scale(1.05);
}

.sidebar-item:hover .icon-container i {
  color: var(--primary-600);
}

.sidebar-item.active .icon-container {
  background: rgba(255, 255, 255, 0.2);
}

.sidebar-item.active .icon-container i {
  color: var(--white);
}

/* Clean Text Styling */
.item-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: all 0.2s ease;
  line-height: 1.5;
}

/* Clean Section Headers */
.section-header {
  padding: 2rem 1rem 0.5rem 1rem;
  font-size: 0.75rem;
  text-transform: uppercase;
  color: #9ca3af;
  font-weight: 600;
  letter-spacing: 0.05em;
  margin-top: 1rem;
}

/* Clean Toggle Button */
.toggle-btn {
  position: relative;
  width: calc(100% - 2rem);
  margin: 1rem;
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
  background: #ffffff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  transition: all 0.2s ease;
  border-radius: 0.5rem;
  font-weight: 500;
}

.toggle-btn:hover {
  background: #f9fafb;
  border-color: #d1d5db;
  color: #374151;
}

.toggle-btn i {
  font-size: 1.125rem;
  transition: all 0.2s ease;
}

.sidebar-item a {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  z-index: 1;
}

/* Clean Tooltip Styles */
.sidebar.collapsed .sidebar-item:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  left: calc(100% + 0.75rem);
  top: 50%;
  transform: translateY(-50%);
  background: #1f2937;
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.sidebar.collapsed .sidebar-item:hover::before {
  content: '';
  position: absolute;
  left: calc(100% + 0.25rem);
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  border-right: 6px solid #1f2937;
  z-index: 1001;
}

/* Clean Layout Adjustments */
.layout-wrapper {
  display: flex;
  min-height: 100vh;
  background: #f9fafb;
}

.content-wrapper {
  flex: 1;
  margin-left: 280px;
  transition: margin-left 0.3s ease;
  position: relative;
}

.sidebar.collapsed + .content-wrapper {
  margin-left: 72px;
}

/* Responsive Design */
@media screen and (max-width: 1024px) {
  .sidebar {
    width: 260px;
  }

  .content-wrapper {
    margin-left: 260px;
  }

  .sidebar.collapsed + .content-wrapper {
    margin-left: 72px;
  }
}

@media screen and (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    width: 280px;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
  }

  .sidebar.mobile-open {
    transform: translateX(0);
  }

  .content-wrapper {
    margin-left: 0 !important;
  }

  /* Mobile overlay */
  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
  }

  .sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
  }
}

@media screen and (max-width: 480px) {
  .sidebar {
    width: 100%;
    max-width: 320px;
  }

  .sidebar-item {
    padding: 0.875rem 1rem;
    margin: 0 1rem;
  }

  .toggle-btn {
    margin: 1rem;
    width: calc(100% - 2rem);
  }

  .section-header {
    padding: 1.5rem 1rem 0.5rem 1rem;
  }
}