.notifications {
  position: relative;
  display: inline-block;
}

.notification-count {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: red;
  color: white;
  border-radius: 50%;
  padding: 2px 5px;
  font-size: 12px;
}

.onboarding-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: bold;
}

.form-group input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.btn-primary {
  background-color: #8B5CF6;
  color: white;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.btn-primary:hover {
  background-color: #7C3AED;
}

/* Toast Notifications */
.toast-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  gap: 12px;
  z-index: 9999;
  animation: slideIn 0.3s ease-out;
  max-width: 400px;
  min-width: 300px;
}

.toast-notification.success {
  border-left: 4px solid #10b981;
}

.toast-notification.error {
  border-left: 4px solid #ef4444;
}

.toast-notification.info {
  border-left: 4px solid #3b82f6;
}

.toast-notification i {
  font-size: 20px;
}

.toast-notification.success i {
  color: #10b981;
}

.toast-notification.error i {
  color: #ef4444;
}

.toast-notification.info i {
  color: #3b82f6;
}

.toast-notification span {
  color: #1f2937;
  font-size: 14px;
  font-weight: 500;
}

.toast-notification.fade-out {
  animation: slideOut 0.3s ease-out forwards;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Banking Details Section Styles */
.banking-details-fields {
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* Creates two equal columns */
  grid-gap: 1rem;
  transition: all 0.3s ease-in-out;
  margin-top: 1rem;
}

/* Payment method should span full width */
#paymentMethod {
  grid-column: 1 / -1;
}

.banking-details-fields.hidden {
  display: none;
}

.select-wrapper,
.input-wrapper {
  position: relative;
  width: 100%;
}

.select-wrapper select,
.input-wrapper input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--input-bg);
}

.select-wrapper i.ph {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  color: var(--text-muted);
}

.required {
  color: var(--danger);
  margin-left: 2px;
}

/* Responsive layout for mobile devices */
@media screen and (max-width: 768px) {
  .banking-details-fields {
    grid-template-columns: 1fr; /* Single column on mobile */
  }
}
