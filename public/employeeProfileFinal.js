document.addEventListener("DOMContentLoaded", function () {
  const selectMonthButton = document.getElementById("selectMonthButton");
  const monthModal = document.getElementById("monthModal");
  const closeButton = monthModal ? monthModal.querySelector(".close") : null;
  const monthsList = document.getElementById("monthsList");

  if (!selectMonthButton) {
    console.error("Select month button not found");
    return;
  }

  if (!monthModal) {
    console.error("Month modal not found");
    return;
  }

  if (!closeButton) {
    console.warn("Close button not found in the modal");
  }

  if (!monthsList) {
    console.error("Months list not found");
    return;
  }

  // Open modal
  selectMonthButton.addEventListener("click", function () {
    monthModal.style.display = "block";
  });

  // Close modal
  if (closeButton) {
    closeButton.addEventListener("click", function () {
      monthModal.style.display = "none";
    });
  }

  // Close modal when clicking outside
  window.addEventListener("click", function (event) {
    if (event.target === monthModal) {
      monthModal.style.display = "none";
    }
  });

  // Handle month selection
  monthsList.addEventListener("click", function (event) {
    const monthItem = event.target.closest(".month-item");
    if (monthItem) {
      const selectedMonth = monthItem.dataset.month;
      const employeeId = selectMonthButton.dataset.employeeId;
      const companyCode = document.body.dataset.companyCode;

      if (!employeeId || !companyCode) {
        console.error("Employee ID or Company Code not found", {
          employeeId,
          companyCode,
        });
        return;
      }

      // CRITICAL FIX: Force page reload with cache-busting parameter
      const timestamp = Date.now();
      window.location.href = `/clients/${companyCode}/employeeProfile/${employeeId}?selectedMonth=${selectedMonth}&_t=${timestamp}`;
    }
  });

  // Update the button text with the selected month
  function updateSelectedMonthDisplay() {
    const urlParams = new URLSearchParams(window.location.search);
    const selectedMonth = urlParams.get("selectedMonth");
    if (selectedMonth) {
      const formattedDate = new Date(selectedMonth).toLocaleDateString(
        "en-GB",
        {
          day: "2-digit",
          month: "2-digit",
          year: "numeric",
        }
      );
      selectMonthButton.textContent = formattedDate;
    }
  }

  // Call this function when the page loads
  updateSelectedMonthDisplay();
});
