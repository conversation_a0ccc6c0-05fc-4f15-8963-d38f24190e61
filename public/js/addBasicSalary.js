class BasicSalaryForm {
  constructor() {
    console.log("Initializing BasicSalaryForm...");

    // Initialize form elements
    this.form = document.getElementById("basicSalaryForm");
    this.hourlyPaidCheckbox = document.getElementById("hourlyPaid");
    this.rateLabel = document.getElementById("rateLabel");
    this.dontAutoPayDiv = document.getElementById("dontAutoPay");
    this.additionalHoursDiv = document.getElementById("additionalHours");
    this.overrideRateContainer = document.getElementById(
      "overrideRateContainer"
    );
    this.basicSalaryInput = document.getElementById("basicSalary");

    // Get CSRF token
    this.csrfToken = document.querySelector('input[name="_csrf"]')?.value;

    // Initialize submission state
    this.isSubmitting = false;
    this.beforeUnloadHandler = null;

    // Force immediate initialization
    this.initializeFormState();
    this.initializeEventListeners();
  }

  initializeFormState() {
    console.log("Initializing form state");
    const isHourlyPaid = this.hourlyPaidCheckbox?.checked || false;
    console.log("Initial hourlyPaid state:", isHourlyPaid);

    // Force immediate UI update
    this.updateUIForHourlyPaid(isHourlyPaid);

    // Log the state after update
    console.log("UI State after initialization:", {
      hourlyPaidChecked: this.hourlyPaidCheckbox?.checked,
      dontAutoPayVisible: this.dontAutoPayDiv?.style.display,
      additionalHoursVisible: this.additionalHoursDiv?.style.display,
    });
  }

  initializeEventListeners() {
    // Handle form submission
    this.form?.addEventListener("submit", async (e) => {
      e.preventDefault();
      await this.handleSubmit(e);
    });

    // Handle hourly paid checkbox changes
    this.hourlyPaidCheckbox?.addEventListener("change", async (e) => {
      const isHourlyPaid = e.target.checked;
      this.updateUIForHourlyPaid(isHourlyPaid);
      await this.updateHourlyPaidStatus(isHourlyPaid);
    });
  }

  async updateHourlyPaidStatus(isHourlyPaid) {
    try {
      const employeeId = this.getEmployeeId();
      const companyCode = document.body.getAttribute('data-company-code');
      
      console.log('Sending request to update hourly status:', {
        employeeId,
        companyCode,
        hourlyPaid: isHourlyPaid
      });

      const response = await fetch(`/clients/${companyCode}/employee/${employeeId}/hourly-status`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "CSRF-Token": this.csrfToken,
        },
        body: JSON.stringify({
          hourlyPaid: isHourlyPaid,
        }),
      });

      let data;
      const contentType = response.headers.get("content-type");
      if (contentType && contentType.includes("application/json")) {
        data = await response.json();
      } else {
        throw new Error(`Server returned ${response.status} ${response.statusText}`);
      }

      if (!response.ok) {
        throw new Error(data.message || 'Failed to update hourly paid status');
      }

      // Update UI based on the response
      this.updateUIForHourlyPaid(data.hourlyPaid);
      
      // Show success message
      this.showToast(data.message, "success");

      // Update the form placeholder and label
      if (this.basicSalaryInput) {
        this.basicSalaryInput.placeholder = isHourlyPaid ? "Enter hourly rate" : "Enter monthly salary";
      }
    } catch (error) {
      console.error("Error updating hourly paid status:", error);
      this.showToast(error.message || "Failed to update hourly paid status", "error");
      
      // Revert checkbox and UI if update failed
      this.hourlyPaidCheckbox.checked = !isHourlyPaid;
      this.updateUIForHourlyPaid(!isHourlyPaid);
    }
  }

  async handleSubmit(e) {
    e.preventDefault();

    // Prevent multiple submissions
    if (this.isSubmitting) {
      return;
    }

    this.isSubmitting = true;

    // Show loading states
    this.showLoadingState();

    try {
      const formData = new FormData(this.form);
      formData.append(
        "hourlyPaid",
        this.hourlyPaidCheckbox.checked ? "on" : "off"
      );

      // Convert FormData to object and ensure all required fields are present
      const requestData = Object.fromEntries(formData);

      // Explicitly ensure basicSalary is included from the input field
      if (!requestData.basicSalary && this.basicSalaryInput) {
        const basicSalaryValue = this.basicSalaryInput.value;
        if (basicSalaryValue !== null && basicSalaryValue !== undefined && basicSalaryValue !== '') {
          requestData.basicSalary = basicSalaryValue;
        }
      }

      // Explicitly ensure employeeId is included (fallback from URL if needed)
      if (!requestData.employeeId) {
        const employeeId = this.getEmployeeId();
        if (employeeId) {
          requestData.employeeId = employeeId;
        }
      }

      console.log("Form submission data:", requestData);
      console.log("Basic salary input value:", this.basicSalaryInput?.value);
      console.log("Basic salary in request:", requestData.basicSalary);

      const response = await fetch(this.form.action, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "CSRF-Token": this.csrfToken,
        },
        body: JSON.stringify(requestData),
      });

      // Parse the JSON response
      const result = await response.json();

      if (!response.ok || !result.success) {
        throw new Error(result.message || "Failed to save changes");
      }

      console.log("Server response:", result);

      // Show success state
      this.showSuccessState();

      // Show success message with server message
      this.showToast(result.message || "Changes saved successfully", "success");

      // Use the redirect URL from server response or form attribute
      const redirectUrl = result.redirectUrl || this.form.getAttribute("data-redirect-url");

      // Only redirect after confirming successful database save
      if (redirectUrl) {
        console.log("Database save confirmed, redirecting to:", redirectUrl);

        // Remove beforeunload handler before redirect to prevent confirmation dialog
        this.allowNavigation();

        setTimeout(() => {
          window.location.href = redirectUrl;
        }, 1500);
      } else {
        // If no redirect, hide loading after success message
        setTimeout(() => {
          this.hideLoadingState();
        }, 2000);
      }
    } catch (error) {
      console.error("Submission error:", error);

      // Show error message
      this.showToast(error.message || "Failed to save changes", "error");

      // Remove navigation prevention on error
      this.allowNavigation();

      // Hide loading state and restore form
      this.hideLoadingState();
    }
  }

  getEmployeeId() {
    const pathParts = window.location.pathname.split("/");
    return pathParts[pathParts.indexOf("employeeProfile") + 1];
  }

  updateUIForHourlyPaid(isHourlyPaid) {
    console.log("Updating UI for hourlyPaid:", isHourlyPaid);

    // Update label
    if (this.rateLabel) {
      this.rateLabel.textContent = isHourlyPaid
        ? "Hourly Rate:"
        : "Basic Salary:";
    }

    // Update visibility and state of Don't Auto-Pay Public Holidays
    if (this.dontAutoPayDiv) {
      this.dontAutoPayDiv.style.display = isHourlyPaid ? "block" : "none";
      const dontAutoPayCheckbox = document.getElementById(
        "dontAutoPayPublicHolidays"
      );
      if (!isHourlyPaid && dontAutoPayCheckbox) {
        dontAutoPayCheckbox.checked = false;
      }
    }

    // Update visibility and state of Additional Hours
    if (this.additionalHoursDiv) {
      this.additionalHoursDiv.style.display = isHourlyPaid ? "none" : "block";
      if (isHourlyPaid) {
        const additionalHoursCheckbox = document.getElementById(
          "paidForAdditionalHours"
        );
        if (additionalHoursCheckbox) {
          additionalHoursCheckbox.checked = false;
        }

        // Also hide and reset override section
        if (this.overrideRateContainer) {
          this.overrideRateContainer.style.display = "none";
          const overrideCheckbox = document.getElementById(
            "overrideCalculatedHourlyRate"
          );
          if (overrideCheckbox) {
            overrideCheckbox.checked = false;
          }
        }
      }
    }

    // Log final state
    console.log("Final UI state:", {
      hourlyPaidChecked: isHourlyPaid,
      dontAutoPayVisible: this.dontAutoPayDiv?.style.display,
      additionalHoursVisible: this.additionalHoursDiv?.style.display,
    });
  }

  showLoadingState() {
    // Create and show loading overlay
    this.createLoadingOverlay();

    // Disable submit button with loading state
    const submitButton = this.form.querySelector('button[type="submit"]');
    if (submitButton) {
      submitButton.disabled = true;
      submitButton.classList.add('loading');

      // Store original content
      if (!submitButton.dataset.originalContent) {
        submitButton.dataset.originalContent = submitButton.innerHTML;
      }

      // Add loading content with wrapper
      submitButton.innerHTML = '<span class="button-content" style="opacity: 0;"><i class="ph ph-check"></i> Save Changes</span>';
    }

    // Disable form inputs to prevent changes during submission
    const inputs = this.form.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
      if (!input.disabled) {
        input.dataset.wasEnabled = 'true';
        input.disabled = true;
      }
    });

    // Prevent page navigation
    this.preventNavigation();
  }

  showSuccessState() {
    // Update loading overlay to show success
    const loadingOverlay = document.querySelector('.form-loading-overlay');
    if (loadingOverlay) {
      const loadingContent = loadingOverlay.querySelector('.loading-content');
      if (loadingContent) {
        loadingContent.innerHTML = `
          <div style="color: #10b981; font-size: 48px; margin-bottom: 1rem;">
            <i class="ph ph-check-circle"></i>
          </div>
          <div class="loading-text" style="color: #10b981;">Success!</div>
          <div class="loading-subtext">Changes saved successfully</div>
        `;
      }
    }

    // Update submit button to show success
    const submitButton = this.form.querySelector('button[type="submit"]');
    if (submitButton) {
      submitButton.classList.remove('loading');
      submitButton.innerHTML = '<i class="ph ph-check"></i> Saved Successfully';
      submitButton.style.backgroundColor = '#10b981';
    }
  }

  hideLoadingState() {
    // Hide loading overlay
    const loadingOverlay = document.querySelector('.form-loading-overlay');
    if (loadingOverlay) {
      loadingOverlay.classList.remove('show');
      setTimeout(() => {
        loadingOverlay.remove();
      }, 300);
    }

    // Restore submit button
    const submitButton = this.form.querySelector('button[type="submit"]');
    if (submitButton) {
      submitButton.disabled = false;
      submitButton.classList.remove('loading');
      submitButton.style.backgroundColor = '';

      if (submitButton.dataset.originalContent) {
        submitButton.innerHTML = submitButton.dataset.originalContent;
        delete submitButton.dataset.originalContent;
      }
    }

    // Re-enable form inputs
    const inputs = this.form.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
      if (input.dataset.wasEnabled === 'true') {
        input.disabled = false;
        delete input.dataset.wasEnabled;
      }
    });

    // Remove navigation prevention
    this.allowNavigation();

    // Reset submission flag
    this.isSubmitting = false;
  }

  createLoadingOverlay() {
    // Remove existing overlay if present
    const existingOverlay = document.querySelector('.form-loading-overlay');
    if (existingOverlay) {
      existingOverlay.remove();
    }

    // Create new loading overlay
    const overlay = document.createElement('div');
    overlay.className = 'form-loading-overlay';
    overlay.innerHTML = `
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <div class="loading-text">Saving Changes...</div>
        <div class="loading-subtext">Please don't refresh or navigate away</div>
      </div>
    `;

    document.body.appendChild(overlay);

    // Show with animation
    setTimeout(() => {
      overlay.classList.add('show');
    }, 10);
  }

  preventNavigation() {
    // Prevent page refresh and navigation
    this.beforeUnloadHandler = (e) => {
      e.preventDefault();
      e.returnValue = 'Your changes are being saved. Are you sure you want to leave?';
      return 'Your changes are being saved. Are you sure you want to leave?';
    };

    window.addEventListener('beforeunload', this.beforeUnloadHandler);
  }

  allowNavigation() {
    if (this.beforeUnloadHandler) {
      window.removeEventListener('beforeunload', this.beforeUnloadHandler);
      this.beforeUnloadHandler = null;
      console.log('Navigation prevention removed - page can now be left without confirmation');
    }
  }

  showToast(message, type = 'info') {
    // Check if a toast container exists, if not create one
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
      toastContainer = document.createElement('div');
      toastContainer.className = 'toast-container';
      document.body.appendChild(toastContainer);
    }

    // Get appropriate icon
    const icons = {
      success: 'ph-check-circle',
      error: 'ph-x-circle',
      info: 'ph-info',
      warning: 'ph-warning'
    };

    // Create the toast element
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.innerHTML = `
      <i class="ph ${icons[type] || icons.info}"></i>
      <span>${message}</span>
    `;

    // Add the toast to the container
    toastContainer.appendChild(toast);

    // Show with animation
    setTimeout(() => {
      toast.classList.add('show');
    }, 10);

    // Remove the toast after 4 seconds
    setTimeout(() => {
      toast.classList.remove('show');
      setTimeout(() => {
        toast.remove();
        if (toastContainer.children.length === 0) {
          toastContainer.remove();
        }
      }, 300);
    }, 4000);
  }
}

// Initialize form when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  console.log("DOM loaded, initializing BasicSalaryForm");
  const form = new BasicSalaryForm();
});

// Prevent any global function calls
window.handleHourlyPaidCheckbox = function () {
  console.warn(
    "Global handleHourlyPaidCheckbox called - This should not happen"
  );
  return false;
};
