let currentFilingSeason;
let filingSeasons = [];

document.addEventListener("DOMContentLoaded", () => {
  if (!companyCode) {
    console.error("Company code not found");
    alert(
      "Error: Company code not found. Please refresh the page or contact support."
    );
    return;
  }
  console.log("Company code:", companyCode);

  initializeFilingSeasons();
  loadBiAnnualFilingData();
  
  // Remove the JavaScript override for pre-validate button to let the HTML link work
  // The HTML template already has the correct URL with the period parameter
});

function initializeFilingSeasons() {
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth(); // 0-11

  // Get previous year and next year
  const previousYear = currentYear - 1;
  const nextYear = currentYear + 1;

  // Initialize filing seasons array
  filingSeasons = [];

  // Add filing seasons for previous year, current year, and next year
  [previousYear, currentYear, nextYear].forEach(year => {
    // February filing (covering Sep previous year to Feb current year)
    filingSeasons.push({
      id: `${year}-Feb`,
      label: `${year} February (Sep ${year-1} - Feb ${year})`,
      startDate: new Date(year-1, 8, 1), // September previous year
      endDate: new Date(year, 1, 29), // February current year (29 to handle leap years)
      dueDate: new Date(year, 4, 31) // Due by 31 May
    });

    // August filing (covering Mar to Aug current year)
    filingSeasons.push({
      id: `${year}-Aug`,
      label: `${year} August (Mar ${year} - Aug ${year})`,
      startDate: new Date(year, 2, 1), // March
      endDate: new Date(year, 7, 31), // August
      dueDate: new Date(year, 9, 31) // Due by 31 October
    });
  });

  // Sort filing seasons by date (newest first)
  filingSeasons.sort((a, b) => b.startDate - a.startDate);

  // Determine current filing season
  const now = new Date();
  currentFilingSeason = filingSeasons.find(season => 
    now >= season.startDate && now <= season.dueDate
  )?.id || filingSeasons[0].id;

  // Update the dropdown
  const filingSeasonSelect = document.getElementById("filingSeason");
  if (filingSeasonSelect) {
    filingSeasonSelect.innerHTML = filingSeasons.map(season => `
      <option value="${season.id}" ${season.id === currentFilingSeason ? 'selected' : ''}>
        ${season.label}
      </option>
    `).join('');

    // Add event listener for changes
    filingSeasonSelect.addEventListener('change', () => {
      currentFilingSeason = filingSeasonSelect.value;
      updateFilingSeasonDisplay();
    });
  }

  // Initial display update
  updateFilingSeasonDisplay();
}

function updateFilingSeasonDisplay() {
  const selectedSeason = filingSeasons.find(s => s.id === currentFilingSeason);
  if (!selectedSeason) return;

  // Update period display
  const periodDisplay = document.querySelector('.period-display');
  if (periodDisplay) {
    periodDisplay.innerHTML = `
      <div class="period-info">
        <span class="label">Period:</span>
        <span class="value">${selectedSeason.label}</span>
      </div>
      <div class="period-info">
        <span class="label">Due Date:</span>
        <span class="value ${new Date() > selectedSeason.dueDate ? 'overdue' : ''}">
          ${selectedSeason.dueDate.toLocaleDateString('en-ZA', {
            day: 'numeric',
            month: 'long',
            year: 'numeric'
          })}
        </span>
      </div>
    `;
  }

  // Update any other UI elements that depend on the selected season
  const submitButton = document.querySelector('.submit-button');
  if (submitButton) {
    const isOverdue = new Date() > selectedSeason.dueDate;
    submitButton.disabled = isOverdue;
    if (isOverdue) {
      submitButton.title = 'Submission period has ended';
    }
  }
}

function changeFilingSeason() {
  const filingSeason = document.getElementById("filingSeason").value;
  window.location.href = `/clients/${companyCode}/bi-annual-filing?season=${filingSeason}`;
}

async function loadBiAnnualFilingData() {
  try {
    const selectedSeason = filingSeasons.find(s => s.id === currentFilingSeason);
    if (!selectedSeason) {
      showToast('error', 'No filing season selected');
      return;
    }

    // Show loading state
    const tbody = document.querySelector('.modern-table tbody');
    tbody.innerHTML = '<tr><td colspan="7" class="loading">Loading employee data...</td></tr>';

    // Fetch employee data for the selected period
    const response = await fetch(`/clients/${companyCode}/filing/bi-annual/employees?period=${currentFilingSeason}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error('Failed to fetch employee data');
    }

    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to load employee data');
    }

    // Update table with employee data
    if (data.employees && data.employees.length > 0) {
      tbody.innerHTML = data.employees.map(employee => `
        <tr data-employee-id="${employee._id}">
          <td>${employee.firstName}</td>
          <td>${employee.lastName}</td>
          <td>${employee.companyEmployeeNumber || 'Not assigned'}</td>
          <td>${selectedSeason.label}</td>
          <td>Bi-annual</td>
          <td>IRP5</td>
          <td>
            <button class="action-button primary" 
                    onclick="viewCertificate('${employee._id}', '${currentFilingSeason}')">
              <i class="ph ph-eye"></i>
              View
            </button>
          </td>
        </tr>
      `).join('');

      // Update certificate count
      document.getElementById('certificateCount').textContent = data.employees.length;

      // Enable bulk action buttons
      document.querySelectorAll('.bulk-actions-section button').forEach(btn => {
        btn.disabled = false;
      });

    } else {
      tbody.innerHTML = `
        <tr>
          <td colspan="7" class="no-data">
            No employees found for the selected filing season
          </td>
        </tr>
      `;

      // Update certificate count
      document.getElementById('certificateCount').textContent = '0';

      // Disable bulk action buttons
      document.querySelectorAll('.bulk-actions-section button').forEach(btn => {
        btn.disabled = true;
      });
    }

  } catch (error) {
    console.error('Error loading bi-annual filing data:', error);
    const tbody = document.querySelector('.modern-table tbody');
    tbody.innerHTML = `
      <tr>
        <td colspan="7" class="error">
          Error loading employee data: ${error.message}
        </td>
      </tr>
    `;
    showToast('error', 'Failed to load employee data');
  }
}

async function preValidateData() {
  try {
    const response = await fetch(
      `/clients/${companyCode}/filing/bi-annual/pre-validate?period=${currentFilingSeason}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.errors?.join('\n') || 'Validation failed');
    }

    const data = await response.json();

    if (!data.success) {
      let errorMessage = 'Validation failed:\n\n';
      
      if (data.unfinalizedEmployees?.length > 0) {
        errorMessage += 'Unfinalized Payrolls:\n';
        data.unfinalizedEmployees.forEach(emp => {
          errorMessage += `- ${emp.name} (${emp.month})\n`;
        });
      }

      if (data.employeesWithMissingInfo?.length > 0) {
        errorMessage += '\nMissing Tax Information:\n';
        data.employeesWithMissingInfo.forEach(emp => {
          errorMessage += `- ${emp.name} (${emp.missingFields.join(', ')})\n`;
        });
      }

      showToast('error', errorMessage);
      return false;
    }

    showToast('success', 'All validations passed. You can now proceed with the submission.');
    return true;

  } catch (error) {
    console.error('Pre-validation error:', error);
    showToast('error', error.message);
    return false;
  }
}

async function viewCertificate(employeeId, filingSeason) {
  console.log("🎯 Starting IRP5 certificate download:", { employeeId, filingSeason });

  try {
    // First, try to fetch with AJAX to check for errors
    const checkUrl = `/clients/${companyCode}/filing/bi-annual/${filingSeason}/certificate/${employeeId}`;
    console.log("🔍 Checking certificate availability:", checkUrl);

    const response = await fetch(checkUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    });

    console.log("📡 Response status:", response.status);

    if (response.ok) {
      // If successful, the response should be a PDF
      const contentType = response.headers.get('content-type');
      console.log("📄 Content type:", contentType);

      if (contentType && contentType.includes('application/pdf')) {
        // Success! Open the PDF in a new window
        console.log("✅ PDF response received, opening in new window");
        window.open(checkUrl, "_blank");
        showToast('success', 'IRP5 certificate downloaded successfully');
      } else {
        // This might be a JSON error response
        const errorData = await response.json();
        console.error("❌ Unexpected response format:", errorData);
        showToast('error', errorData.error || 'Unexpected response format');
      }
    } else {
      // Handle error responses
      const errorData = await response.json();
      console.error("❌ Certificate download failed:", errorData);

      let errorMessage = errorData.error || 'Failed to generate IRP5 certificate';
      if (errorData.details) {
        console.log("📋 Error details:", errorData.details);
      }

      showToast('error', errorMessage);
    }

  } catch (error) {
    console.error("❌ Error during certificate download:", error);

    // Fallback: try direct download in case of network issues
    console.log("🔄 Attempting fallback direct download");
    try {
      window.open(
        `/clients/${companyCode}/filing/bi-annual/${filingSeason}/certificate/${employeeId}`,
        "_blank"
      );
    } catch (fallbackError) {
      console.error("❌ Fallback download also failed:", fallbackError);
      showToast('error', 'Failed to download IRP5 certificate. Please try again.');
    }
  }
}

async function viewAllCertificates() {
  try {
    const filingSeason = document.getElementById("filingSeason").value;
    const currentButton = event.currentTarget;
    const originalText = currentButton.innerHTML;

    // Show loading state
    currentButton.innerHTML =
      '<i class="ph ph-spinner ph-spin"></i> Generating...';
    currentButton.disabled = true;

    // Get all employee rows that have data-employee-id attribute
    const rows = Array.from(
      document.querySelectorAll("tr[data-employee-id]")
    ).filter((row) => row.dataset.employeeId);
    const totalCertificates = rows.length;

    if (totalCertificates === 0) {
      showToast("warning", "No certificates to generate");
      currentButton.innerHTML = originalText;
      currentButton.disabled = false;
      return;
    }

    // Create a progress element
    const progressDiv = document.createElement("div");
    progressDiv.className = "progress-bar";
    progressDiv.innerHTML = `<div class="progress-text">Generating 0/${totalCertificates} certificates...</div>`;
    currentButton.parentNode.insertBefore(
      progressDiv,
      currentButton.nextSibling
    );

    // Generate all certificates
    const pdfs = [];
    let completed = 0;
    let errors = 0;

    for (const row of rows) {
      const employeeId = row.dataset.employeeId;
      if (!employeeId) continue; // Skip if no employee ID

      try {
        const response = await fetch(
          `/clients/${companyCode}/filing/bi-annual/${filingSeason}/certificate/${employeeId}`,
          {
            method: "GET",
            headers: {
              Accept: "application/pdf",
            },
          }
        );

        if (!response.ok) {
          throw new Error(
            `Failed to generate certificate for employee ${employeeId}`
          );
        }

        const pdfBlob = await response.blob();
        pdfs.push({
          blob: pdfBlob,
          employeeNumber: row
            .querySelector("td:nth-child(3)")
            .textContent.trim(),
        });

        // Update progress
        completed++;
        progressDiv.innerHTML = `
          <div class="progress-text">
            Generating ${completed}/${totalCertificates} certificates...
            ${errors > 0 ? `(${errors} errors)` : ""}
          </div>`;
      } catch (error) {
        console.error(
          `Error generating certificate for employee ${employeeId}:`,
          error
        );
        errors++;
      }
    }

    if (pdfs.length === 0) {
      throw new Error("No certificates were generated successfully");
    }

    // Merge PDFs using PDF-Lib
    const mergedPdf = await mergePDFs(pdfs);

    // Download the merged PDF
    download(
      mergedPdf,
      `Tax_Certificates_${filingSeason}.pdf`,
      "application/pdf"
    );

    // Cleanup
    progressDiv.remove();

    // Show completion message
    showToast(
      errors > 0 ? "warning" : "success",
      `Generated ${completed - errors} certificates${
        errors > 0 ? ` (${errors} failed)` : ""
      }`
    );

    // Restore button state
    currentButton.innerHTML = originalText;
    currentButton.disabled = false;
  } catch (error) {
    console.error("Error generating certificates:", error);
    showToast("error", `Failed to generate certificates: ${error.message}`);
    if (currentButton) {
      currentButton.innerHTML = originalText;
      currentButton.disabled = false;
    }
  }
}

// Updated mergePDFs function
async function mergePDFs(pdfs) {
  const { PDFDocument } = PDFLib;
  const mergedPdf = await PDFDocument.create();

  for (const pdf of pdfs) {
    const pdfBytes = await pdf.blob.arrayBuffer();
    const pdfDoc = await PDFDocument.load(pdfBytes);
    const pages = await mergedPdf.copyPages(pdfDoc, pdfDoc.getPageIndices());
    pages.forEach((page) => mergedPdf.addPage(page));
  }

  return new Blob([await mergedPdf.save()], { type: "application/pdf" });
}

// Add a toast notification function if not already present
function showToast(type, message) {
  // Prevent XSS by escaping HTML
  message = message.replace(/</g, '&lt;').replace(/>/g, '&gt;');
  
  // Create toast element
  const toast = document.createElement("div");
  toast.className = `toast toast-${type}`;
  
  // Set icon based on type
  let icon;
  switch(type) {
    case "success":
      icon = "ph-check-circle";
      break;
    case "error":
      icon = "ph-x-circle";
      break;
    case "warning":
      icon = "ph-warning-circle";
      break;
    case "info":
    default:
      icon = "ph-info";
      break;
  }
  
  toast.innerHTML = `
    <i class="ph ${icon}"></i>
    <span>${message}</span>
  `;
  
  // Add to DOM
  document.body.appendChild(toast);
  
  // Position toasts to stack
  const existingToasts = document.querySelectorAll('.toast');
  if (existingToasts.length > 1) {
    const offset = Array.from(existingToasts)
      .slice(0, -1)
      .reduce((acc, t) => acc + t.offsetHeight + 10, 0);
    toast.style.top = `${20 + offset}px`;
  }
  
  // Remove after delay
  setTimeout(() => {
    toast.classList.add('fade-out');
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
        // Reposition remaining toasts
        repositionToasts();
      }
    }, 500);
  }, 5000);
}

// Helper function to reposition toasts after one is removed
function repositionToasts() {
  const toasts = document.querySelectorAll('.toast');
  toasts.forEach((toast, index) => {
    toast.style.top = `${20 + (index * (toast.offsetHeight + 10))}px`;
  });
}

function exportToEasyfile() {
  const filingSeason = document.getElementById("filingSeason").value;
  window.location.href = `/clients/${companyCode}/filing/bi-annual/${filingSeason}/export`;
}

function generateEMP501() {
  const filingSeason = document.getElementById("filingSeason").value;
  window.location.href = `/clients/${companyCode}/filing/bi-annual/${filingSeason}/emp501`;
}
