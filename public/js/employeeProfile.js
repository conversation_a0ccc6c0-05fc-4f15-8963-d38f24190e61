// Simple tooltip class
class Tooltip {
  constructor(element, options = {}) {
    this.element = element;
    this.options = {
      placement: options.placement || "top",
      trigger: options.trigger || "hover",
    };
    this.init();
  }

  init() {
    if (this.options.trigger === "hover") {
      this.element.addEventListener("mouseenter", () => this.show());
      this.element.addEventListener("mouseleave", () => this.hide());
    }
  }

  show() {
    // Implementation of show tooltip
  }

  hide() {
    // Implementation of hide tooltip
  }
}

// Move pagination functions to global scope
window.currentPage = 1;
window.totalPages = 4;

window.updatePageIndicator = function () {
  const pageIndicator = document.querySelector(".page-indicator");
  if (pageIndicator) {
    pageIndicator.textContent = `${currentPage} of ${totalPages}`;
  }
};

window.showPage = function (pageNum) {
  // Hide all pages
  document.querySelectorAll(".calc-page").forEach((page) => {
    page.style.display = "none";
  });

  // Show the selected page
  const selectedPage = document.querySelector(
    `.calc-page[data-page="${pageNum}"]`
  );
  if (selectedPage) {
    selectedPage.style.display = "block";
    selectedPage.style.animation = "fadeIn 0.3s ease-out";
  }

  // Update navigation buttons
  const prevButton = document.querySelector("#prevPageBtn");
  const nextButton = document.querySelector("#nextPageBtn");

  if (prevButton) prevButton.disabled = pageNum === 1;
  if (nextButton) nextButton.disabled = pageNum === totalPages;
};

window.prevPage = function () {
  if (currentPage > 1) {
    currentPage--;
    showPage(currentPage);
    updatePageIndicator();
  }
};

window.nextPage = function () {
  if (currentPage < totalPages) {
    currentPage++;
    showPage(currentPage);
    updatePageIndicator();
  }
};

// Initialize the calculator
function initializeCalculator() {
  showPage(1);
  updatePageIndicator();
}

// Error message display function
function showError(message) {
  const errorDiv = document.createElement("div");
  errorDiv.className = "error-message";
  errorDiv.innerHTML = `
    <i class="ph ph-warning"></i>
    <span>${message}</span>
  `;

  document.body.appendChild(errorDiv);

  // Remove after 3 seconds
  setTimeout(() => {
    errorDiv.classList.add("fade-out");
    setTimeout(() => errorDiv.remove(), 300);
  }, 3000);
}

// Status update function
function updateStatusStyle(element, status) {
  if (!element) return;
  
  const statusClasses = {
    Active: "active",
    Inactive: "inactive",
    Suspended: "suspended",
    Terminated: "terminated"
  };

  element.className = `employee-status ${statusClasses[status] || ""}`;
}

// Payroll date validation functions
function validatePayrollPeriod(periodDate, terminationDate) {
  if (!terminationDate) return true;

  const periodEnd = new Date(periodDate);
  const lastDay = new Date(terminationDate);

  return periodEnd <= lastDay;
}

// Main DOMContentLoaded event handler
document.addEventListener("DOMContentLoaded", function () {
  // Initialize calculator
  initializeCalculator();

  // Month modal handling
  const monthModal = document.getElementById('monthModal');
  const selectMonthButton = document.getElementById('selectMonthButton');
  
  if (monthModal && selectMonthButton) {
    const closeBtn = monthModal.querySelector(".close");
    const periodsList = document.getElementById("periodsList");

    selectMonthButton.addEventListener("click", function () {
      monthModal.style.display = "block";
    });

    if (closeBtn) {
      closeBtn.addEventListener("click", function () {
        monthModal.style.display = "none";
      });
    }

    if (periodsList) {
      periodsList.addEventListener("click", function (e) {
        if (e.target && e.target.matches("li.period-item")) {
          const periodStart = e.target.dataset.periodStart;
          const periodEnd = e.target.dataset.periodEnd;
          const employeeId = selectMonthButton.dataset.employeeId;
          // Handle period selection...
        }
      });
    }
  }

  // CRITICAL FIX: Force page refresh after any salary updates to ensure data sync
  const updateSalaryForm = document.getElementById("updateSalaryForm");
  if (updateSalaryForm) {
    updateSalaryForm.addEventListener("submit", async function (e) {
      e.preventDefault();
      // Existing salary update code...

      // Force page refresh with cache-busting parameter after form submission
      setTimeout(() => {
        const url = new URL(window.location);
        url.searchParams.set('_refresh', Date.now());
        window.location.href = url.toString();
      }, 1000);
    });
  }

  // Function to get CSRF token from meta tag
  function getCsrfToken() {
    return document.querySelector('meta[name="csrf-token"]')?.content;
  }

  // Delete employee functionality
  function initializeDeleteEmployee() {
    const deleteEmployeeBtn = document.getElementById('deleteEmployeeBtn');
    if (deleteEmployeeBtn) {
      const employeeId = deleteEmployeeBtn.dataset.employeeId;
      const companyCode = document.body.dataset.companyCode;
      
      deleteEmployeeBtn.addEventListener('click', async function(event) {
        event.preventDefault();
        
        if (confirm('Are you sure you want to delete this employee? This action cannot be undone.')) {
          try {
            const response = await fetch(`/clients/${companyCode}/employeeProfile/${employeeId}/delete`, {
              method: 'DELETE',
              headers: {
                'Content-Type': 'application/json',
                'CSRF-Token': getCsrfToken()
              },
            });

            const data = await response.json();
            
            if (data.success) {
              alert('Employee deleted successfully');
              window.location.href = data.redirectUrl;
            } else {
              alert('Failed to delete employee: ' + data.message);
            }
          } catch (error) {
            console.error('Delete error:', error);
            alert('An error occurred while deleting the employee');
          }
        }
      });
    }
  }

  initializeDeleteEmployee();

  // Add CSRF token to all fetch requests
  const csrfToken = getCsrfToken();
  if (csrfToken) {
    const originalFetch = window.fetch;
    window.fetch = function() {
      let [resource, config] = arguments;
      if (config === undefined) {
        config = {};
      }
      if (config.headers === undefined) {
        config.headers = {};
      }
      if (!(config.headers instanceof Headers)) {
        config.headers = new Headers(config.headers);
      }
      config.headers.set('CSRF-Token', csrfToken);
      return originalFetch(resource, config);
    };
  }

  // Period items hover effect
  const periodItems = document.querySelectorAll('.period-item');
  periodItems.forEach(item => {
    item.addEventListener('mouseenter', function() {
      this.style.transform = 'translateX(5px)';
    });
    item.addEventListener('mouseleave', function() {
      this.style.transform = 'translateX(0)';
    });
  });

  // Initialize tooltips
  const tooltipElements = document.querySelectorAll("[title]");
  tooltipElements.forEach((element) => {
    new Tooltip(element, {
      placement: "top",
      trigger: "hover",
    });
  });

  // Add styles for disabled periods and error messages
  const style = document.createElement("style");
  style.textContent = `
    .period-item.disabled {
      opacity: 0.6;
      cursor: not-allowed;
      background-color: #f5f5f5;
    }
    
    .period-status.terminated {
      color: #dc3545;
      font-weight: bold;
    }
    
    .period-item.disabled:hover {
      transform: none !important;
    }
    
    .error-message {
      position: fixed;
      top: 20px;
      right: 20px;
      background-color: #dc3545;
      color: white;
      padding: 12px 20px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      gap: 8px;
      z-index: 1000;
      animation: slideIn 0.3s ease-out;
    }
    
    .error-message.fade-out {
      animation: fadeOut 0.3s ease-out;
    }
    
    @keyframes slideIn {
      from { transform: translateX(100%); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes fadeOut {
      from { opacity: 1; }
      to { opacity: 0; }
    }
  `;
  document.head.appendChild(style);
});

// Update the existing month selection modal code
const monthModal = document.getElementById("monthModal");
if (monthModal) {
  const closeBtn = monthModal.querySelector(".close");
  const periodsList = monthModal.querySelector("#periodsList");
  const terminationDate = document.body.dataset.terminationDate;

  // Add termination date validation to period selection
  periodsList.addEventListener("click", function (e) {
    const periodItem = e.target.closest(".period-item");
    if (!periodItem) return;

    const periodDate = periodItem.dataset.period;
    if (!validatePayrollPeriod(periodDate, terminationDate)) {
      e.preventDefault();
      showError("Cannot select payroll period beyond termination date");
      return;
    }

    // Existing period selection logic...
  });
}

function updateStatusStyle(element, status) {
  // Remove all status classes
  element.classList.remove("status-active", "status-inactive", "status-notice");

  // Add appropriate class based on status
  switch (status.toLowerCase()) {
    case "active":
      element.classList.add("status-active");
      break;
    case "inactive":
      element.classList.add("status-inactive");
      break;
    case "serving notice":
      element.classList.add("status-notice");
      break;
  }
}

// Tab Management
function showTab(tabName) {
  // Remove active class from all tabs
  document.querySelectorAll(".tab-button").forEach((tab) => {
    tab.classList.remove("active");
  });

  // Add active class to clicked tab
  event.currentTarget.classList.add("active");

  // Handle tab content visibility
  // Add your tab content logic here
}

// Dropdown Management
function toggleDropdown(event) {
  event.stopPropagation();
  const dropdown = document.getElementById("dropdownContent");
  const button = event.currentTarget;

  // Close any other open dropdowns
  document.querySelectorAll(".dropdown-content").forEach((content) => {
    if (content !== dropdown) content.classList.remove("show");
  });

  // Toggle current dropdown
  dropdown.classList.toggle("show");
  button.classList.toggle("active");

  // Add click outside listener
  if (dropdown.classList.contains("show")) {
    document.addEventListener("click", closeDropdown);
  }
}

function closeDropdown(event) {
  if (
    !event.target.matches(".dropdown-content") &&
    !event.target.matches(".tab-button")
  ) {
    const dropdowns = document.getElementsByClassName("dropdown-content");
    const buttons = document.querySelectorAll(".dropdown .tab-button");

    Array.from(dropdowns).forEach((dropdown) => {
      dropdown.classList.remove("show");
    });

    buttons.forEach((button) => {
      button.classList.remove("active");
    });
    document.removeEventListener("click", closeDropdown);
  }
}

// Coming Soon Alert
function showComingSoon() {
  const toast = document.createElement("div");
  toast.className = "toast-notification";
  toast.innerHTML = `
    <i class="ph ph-info"></i>
    <span>Leave management coming soon!</span>
  `;

  document.body.appendChild(toast);

  // Remove toast after 3 seconds
  setTimeout(() => {
    toast.classList.add("fade-out");
    setTimeout(() => toast.remove(), 300);
  }, 3000);
}

// Delete Employee Confirmation
function confirmDeleteEmployee(employeeId) {
  const modal = document.createElement("div");
  modal.className = "confirmation-modal";
  modal.innerHTML = `
    <div class="modal-content">
      <i class="ph ph-warning-circle"></i>
      <h3>Delete Employee</h3>
      <p>Are you sure you want to delete this employee? This action cannot be undone.</p>
      <div class="modal-actions">
        <button onclick="closeModal()" class="btn-secondary">Cancel</button>
        <button onclick="deleteEmployee('${employeeId}')" class="btn-danger">Delete</button>
      </div>
    </div>
  `;

  document.body.appendChild(modal);
}

// JavaScript Function to load the payslip for the selected month
function loadPayslip() {
  const selectedMonth = document.getElementById("payslipMonths").value;
  if (selectedMonth) {
    // TODO: Implement the function to load the payslip for the selected month
    // Example: Use fetch API to make an AJAX call to the server
  }
}

// JavaScript Function to finalise the payroll
function finalisePayroll() {
  // TODO: Implement the function to finalise the payroll
  // Example: Use fetch API to make a POST request to the server
}

// If there are buttons to load payslip and finalize payroll,
// attach event listeners to them.
const loadPayslipButton = document.getElementById("loadPayslipButtonId");
if (loadPayslipButton) {
  loadPayslipButton.addEventListener("click", loadPayslip);
}

const finalisePayrollButton = document.getElementById(
  "finalisePayrollButtonId"
);
if (finalisePayrollButton) {
  finalisePayrollButton.addEventListener("click", finalisePayroll);
}

const selectMonthButton = document.getElementById("selectMonthButton");
const closeBtn = document.getElementById('monthModal')?.querySelector(".close");
const periodsList = document.getElementById("periodsList");

selectMonthButton.addEventListener("click", function () {
  document.getElementById('monthModal').style.display = "block";
});

if (closeBtn) {
  closeBtn.addEventListener("click", function () {
    document.getElementById('monthModal').style.display = "none";
  });
}

if (periodsList) {
  periodsList.addEventListener("click", function (e) {
    if (e.target && e.target.matches("li.period-item")) {
      const periodStart = e.target.dataset.periodStart;
      const periodEnd = e.target.dataset.periodEnd;
      const employeeId = selectMonthButton.dataset.employeeId;

      // Update the button text
      selectMonthButton.textContent = `${moment(periodStart).format(
        "DD/MM/YYYY"
      )} - ${moment(periodEnd).format("DD/MM/YYYY")}`;

      // Update the hidden input
      document.getElementById("selectedMonth").value =
        moment(periodEnd).format("YYYY-MM-DD");

      // Close the modal
      document.getElementById('monthModal').style.display = "none";

      // Reload the page with the new period
      window.location.href = `/employeeProfile/${employeeId}?month=${moment(
        periodEnd
      ).format("YYYY-MM-DD")}`;
    }
  });
}

// Add event listeners for pagination buttons
const prevButton = document.querySelector("#prevPageBtn");
const nextButton = document.querySelector("#nextPageBtn");

if (prevButton) {
  prevButton.addEventListener("click", window.prevPage);
}

if (nextButton) {
  nextButton.addEventListener("click", window.nextPage);
}

// Status management
const statusElement = document.querySelector(".employee-status");
if (statusElement) {
  const status = statusElement.dataset.status;
  updateStatusStyle(statusElement, status);
}

// Update the period selection handler
document.addEventListener("DOMContentLoaded", function () {
  const periodsList = document.getElementById("periodsList");
  const selectMonthButton = document.getElementById("selectMonthButton");
  const finaliseForm = document.getElementById("finaliseForm");

  // Get termination date if it exists
  const terminationDate = document.body.dataset.terminationDate;

  if (periodsList) {
    // Disable periods beyond termination date
    const periodItems = periodsList.querySelectorAll(".period-item");
    periodItems.forEach((item) => {
      const periodDate = item.dataset.period;

      if (!validatePayrollPeriod(periodDate, terminationDate)) {
        item.classList.add("disabled");
        item.title = "Period is beyond termination date";

        // Add visual indicator
        const statusSpan = item.querySelector(".period-status");
        if (statusSpan) {
          statusSpan.textContent = "Beyond Termination";
          statusSpan.classList.add("terminated");
        }
      }
    });

    // Add click handler with validation
    periodsList.addEventListener("click", function (e) {
      const periodItem = e.target.closest(".period-item");
      if (!periodItem || periodItem.classList.contains("disabled")) {
        e.preventDefault();
        if (periodItem?.classList.contains("disabled")) {
          showError("Cannot select payroll period beyond termination date");
        }
        return;
      }

      const periodDate = periodItem.dataset.period;
      if (!validatePayrollPeriod(periodDate, terminationDate)) {
        e.preventDefault();
        showError("Cannot select payroll period beyond termination date");
        return;
      }
    });
  }

  // Add validation to finalise form
  if (finaliseForm) {
    finaliseForm.addEventListener("submit", function (e) {
      const currentPeriodEnd = this.querySelector(
        '[name="currentPeriodEndDate"]'
      ).value;

      if (!validatePayrollPeriod(currentPeriodEnd, terminationDate)) {
        e.preventDefault();
        showError("Cannot process payroll beyond termination date");
        return false;
      }
    });
  }

  // Error message display function
  function showError(message) {
    const errorDiv = document.createElement("div");
    errorDiv.className = "error-message";
    errorDiv.innerHTML = `
      <i class="ph ph-warning"></i>
      <span>${message}</span>
    `;

    document.body.appendChild(errorDiv);

    // Remove after 3 seconds
    setTimeout(() => {
      errorDiv.classList.add("fade-out");
      setTimeout(() => errorDiv.remove(), 300);
    }, 3000);
  }

  // Add styles for disabled periods and error messages
  const style = document.createElement("style");
  style.textContent = `
    .period-item.disabled {
      opacity: 0.6;
      cursor: not-allowed;
      background-color: #f5f5f5;
    }
    
    .period-status.terminated {
      color: #dc3545;
      font-weight: bold;
    }
    
    .period-item.disabled:hover {
      transform: none !important;
    }
    
    .error-message {
      position: fixed;
      top: 20px;
      right: 20px;
      background-color: #dc3545;
      color: white;
      padding: 12px 20px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      gap: 8px;
      z-index: 1000;
      animation: slideIn 0.3s ease-out;
    }
    
    .error-message.fade-out {
      animation: fadeOut 0.3s ease-out;
    }
    
    @keyframes slideIn {
      from { transform: translateX(100%); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes fadeOut {
      from { opacity: 1; }
      to { opacity: 0; }
    }
  `;
  document.head.appendChild(style);
});

window.showTab = function (tabName) {
  // Implement your tab switching logic here
  console.log("Switching to tab:", tabName);
};

window.toggleDropdown = function () {
  const dropdownContent = document.getElementById("dropdownContent");
  if (dropdownContent) {
    dropdownContent.classList.toggle("show");
  }
};

window.confirmDownloadPayslip = function (
  employeeId,
  periodEndDate,
  isFinalized
) {
  console.log("Download params:", { employeeId, periodEndDate, isFinalized });

  if (!isFinalized) {
    if (
      !confirm(
        "This period is not finalized. The payslip will be marked as DRAFT. Do you want to continue?"
      )
    ) {
      return;
    }
  }

  try {
    // Get companyCode from the data attribute on body
    const companyCode = document.body.dataset.companyCode;
    if (!companyCode) {
      console.error("Company code not found");
      return;
    }

    // Format the date to YYYY-MM-DD
    const formattedDate = new Date(periodEndDate).toISOString().split("T")[0];

    // Construct the download URL with company code
    const downloadUrl = `/clients/${companyCode}/payslip/download/${employeeId}/${formattedDate}`;
    console.log("Download URL:", downloadUrl);

    // Create a temporary link element
    const link = document.createElement("a");
    link.href = downloadUrl;
    link.target = "_blank"; // Open in new tab
    link.style.display = "none";
    document.body.appendChild(link);

    // Trigger click and remove the element
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    console.error("Error downloading payslip:", error);
    alert("Error downloading payslip. Please try again.");
  }
};
