document.addEventListener("DOMContentLoaded", function () {
  // Get DOM elements
  const statusFilter = document.getElementById("statusFilter");
  const searchInput = document.getElementById("searchInputEmployee");
  const tableBody = document.querySelector(".modern-table tbody");
  const rows = Array.from(tableBody.getElementsByTagName("tr"));
  const rowsPerPage = 10; // Number of rows per page
  let currentPage = 1;

  // Store original rows for reset
  const originalRows = [...rows];

  function updatePaginationInfo() {
    const visibleRows = rows.filter((row) => row.style.display !== "none");
    const totalRows = visibleRows.length;
    const start = (currentPage - 1) * rowsPerPage + 1;
    const end = Math.min(start + rowsPerPage - 1, totalRows);

    // Update pagination info text
    document.querySelector(".pagination-info").textContent =
      totalRows === 0
        ? "No entries to show"
        : `Showing ${start} to ${end} of ${totalRows} entries`;

    // Update pagination buttons
    const totalPages = Math.ceil(totalRows / rowsPerPage);
    document.getElementById("prevButton").disabled = currentPage === 1;
    document.getElementById("nextButton").disabled = currentPage >= totalPages;

    // Show/hide rows based on current page
    visibleRows.forEach((row, index) => {
      row.style.display =
        index >= (currentPage - 1) * rowsPerPage &&
        index < currentPage * rowsPerPage
          ? ""
          : "none";
    });
  }

  function filterTable() {
    currentPage = 1; // Reset to first page when filtering
    const searchTerm = searchInput.value.toLowerCase();
    const selectedStatus = statusFilter.value;

    originalRows.forEach((row) => {
      const name = row
        .querySelector(".employee-name")
        .textContent.toLowerCase();
      const employeeNumber = row.cells[0].textContent.toLowerCase();
      const email = row.cells[2].textContent.toLowerCase();
      const status = row.querySelector(".status-badge").textContent.trim();

      const matchesSearch =
        name.includes(searchTerm) ||
        employeeNumber.includes(searchTerm) ||
        email.includes(searchTerm);

      const matchesStatus =
        selectedStatus === "all" ||
        (selectedStatus === "Active" && status === "Enabled") ||
        (selectedStatus === "Inactive" && status === "Disabled");

      row.style.display = matchesSearch && matchesStatus ? "" : "none";
    });

    updatePaginationInfo();
  }

  // Pagination functions
  window.prevPage = function () {
    if (currentPage > 1) {
      currentPage--;
      updatePaginationInfo();
    }
  };

  window.nextPage = function () {
    const visibleRows = rows.filter((row) => row.style.display !== "none");
    const totalPages = Math.ceil(visibleRows.length / rowsPerPage);
    if (currentPage < totalPages) {
      currentPage++;
      updatePaginationInfo();
    }
  };

  // Add event listeners
  if (statusFilter) {
    statusFilter.addEventListener("change", filterTable);
  }

  if (searchInput) {
    let searchTimeout;
    searchInput.addEventListener("input", function () {
      clearTimeout(searchTimeout);
      searchTimeout = setTimeout(filterTable, 300);
    });
  }

  // Initialize table
  filterTable();
});

// Add companyCode from URL
const companyCode = window.location.pathname.split("/")[2];
console.log("Initialized with companyCode:", companyCode);

// Add loading overlay function
function showLoading(button) {
  // Disable the button and add loading state
  button.disabled = true;
  const originalContent = button.innerHTML;
  button.innerHTML = `
    <div class="button-loader">
      <i class="ph ph-circle-notch"></i>
      Processing...
    </div>
  `;
  button.classList.add("loading");
  return originalContent;
}

function hideLoading(button, originalContent) {
  // Re-enable the button and restore original content
  button.disabled = false;
  button.innerHTML = originalContent;
  button.classList.remove("loading");
}

// Self Service Actions
async function enableSelfService(employeeId) {
  const button = event.currentTarget;

  // Show confirmation modal for enable action too
  showSelfServiceModal(
    'enable',
    'Enable Self Service',
    'Are you sure you want to enable self-service for this employee? They will receive a setup email to access their employee portal.',
    async () => {
      // This function will be called when user confirms
      const originalContent = showLoading(button);
      await performEnableSelfService(employeeId, button, originalContent);
    }
  );
}

async function performEnableSelfService(employeeId, button, originalContent) {
  try {
    const csrfToken = document.querySelector('meta[name="csrf-token"]').content;
    const url = `/clients/${companyCode}/employeeManagement/selfService/enable/${employeeId}`;

    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "CSRF-Token": csrfToken,
      },
    });

    // Check if response is JSON
    const contentType = response.headers.get("content-type");
    let data;

    if (contentType && contentType.includes("application/json")) {
      data = await response.json();
    } else {
      // Handle non-JSON responses (like 404 HTML pages)
      const text = await response.text();
      console.error("Non-JSON response received:", text);
      data = {
        success: false,
        message: `Server error: ${response.status} ${response.statusText}`,
      };
    }

    if (data.success) {
      showNotification(
        "success",
        "Self-service enabled successfully. Setup email sent."
      );
      setTimeout(() => window.location.reload(), 1500);
    } else {
      hideLoading(button, originalContent);
      showNotification(
        "error",
        data.message || "Failed to enable self service"
      );
    }
  } catch (error) {
    console.error("Error in performEnableSelfService:", error);
    hideLoading(button, originalContent);
    showNotification("error", "An error occurred while enabling self service");
  }
}

async function disableSelfService(employeeId) {
  const button = event.currentTarget;

  // Show confirmation modal instead of browser confirm
  showSelfServiceModal(
    'disable',
    'Disable Self Service',
    'Are you sure you want to disable self-service for this employee? They will no longer be able to access their employee portal.',
    async () => {
      // This function will be called when user confirms
      const originalContent = showLoading(button);
      await performDisableSelfService(employeeId, button, originalContent);
    }
  );
}

async function performDisableSelfService(employeeId, button, originalContent) {

  try {
    const csrfToken = document.querySelector('meta[name="csrf-token"]').content;
    console.log("CSRF Token found:", !!csrfToken);

    const url = `/clients/${companyCode}/employeeManagement/selfService/disable/${employeeId}`;
    console.log("Making request to:", url);

    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "CSRF-Token": csrfToken,
      },
    });

    console.log("Response status:", response.status);

    // Check if response is JSON
    const contentType = response.headers.get("content-type");
    let data;

    if (contentType && contentType.includes("application/json")) {
      data = await response.json();
    } else {
      // Handle non-JSON responses (like 404 HTML pages)
      const text = await response.text();
      console.error("Non-JSON response received:", text);
      data = {
        success: false,
        message: `Server error: ${response.status} ${response.statusText}`,
      };
    }

    console.log("Response data:", data);

    if (data.success) {
      showNotification("success", "Self-service disabled successfully");
      console.log("Operation successful, reloading page in 1.5s");
      setTimeout(() => window.location.reload(), 1500);
    } else {
      console.error("Operation failed:", data.message);
      hideLoading(button, originalContent);
      showNotification(
        "error",
        data.message || "Failed to disable self service"
      );
    }
  } catch (error) {
    console.error("Error in performDisableSelfService:", error);
    hideLoading(button, originalContent);
    showNotification("error", "An error occurred while disabling self service");
  }
}

async function resendInvite(employeeId) {
  const button = event.currentTarget;
  const originalContent = showLoading(button);

  try {
    const csrfToken = document.querySelector('meta[name="csrf-token"]').content;
    console.log("CSRF Token found:", !!csrfToken);

    const url = `/clients/${companyCode}/employeeManagement/selfService/resend/${employeeId}`;
    console.log("Making request to:", url);

    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "CSRF-Token": csrfToken,
      },
    });

    console.log("Response status:", response.status);

    // Check if response is JSON
    const contentType = response.headers.get("content-type");
    let data;

    if (contentType && contentType.includes("application/json")) {
      data = await response.json();
    } else {
      // Handle non-JSON responses (like 404 HTML pages)
      const text = await response.text();
      console.error("Non-JSON response received:", text);
      data = {
        success: false,
        message: `Server error: ${response.status} ${response.statusText}`,
      };
    }

    console.log("Response data:", data);

    if (data.success) {
      showNotification("success", "Setup email resent successfully");
      hideLoading(button, originalContent);
    } else {
      hideLoading(button, originalContent);
      showNotification("error", data.message || "Failed to resend setup email");
    }
  } catch (error) {
    console.error("Error in resendInvite:", error);
    hideLoading(button, originalContent);
    showNotification(
      "error",
      "An error occurred while resending the setup email"
    );
  }
}

// Notification helper with logging
function showNotification(type, message) {
  console.log(`Showing notification - Type: ${type}, Message: ${message}`);

  const notification = document.createElement("div");
  notification.className = `notification ${type}`;
  notification.textContent = message;
  document.body.appendChild(notification);

  console.log("Notification element created and added to DOM");

  setTimeout(() => {
    notification.remove();
    console.log("Notification removed from DOM");
  }, 3000);
}

// Add initialization logging
document.addEventListener("DOMContentLoaded", () => {
  console.log("DOM fully loaded");
  console.log("Current pathname:", window.location.pathname);
  console.log("Extracted companyCode:", companyCode);

  // Log initial table state
  const tableBody = document.querySelector(".modern-table tbody");
  console.log(
    "Table rows found:",
    tableBody?.getElementsByTagName("tr").length
  );

  // Log CSRF token presence
  const csrfToken = document.querySelector('meta[name="csrf-token"]');
  console.log("CSRF token meta tag present:", !!csrfToken);
});

// Self Service Modal Functions
function showSelfServiceModal(action, title, message, onConfirm) {
  const modal = document.getElementById('selfServiceModal');
  const modalTitle = document.getElementById('modalTitle');
  const modalMessage = document.getElementById('modalMessage');
  const modalIcon = document.getElementById('modalIcon');
  const modalConfirmBtn = document.getElementById('modalConfirmBtn');

  if (!modal) {
    console.error('Self service modal not found');
    return;
  }

  // Set modal content
  modalTitle.textContent = title;
  modalMessage.textContent = message;

  // Set icon and button styling based on action
  if (action === 'disable') {
    modalIcon.className = 'ph ph-warning-circle disable';
    modalConfirmBtn.className = 'btn btn-danger';
    modalConfirmBtn.textContent = 'Disable';
  } else if (action === 'enable') {
    modalIcon.className = 'ph ph-check-circle enable';
    modalConfirmBtn.className = 'btn btn-success';
    modalConfirmBtn.textContent = 'Enable';
  }

  // Remove any existing event listeners
  const newConfirmBtn = modalConfirmBtn.cloneNode(true);
  modalConfirmBtn.parentNode.replaceChild(newConfirmBtn, modalConfirmBtn);

  // Add new event listener
  newConfirmBtn.addEventListener('click', () => {
    closeSelfServiceModal();
    onConfirm();
  });

  // Show modal with animation
  modal.style.display = 'flex';
  document.body.style.overflow = 'hidden';

  // Trigger animation
  setTimeout(() => {
    modal.classList.add('show');
  }, 10);
}

function closeSelfServiceModal() {
  const modal = document.getElementById('selfServiceModal');
  if (!modal) return;

  // Hide with animation
  modal.classList.remove('show');

  // Remove from DOM after animation
  setTimeout(() => {
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
  }, 300);
}

// Close modal when clicking outside
document.addEventListener('click', function(event) {
  const modal = document.getElementById('selfServiceModal');
  if (modal && event.target === modal) {
    closeSelfServiceModal();
  }
});

// Close modal with ESC key
document.addEventListener('keydown', function(event) {
  if (event.key === 'Escape') {
    const modal = document.getElementById('selfServiceModal');
    if (modal && modal.style.display === 'flex') {
      closeSelfServiceModal();
    }
  }
});

// Add notification styles
const style = document.createElement("style");
style.textContent = `
    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 24px;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        z-index: 1000;
        animation: slideIn 0.3s ease-out;
    }

    .notification.success {
        background: var(--success-color);
    }

    .notification.error {
        background: var(--danger-color);
    }

    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
`;
document.head.appendChild(style);
