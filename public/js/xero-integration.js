/**
 * Xero Integration JavaScript
 * Handles UI interactions for Xero integration features
 */

console.log('[XERO-DEBUG] Xero integration JavaScript loaded!', new Date().toISOString());

// Track initialization sequence
let initSequence = 0;
let tokenRefreshInterval = null;
let tokenRefreshRetryCount = 0;
const MAX_RETRY_ATTEMPTS = 3;
const RETRY_DELAY = 5000; // 5 seconds
let worker = null;

// Function to check and refresh token
async function checkAndRefreshToken() {
    try {
        console.log('[XERO-DEBUG] Checking token status...', new Date().toISOString());
        const response = await fetch(`/xero/health`);
        const health = await response.json();
        
        console.log('[XERO-DEBUG] Token health check:', health);
        
        // Reset retry count on successful check
        tokenRefreshRetryCount = 0;
        
        // Check if token needs refresh (less than 10 minutes until expiry or marked as needing refresh)
        // This matches the backend standardized threshold of 10 minutes
        if (health.needsRefresh || (health.timeUntilExpiry && health.timeUntilExpiry < 600)) {
            console.log('[XERO-DEBUG] To<PERSON> needs refresh, initiating refresh...', new Date().toISOString());
            await refreshToken();
        } else if (health.status === 'inactive') {
            console.error('[XERO-DEBUG] Xero integration is inactive');
            showToast('Xero connection is inactive. Please reconnect.', 'error');
            // Reload page after short delay to show disconnected state
            setTimeout(() => window.location.reload(), 2000);
        }
    } catch (error) {
        console.error('[XERO-DEBUG] Error checking token:', error);
        await handleTokenCheckError();
    }
}

// Function to refresh token
async function refreshToken() {
    try {
        const refreshResponse = await fetch(`/xero/refresh`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
            }
        });
        
        if (!refreshResponse.ok) {
            throw new Error(`HTTP error! status: ${refreshResponse.status}`);
        }
        
        const refreshResult = await refreshResponse.json();
        console.log('[XERO-DEBUG] Token refresh result:', refreshResult);
        
        if (!refreshResult.success) {
            throw new Error(refreshResult.error || 'Token refresh failed');
        }
        
        // Reset retry count on successful refresh
        tokenRefreshRetryCount = 0;
        
        // Show success message
        showToast('Xero connection refreshed successfully', 'success');
        
        // Trigger success callback
        if (window.handleTokenRefreshSuccess) {
            window.handleTokenRefreshSuccess();
        }
    } catch (error) {
        console.error('[XERO-DEBUG] Token refresh error:', error);
        await handleTokenRefreshError(error);
    }
}

// Handle token check errors
async function handleTokenCheckError() {
    tokenRefreshRetryCount++;
    
    if (tokenRefreshRetryCount <= MAX_RETRY_ATTEMPTS) {
        console.log(`[XERO-DEBUG] Retrying token check (attempt ${tokenRefreshRetryCount}/${MAX_RETRY_ATTEMPTS})...`);
        showToast(`Retrying connection check (attempt ${tokenRefreshRetryCount})...`, 'info');
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
        await checkAndRefreshToken();
    } else {
        console.error('[XERO-DEBUG] Max retry attempts reached for token check');
        showToast('Unable to verify Xero connection. Please try reconnecting.', 'error');
        // Reload page after short delay
        setTimeout(() => window.location.reload(), 2000);
    }
}

// Handle token refresh errors
async function handleTokenRefreshError(error) {
    tokenRefreshRetryCount++;
    
    if (tokenRefreshRetryCount <= MAX_RETRY_ATTEMPTS) {
        console.log(`[XERO-DEBUG] Retrying token refresh (attempt ${tokenRefreshRetryCount}/${MAX_RETRY_ATTEMPTS})...`);
        showToast(`Retrying connection refresh (attempt ${tokenRefreshRetryCount})...`, 'info');
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
        await refreshToken();
    } else {
        console.error('[XERO-DEBUG] Max retry attempts reached for token refresh');
        showToast('Unable to refresh Xero connection. Please reconnect.', 'error');
        
        // Trigger error callback
        if (window.handleTokenRefreshError) {
            window.handleTokenRefreshError(error);
        }
        
        // Reload page after short delay
        setTimeout(() => window.location.reload(), 2000);
    }
}

// Function to initialize service worker
async function initializeWorker() {
    if ('serviceWorker' in navigator) {
        try {
            // Register the service worker
            const registration = await navigator.serviceWorker.register('/js/xero-refresh-worker.js');
            console.log('[XERO-DEBUG] Service Worker registered:', registration);
            
            // Create a new worker if needed
            if (!worker) {
                worker = registration.active || registration.waiting || registration.installing;
            }
            
            // Set up message handling
            navigator.serviceWorker.addEventListener('message', (event) => {
                switch (event.data.type) {
                    case 'REFRESH_SUCCESS':
                        console.log('[XERO-DEBUG] Background refresh successful');
                        showToast('Xero connection refreshed in background', 'success');
                        break;
                    case 'REFRESH_ERROR':
                        console.error('[XERO-DEBUG] Background refresh error:', event.data.error);
                        showToast('Error refreshing Xero connection', 'error');
                        break;
                    case 'ERROR':
                        console.error('[XERO-DEBUG] Background worker error:', event.data.error);
                        break;
                }
            });
            
            // Start the refresh cycle
            if (worker) {
                worker.postMessage({
                    type: 'START_REFRESH',
                    companyCode: document.querySelector('meta[name="company-code"]').content
                });
            }
        } catch (error) {
            console.error('[XERO-DEBUG] Service Worker registration failed:', error);
        }
    }
}

// Start token refresh monitoring with more frequent checks
function startTokenRefreshMonitoring() {
    if (tokenRefreshInterval) {
        clearInterval(tokenRefreshInterval);
    }
    
    // Reset retry count
    tokenRefreshRetryCount = 0;
    
    // Initialize service worker for background refresh
    initializeWorker();
    
    // Check token immediately
    checkAndRefreshToken();
    
    // Then check every 5 minutes (increased frequency from 10 minutes)
    tokenRefreshInterval = setInterval(checkAndRefreshToken, 5 * 60 * 1000);
    console.log('[XERO-DEBUG] Token refresh monitoring started with 5-minute intervals', new Date().toISOString());
    
    // Add page visibility change handler
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    // Add beforeunload handler
    window.addEventListener('beforeunload', handleBeforeUnload);
}

// Handle page visibility changes
function handleVisibilityChange() {
    if (document.visibilityState === 'visible') {
        console.log('[XERO-DEBUG] Page became visible, checking token status...');
        checkAndRefreshToken();
    }
}

// Handle page unload
function handleBeforeUnload() {
    // Clear interval and remove event listeners
    if (tokenRefreshInterval) {
        clearInterval(tokenRefreshInterval);
    }
    document.removeEventListener('visibilitychange', handleVisibilityChange);
    
    // Stop the service worker refresh cycle
    if (worker) {
        worker.postMessage({ type: 'STOP_REFRESH' });
    }
}

// Enhanced toast notification system
function showToast(message, type = 'info') {
    const toast = document.getElementById('tokenRefreshToast');
    if (!toast) return;
    
    // Clear any existing timeout
    if (toast.timeoutId) {
        clearTimeout(toast.timeoutId);
    }
    
    // Add icon based on type
    const icon = type === 'success' ? 'ph-check-circle' :
                type === 'error' ? 'ph-x-circle' :
                type === 'info' ? 'ph-info' : 'ph-bell';
    
    toast.innerHTML = `
        <i class="ph ${icon}"></i>
        <span>${message}</span>
    `;
    
    toast.className = `toast ${type}`;
    toast.style.display = 'block';
    
    // Set new timeout
    toast.timeoutId = setTimeout(() => {
        toast.style.display = 'none';
    }, 5000);
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('[XERO-DEBUG] Xero integration DOM loaded!', new Date().toISOString());
    // Initialize Xero mapping functionality if the company is connected to Xero
    if (typeof isXeroConnected !== 'undefined' && isXeroConnected) {
        console.log('[XERO-DEBUG] Xero is connected, initializing mapping', new Date().toISOString());
        initializeXeroMapping(companyCode);
        
        // Start token refresh monitoring
        startTokenRefreshMonitoring();
    } else {
        console.log('[XERO-DEBUG] Xero is not connected or isXeroConnected is undefined', new Date().toISOString());
        console.log('[XERO-DEBUG] isXeroConnected:', isXeroConnected);
    }

    // Handle disconnect button click
    const disconnectButton = document.getElementById('disconnectButton');
    if (disconnectButton) {
        disconnectButton.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Get company code from data attribute
            const companyCode = disconnectButton.getAttribute('data-company-code');
            
            // Show confirmation modal
            const modal = document.getElementById('disconnectModal');
            if (modal) {
                modal.classList.add('active');
                
                // Set the company code in the form
                const companyCodeInput = document.getElementById('disconnectCompanyCode');
                if (companyCodeInput) {
                    companyCodeInput.value = companyCode;
                }
            }
        });
    }

    // Handle modal close button
    const closeModalButtons = document.querySelectorAll('.close-modal');
    closeModalButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modal = document.getElementById('disconnectModal');
            if (modal) {
                modal.classList.remove('active');
            }
        });
    });

    // Handle cancel button in modal
    const cancelButton = document.getElementById('cancelDisconnect');
    if (cancelButton) {
        cancelButton.addEventListener('click', function(e) {
            e.preventDefault();
            const modal = document.getElementById('disconnectModal');
            if (modal) {
                modal.classList.remove('active');
            }
        });
    }
});

/**
 * Initialize Xero account mapping functionality
 * @param {string} companyCode - The company code
 */
function initializeXeroMapping(companyCode) {
    const currentSequence = ++initSequence;
    console.log(`[XERO-DEBUG] [${currentSequence}] initializeXeroMapping started`, new Date().toISOString());
    
    const mappingGrid = document.getElementById('xeroMappingGrid');
    const saveButton = document.getElementById('saveXeroMappings');
    
    if (!mappingGrid || !saveButton) {
        console.log(`[XERO-DEBUG] [${currentSequence}] Missing required elements, aborting initialization`, new Date().toISOString());
        return;
    }
    
    // Clear loading skeleton and any existing content
    console.log(`[XERO-DEBUG] [${currentSequence}] Clearing mapping grid`, new Date().toISOString());
    mappingGrid.innerHTML = '';
    
    // Fetch Xero accounts
    console.log(`[XERO-DEBUG] [${currentSequence}] Fetching Xero accounts`, new Date().toISOString());
    fetchXeroAccounts(companyCode)
        .then(accounts => {
            console.log(`[XERO-DEBUG] [${currentSequence}] Accounts fetched, count:`, accounts.length, new Date().toISOString());
            
            if (!accounts || accounts.length === 0) {
                console.log(`[XERO-DEBUG] [${currentSequence}] No accounts found`, new Date().toISOString());
                showMappingError('No Xero accounts found');
                return;
            }
            
            // Fetch current mappings
            console.log(`[XERO-DEBUG] [${currentSequence}] Fetching current mappings`, new Date().toISOString());
            fetchCurrentMappings(companyCode)
                .then(mappings => {
                    console.log(`[XERO-DEBUG] [${currentSequence}] Mappings fetched:`, mappings, new Date().toISOString());
                    
                    // Check if grid has already been populated by another sequence
                    if (mappingGrid.children.length > 0 && mappingGrid.children[0].className !== 'skeleton') {
                        console.log(`[XERO-DEBUG] [${currentSequence}] Grid already populated, skipping to prevent duplicates`, new Date().toISOString());
                        return;
                    }
                    
                    // Create mapping cards
                    console.log(`[XERO-DEBUG] [${currentSequence}] Creating mapping cards`, new Date().toISOString());
                    createMappingCards(mappingGrid, accounts, mappings, currentSequence);
                    
                    // Enable save button
                    saveButton.disabled = false;
                    
                    // Add event listener to save button
                    saveButton.addEventListener('click', function() {
                        saveMappings(companyCode);
                    });
                    
                    console.log(`[XERO-DEBUG] [${currentSequence}] Mapping initialization complete`, new Date().toISOString());
                })
                .catch(error => {
                    console.error(`[XERO-DEBUG] [${currentSequence}] Error fetching mappings:`, error, new Date().toISOString());
                    showMappingError('Failed to load current mappings');
                });
        })
        .catch(error => {
            console.error(`[XERO-DEBUG] [${currentSequence}] Error fetching Xero accounts:`, error, new Date().toISOString());
            showMappingError('Failed to load Xero accounts');
        });
}

/**
 * Fetch Xero accounts for the company
 * @param {string} companyCode - The company code
 * @returns {Promise} - Promise resolving to accounts array
 */
function fetchXeroAccounts(companyCode) {
    console.log(`[XERO-DEBUG] fetchXeroAccounts started for ${companyCode}`, new Date().toISOString());
    return fetch(`/clients/${companyCode}/settings/accounting/xero/accounts`)
        .then(response => {
            console.log(`[XERO-DEBUG] fetchXeroAccounts response status:`, response.status, new Date().toISOString());
            if (!response.ok) {
                throw new Error('Failed to fetch Xero accounts');
            }
            return response.json();
        })
        .then(data => {
            console.log(`[XERO-DEBUG] fetchXeroAccounts received ${data.length} accounts`, new Date().toISOString());
            return data;
        });
}

/**
 * Fetch current account mappings
 * @param {string} companyCode - The company code
 * @returns {Promise} - Promise resolving to mappings object
 */
function fetchCurrentMappings(companyCode) {
    console.log(`[XERO-DEBUG] fetchCurrentMappings started for ${companyCode}`, new Date().toISOString());
    return fetch(`/clients/${companyCode}/settings/accounting/xero/mappings`)
        .then(response => {
            console.log(`[XERO-DEBUG] fetchCurrentMappings response status:`, response.status, new Date().toISOString());
            if (!response.ok) {
                throw new Error('Failed to fetch current mappings');
            }
            return response.json();
        })
        .then(data => {
            console.log(`[XERO-DEBUG] fetchCurrentMappings received data:`, data, new Date().toISOString());
            return data;
        });
}

/**
 * Create mapping cards in the grid
 * @param {HTMLElement} grid - The mapping grid element
 * @param {Array} accounts - Array of Xero accounts
 * @param {Object} mappings - Current mappings
 * @param {number} sequence - Initialization sequence number
 */
function createMappingCards(grid, accounts, mappings, sequence) {
    console.log(`[XERO-DEBUG] [${sequence}] createMappingCards started with ${accounts.length} accounts`, new Date().toISOString());
    
    // Define payroll items that need mapping with appropriate account types
    const payrollItems = [
        { 
            id: 'salary', 
            name: 'Basic Salary', 
            required: true,
            accountTypes: ['EXPENSE', 'DIRECTCOSTS'] 
        },
        { 
            id: 'commission', 
            name: 'Commission', 
            required: false,
            accountTypes: ['EXPENSE', 'DIRECTCOSTS'] 
        },
        { 
            id: 'bonus', 
            name: 'Bonus', 
            required: false,
            accountTypes: ['EXPENSE', 'DIRECTCOSTS'] 
        },
        { 
            id: 'medical', 
            name: 'Medical Aid', 
            required: false,
            accountTypes: ['EXPENSE', 'LIABILITY', 'CURRLIAB'] 
        },
        { 
            id: 'pension', 
            name: 'Pension Fund', 
            required: false,
            accountTypes: ['EXPENSE', 'LIABILITY', 'CURRLIAB'] 
        },
        { 
            id: 'paye', 
            name: 'PAYE', 
            required: true,
            accountTypes: ['LIABILITY', 'CURRLIAB'] 
        },
        { 
            id: 'uif', 
            name: 'UIF', 
            required: true,
            accountTypes: ['LIABILITY', 'CURRLIAB'] 
        }
    ];
    
    // Log all accounts for debugging
    console.log(`[XERO-DEBUG] [${sequence}] All Xero accounts:`, accounts.map(a => `${a.name} (${a.code}) - ${a.type}`), new Date().toISOString());
    
    // Check if grid already has content
    if (grid.children.length > 0 && grid.children[0].className !== 'skeleton') {
        console.log(`[XERO-DEBUG] [${sequence}] Grid already has ${grid.children.length} children, clearing to prevent duplicates`, new Date().toISOString());
        grid.innerHTML = '';
    }
    
    // Create a card for each payroll item
    payrollItems.forEach(item => {
        console.log(`[XERO-DEBUG] [${sequence}] Creating card for ${item.name}`, new Date().toISOString());
        
        const card = document.createElement('div');
        card.className = 'mapping-card';
        card.dataset.item = item.id;
        card.dataset.sequence = sequence;
        
        const title = document.createElement('h3');
        title.textContent = item.name + (item.required ? ' *' : '');
        
        const select = document.createElement('select');
        select.name = item.id;
        select.required = item.required;
        
        // Add default option
        const defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.textContent = 'Select an account';
        defaultOption.disabled = true;
        defaultOption.selected = !mappings[item.id];
        select.appendChild(defaultOption);
        
        // Filter accounts by type for this payroll item
        const filteredAccounts = accounts.filter(account => {
            // If no account types specified, show all accounts
            if (!item.accountTypes || item.accountTypes.length === 0) {
                return true;
            }
            // Otherwise, filter by account type
            return item.accountTypes.includes(account.type);
        });
        
        // Remove duplicate account names - keep only the first occurrence of each name
        const uniqueAccounts = [];
        const accountNames = new Set();
        
        filteredAccounts.forEach(account => {
            if (!accountNames.has(account.name)) {
                accountNames.add(account.name);
                uniqueAccounts.push(account);
            }
        });
        
        // Sort accounts by name
        uniqueAccounts.sort((a, b) => a.name.localeCompare(b.name));
        
        // Log filtered accounts for debugging
        console.log(`[XERO-DEBUG] [${sequence}] Filtered accounts for ${item.name}:`, uniqueAccounts.map(a => `${a.name} (${a.code})`), new Date().toISOString());
        
        // Add account options
        uniqueAccounts.forEach(account => {
            const option = document.createElement('option');
            option.value = account.accountID;
            option.textContent = `${account.name} (${account.code})`;
            option.selected = mappings[item.id] === account.accountID;
            select.appendChild(option);
        });
        
        // Add change event listener
        select.addEventListener('change', function() {
            saveButton.classList.add('active');
        });
        
        card.appendChild(title);
        card.appendChild(select);
        grid.appendChild(card);
        
        console.log(`[XERO-DEBUG] [${sequence}] Card created for ${item.name} with ${uniqueAccounts.length} accounts`, new Date().toISOString());
    });
    
    console.log(`[XERO-DEBUG] [${sequence}] All cards created, grid now has ${grid.children.length} children`, new Date().toISOString());
}

/**
 * Save the account mappings
 * @param {string} companyCode - The company code
 */
function saveMappings(companyCode) {
    const saveButton = document.getElementById('saveXeroMappings');
    saveButton.disabled = true;
    saveButton.innerHTML = '<i class="ph ph-spinner ph-spin"></i> Saving...';
    
    // Get all mapping selects
    const mappingCards = document.querySelectorAll('.mapping-card');
    const mappings = {};
    
    mappingCards.forEach(card => {
        const itemId = card.dataset.item;
        const select = card.querySelector('select');
        mappings[itemId] = select.value;
    });
    
    // Get CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    
    // Send mappings to server
    fetch(`/clients/${companyCode}/settings/accounting/xero/mappings`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'CSRF-Token': csrfToken
        },
        body: JSON.stringify({ mappings })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Failed to save mappings');
        }
        return response.json();
    })
    .then(data => {
        saveButton.innerHTML = '<i class="ph ph-check"></i> Saved';
        saveButton.classList.remove('active');
        
        // Show success message
        showToast('Mappings saved successfully', 'success');
        
        // Reset button after delay
        setTimeout(() => {
            saveButton.innerHTML = '<i class="ph ph-floppy-disk"></i> Save Mappings';
            saveButton.disabled = false;
        }, 2000);
    })
    .catch(error => {
        console.error('Error saving mappings:', error);
        saveButton.innerHTML = '<i class="ph ph-x"></i> Error';
        
        // Show error message
        showToast('Failed to save mappings', 'error');
        
        // Reset button after delay
        setTimeout(() => {
            saveButton.innerHTML = '<i class="ph ph-floppy-disk"></i> Save Mappings';
            saveButton.disabled = false;
        }, 2000);
    });
}

/**
 * Show a mapping error message
 * @param {string} message - The error message
 */
function showMappingError(message) {
    const mappingGrid = document.getElementById('xeroMappingGrid');
    if (!mappingGrid) return;
    
    mappingGrid.innerHTML = `
        <div class="mapping-error">
            <i class="ph ph-warning"></i>
            <p>${message}</p>
        </div>
    `;
} 