const express = require("express");
const multer = require("multer");
const path = require("path");
const fs = require("fs");
const { ensureAuthenticated } = require("../middleware/auth");

// Create a completely isolated router for file uploads
const fileUploadRouter = express.Router();

// Configure multer storage for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, "..", "uploads");
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    cb(
      null,
      file.fieldname + "-" + uniqueSuffix + path.extname(file.originalname)
    );
  },
});

// Configure multer with strict settings
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
    fieldSize: 1024 * 1024, // 1MB for text fields
    fields: 5, // Minimal fields for file upload only
    parts: 10, // Minimal parts
  },
  fileFilter: (req, file, cb) => {
    console.log('\n🔧 ISOLATED MULTER FILE FILTER');
    console.log('File received:', {
      fieldname: file.fieldname,
      originalname: file.originalname,
      mimetype: file.mimetype,
    });
    
    const allowedTypes = /jpeg|jpg|png|gif|webp/;
    const extname = allowedTypes.test(
      path.extname(file.originalname).toLowerCase()
    );
    const mimetype = allowedTypes.test(file.mimetype);

    if (extname && mimetype) {
      console.log('✅ File accepted by isolated router');
      return cb(null, true);
    } else {
      console.log('❌ File rejected - invalid type');
      cb(new Error("Only JPG, PNG, GIF, and WebP formats are allowed!"));
    }
  },
});

// Enhanced authentication middleware with session + JWT fallback
const enhancedAuth = (req, res, next) => {
  console.log('\n🔐 ISOLATED ROUTER AUTHENTICATION CHECK');
  console.log('Session ID:', req.sessionID || 'No session');
  console.log('User in session:', req.session?.passport?.user || 'No user');
  console.log('isAuthenticated available:', typeof req.isAuthenticated);
  console.log('req.user:', req.user ? 'Present' : 'Missing');
  console.log('Authorization header:', req.headers.authorization ? 'Present' : 'Missing');

  // Primary: Session-based authentication (Passport)
  if (typeof req.isAuthenticated === 'function') {
    if (req.isAuthenticated()) {
      console.log('✅ Session authentication successful');
      return next();
    }
    console.log('⚠️ Session authentication failed, trying JWT fallback...');
  } else {
    console.log('⚠️ Passport not available, trying JWT fallback...');
  }

  // Fallback: JWT authentication
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    try {
      const token = authHeader.substring(7);
      const jwt = require('jsonwebtoken');
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      req.user = decoded;
      console.log('✅ JWT authentication successful');
      return next();
    } catch (jwtError) {
      console.error('❌ JWT authentication failed:', jwtError.message);
    }
  }

  // Fallback: Manual session check
  if (req.session?.passport?.user) {
    console.log('✅ Manual session check successful');
    return next();
  }

  console.error('❌ All authentication methods failed');
  return res.status(401).json({
    success: false,
    message: 'Authentication required'
  });
};

// ISOLATED FILE UPLOAD ENDPOINT WITH ENHANCED AUTH
// This route has session/passport access but bypasses body parsing conflicts
fileUploadRouter.post(
  "/upload-logo/:companyCode",
  (req, res, next) => {
    console.log('\n🚀 ISOLATED FILE UPLOAD ENDPOINT HIT!');
    console.log('Path:', req.path);
    console.log('Method:', req.method);
    console.log('Content-Type:', req.headers['content-type']);
    console.log('Content-Length:', req.headers['content-length']);
    console.log('Request readable:', req.readable);
    console.log('Request destroyed:', req.destroyed);
    next();
  },
  enhancedAuth,
  upload.single("logo"),
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      console.log("\n=== ISOLATED FILE UPLOAD PROCESSING ===");
      console.log("Company Code:", companyCode);
      console.log("File uploaded:", req.file ? {
        originalname: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size,
        filename: req.file.filename,
        path: req.file.path
      } : 'No file received');

      if (!req.file) {
        console.error("No file received in isolated upload");
        return res.status(400).json({
          success: false,
          message: "No file uploaded"
        });
      }

      console.log("✅ File upload successful in isolated router");

      // Return file information for the main form to use
      return res.json({
        success: true,
        message: "File uploaded successfully",
        file: {
          filename: req.file.filename,
          originalname: req.file.originalname,
          path: `/uploads/${req.file.filename}`,
          size: req.file.size,
          mimetype: req.file.mimetype
        }
      });
    } catch (error) {
      console.error("Isolated file upload error:", error);
      console.error("Error stack:", error.stack);
      return res.status(500).json({
        success: false,
        message: "File upload failed",
        error: process.env.NODE_ENV === 'development' ? error.message : 'Upload error'
      });
    }
  }
);

// Test endpoint to verify authentication is working
fileUploadRouter.get("/test-auth", enhancedAuth, (req, res) => {
  console.log('\n🧪 AUTHENTICATION TEST ENDPOINT');
  res.json({
    success: true,
    message: 'Authentication working correctly',
    user: req.user ? {
      id: req.user.id || req.user._id,
      // Don't expose sensitive data
    } : 'No user data',
    sessionID: req.sessionID,
    timestamp: new Date().toISOString()
  });
});

module.exports = fileUploadRouter;
