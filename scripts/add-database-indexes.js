#!/usr/bin/env node

/**
 * Database Performance Optimization Script
 * Adds critical indexes to improve dashboard query performance
 * 
 * Expected improvement: 50-70% faster queries
 * Target: 3,740ms → 1,000-1,500ms server response time
 */

const mongoose = require('mongoose');
require('dotenv').config();

async function addDatabaseIndexes() {
  try {
    console.log('🚀 Starting database index optimization...');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ Connected to MongoDB');
    
    const db = mongoose.connection.db;
    
    // Employee collection indexes for dashboard queries
    console.log('📊 Adding Employee collection indexes...');
    
    // Basic company index (most important)
    await db.collection('employees').createIndex({ "company": 1 });
    console.log('✅ Added: company index');
    
    // Date-based queries for joiners/terminations
    await db.collection('employees').createIndex({ "company": 1, "doa": 1 });
    console.log('✅ Added: company + doa index');
    
    await db.collection('employees').createIndex({ "company": 1, "endServiceDate": 1 });
    console.log('✅ Added: company + endServiceDate index');
    
    // Cost center distribution query
    await db.collection('employees').createIndex({ "company": 1, "costCentre": 1 });
    console.log('✅ Added: company + costCentre index');
    
    // Status-based queries
    await db.collection('employees').createIndex({ "company": 1, "status": 1 });
    console.log('✅ Added: company + status index');
    
    // Compound index for headcount calculations (most expensive query)
    await db.collection('employees').createIndex({ 
      "company": 1, 
      "doa": 1, 
      "endServiceDate": 1 
    });
    console.log('✅ Added: company + doa + endServiceDate compound index');
    
    // Notification collection indexes
    console.log('📢 Adding Notification collection indexes...');
    
    await db.collection('notifications').createIndex({ "company": 1, "createdAt": -1 });
    console.log('✅ Added: company + createdAt index');
    
    // Company collection indexes
    console.log('🏢 Adding Company collection indexes...');
    
    await db.collection('companies').createIndex({ "companyCode": 1 });
    console.log('✅ Added: companyCode index');
    
    // User collection indexes
    console.log('👤 Adding User collection indexes...');
    
    await db.collection('users').createIndex({ "email": 1 });
    console.log('✅ Added: email index');
    
    await db.collection('users').createIndex({ "companies": 1 });
    console.log('✅ Added: companies index');
    
    // Check index creation
    console.log('\n📋 Verifying indexes...');
    
    const employeeIndexes = await db.collection('employees').indexes();
    console.log(`✅ Employee collection has ${employeeIndexes.length} indexes`);
    
    const notificationIndexes = await db.collection('notifications').indexes();
    console.log(`✅ Notification collection has ${notificationIndexes.length} indexes`);
    
    console.log('\n🎉 Database indexing complete!');
    console.log('📈 Expected performance improvement: 50-70% faster queries');
    console.log('🎯 Target: Dashboard load time should improve from 3.7s to 1.0-1.5s');
    
  } catch (error) {
    console.error('❌ Error adding database indexes:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
    process.exit(0);
  }
}

// Run the script
if (require.main === module) {
  addDatabaseIndexes();
}

module.exports = { addDatabaseIndexes };
