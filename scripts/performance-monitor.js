#!/usr/bin/env node

/**
 * Performance Monitoring Script
 * Tests dashboard query performance before and after optimizations
 * 
 * Usage: node scripts/performance-monitor.js
 */

const mongoose = require('mongoose');
const moment = require('moment');
require('dotenv').config();

// Import models
const Employee = require('../models/Employee');
const Notification = require('../models/Notification');

async function testDashboardPerformance() {
  try {
    console.log('🚀 Starting dashboard performance test...');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ Connected to MongoDB');
    
    // Get a sample company ID for testing
    const sampleEmployee = await Employee.findOne().populate('company');
    if (!sampleEmployee || !sampleEmployee.company) {
      console.log('❌ No sample company found for testing');
      return;
    }
    
    const companyId = sampleEmployee.company._id;
    const filterYear = moment().year();
    const filterMonth = moment().month() + 1;
    
    console.log(`📊 Testing with Company ID: ${companyId}`);
    console.log(`📅 Filter: ${filterMonth}/${filterYear}`);
    
    // Test individual query performance
    console.log('\n🔍 Testing individual query performance...');
    
    // Test 1: Employee count
    const start1 = Date.now();
    const headcount = await Employee.countDocuments({ company: companyId });
    const time1 = Date.now() - start1;
    console.log(`✅ Headcount query: ${time1}ms (${headcount} employees)`);
    
    // Test 2: Joiners count
    const currentDate = moment([filterYear, filterMonth - 1]).endOf('month');
    const firstDayOfMonth = moment([filterYear, filterMonth - 1]).startOf('month');
    
    const start2 = Date.now();
    const joiners = await Employee.countDocuments({
      company: companyId,
      doa: {
        $gte: firstDayOfMonth.toDate(),
        $lte: currentDate.toDate(),
      },
      doa: { $ne: null, $exists: true }
    });
    const time2 = Date.now() - start2;
    console.log(`✅ Joiners query: ${time2}ms (${joiners} joiners)`);
    
    // Test 3: Terminations count
    const start3 = Date.now();
    const terminations = await Employee.countDocuments({
      company: companyId,
      endServiceDate: {
        $gte: firstDayOfMonth.toDate(),
        $lte: currentDate.toDate(),
      },
      endServiceDate: { $ne: null, $exists: true }
    });
    const time3 = Date.now() - start3;
    console.log(`✅ Terminations query: ${time3}ms (${terminations} terminations)`);
    
    // Test 4: Latest notification
    const start4 = Date.now();
    const latestNotification = await Notification.findOne({ company: companyId })
      .sort({ createdAt: -1 })
      .limit(1);
    const time4 = Date.now() - start4;
    console.log(`✅ Notification query: ${time4}ms`);
    
    // Test 5: Cost center distribution
    const start5 = Date.now();
    const costCenterData = await Employee.aggregate([
      {
        $match: {
          company: companyId,
          costCentre: { $exists: true, $ne: null, $ne: "" }
        }
      },
      {
        $group: {
          _id: "$costCentre",
          count: { $sum: 1 }
        }
      },
      { $sort: { count: -1 } },
      { $limit: 20 }
    ]);
    const time5 = Date.now() - start5;
    console.log(`✅ Cost center query: ${time5}ms (${costCenterData.length} centers)`);
    
    // Test 6: Parallel execution
    console.log('\n⚡ Testing parallel query execution...');
    const startParallel = Date.now();
    
    const [
      parallelHeadcount,
      parallelJoiners,
      parallelTerminations,
      parallelNotification,
      parallelCostCenters
    ] = await Promise.all([
      Employee.countDocuments({ company: companyId }),
      Employee.countDocuments({
        company: companyId,
        doa: {
          $gte: firstDayOfMonth.toDate(),
          $lte: currentDate.toDate(),
        },
        doa: { $ne: null, $exists: true }
      }),
      Employee.countDocuments({
        company: companyId,
        endServiceDate: {
          $gte: firstDayOfMonth.toDate(),
          $lte: currentDate.toDate(),
        },
        endServiceDate: { $ne: null, $exists: true }
      }),
      Notification.findOne({ company: companyId })
        .sort({ createdAt: -1 })
        .limit(1),
      Employee.aggregate([
        {
          $match: {
            company: companyId,
            costCentre: { $exists: true, $ne: null, $ne: "" }
          }
        },
        {
          $group: {
            _id: "$costCentre",
            count: { $sum: 1 }
          }
        },
        { $sort: { count: -1 } },
        { $limit: 20 }
      ])
    ]);
    
    const timeParallel = Date.now() - startParallel;
    console.log(`✅ Parallel execution: ${timeParallel}ms`);
    
    // Calculate performance summary
    const sequentialTime = time1 + time2 + time3 + time4 + time5;
    const improvement = ((sequentialTime - timeParallel) / sequentialTime * 100).toFixed(1);
    
    console.log('\n📊 Performance Summary:');
    console.log(`🔄 Sequential execution: ${sequentialTime}ms`);
    console.log(`⚡ Parallel execution: ${timeParallel}ms`);
    console.log(`🚀 Performance improvement: ${improvement}%`);
    
    // Performance recommendations
    console.log('\n💡 Performance Analysis:');
    if (timeParallel < 500) {
      console.log('🟢 Excellent performance! Dashboard should load quickly.');
    } else if (timeParallel < 1000) {
      console.log('🟡 Good performance. Consider adding caching for further improvement.');
    } else if (timeParallel < 2000) {
      console.log('🟠 Moderate performance. Database indexing may help.');
    } else {
      console.log('🔴 Poor performance. Check database indexes and query optimization.');
    }
    
    // Index recommendations
    console.log('\n🔍 Recommended indexes (if not already created):');
    console.log('- db.employees.createIndex({ "company": 1 })');
    console.log('- db.employees.createIndex({ "company": 1, "doa": 1 })');
    console.log('- db.employees.createIndex({ "company": 1, "endServiceDate": 1 })');
    console.log('- db.employees.createIndex({ "company": 1, "costCentre": 1 })');
    console.log('- db.notifications.createIndex({ "company": 1, "createdAt": -1 })');
    
  } catch (error) {
    console.error('❌ Error testing performance:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
    process.exit(0);
  }
}

// Run the test
if (require.main === module) {
  testDashboardPerformance();
}

module.exports = { testDashboardPerformance };
