const crypto = require("crypto");
const moment = require("moment");
const Employee = require("../models/Employee");
const User = require("../models/user");
const Role = require("../models/role");
const transporter = require("../config/emailConfig");
const ejs = require("ejs");
const path = require("path");

class SelfServiceTokenService {
  static async generateSetupToken(employee) {

    const token = crypto.randomBytes(32).toString("hex");
    const expirationDate = moment().add(24, "hours").toDate();


    employee.selfServiceToken = token;
    employee.selfServiceTokenExpiration = expirationDate;
    await employee.save();

    return token;
  }

  static async createEmployeeUser(employee) {
    try {
      // Check if user already exists
      let user = await User.findOne({ email: employee.email });
      if (user) {
        return user;
      }

      // Get employee role
      const employeeRole = await Role.findOne({ name: "employee" });
      if (!employeeRole) {
        throw new Error("Employee role not found");
      }

      // Create new user
      user = new User({
        firstName: employee.firstName,
        lastName: employee.lastName,
        email: employee.email,
        username: employee.email,
        phone: employee.phone,
        role: employeeRole._id,
        companies: [employee.company],
        currentCompany: employee.company,
        isVerified: false,
        password: crypto.randomBytes(20).toString("hex"), // Temporary password
      });

      await user.save();

      // Link user to employee
      employee.user = user._id;
      await employee.save();

      return user;
    } catch (error) {
      console.error("Error creating user account:", error);
      throw error;
    }
  }

  static async sendSetupEmail(employee, setupUrl) {
    try {
      // Create user account first
      await this.createEmployeeUser(employee);

      // Render email template
      const emailTemplate = await ejs.renderFile(
        path.join(__dirname, "../views/emails/selfServiceSetup.ejs"),
        {
          employeeName: `${employee.firstName} ${employee.lastName}`,
          setupUrl: setupUrl,
        }
      );

      // Add retry logic for sending email
      let attempts = 0;
      const maxAttempts = 3;
      let lastError;

      while (attempts < maxAttempts) {
        try {
          // Ensure proper FROM address formatting
          const fromAddress = process.env.EMAIL_USER;
          const fromName = process.env.EMAIL_FROM_NAME || "Panda Software Solutions Group";

          if (!fromAddress) {
            throw new Error("EMAIL_USER environment variable is required");
          }

          const info = await transporter.sendMail({
            from: `"${fromName}" <${fromAddress}>`,
            to: employee.email,
            subject: "Set Up Your Employee Self-Service Portal",
            html: emailTemplate,
          });


          // Update employee record
          employee.essEmailSent = true;
          employee.lastActivity = new Date();
          await employee.save();

          return true;
        } catch (error) {
          console.error(`Email sending attempt ${attempts + 1} failed:`, error);
          lastError = error;
          attempts++;
          if (attempts < maxAttempts) {
            // Wait for 1 second before retrying
            await new Promise((resolve) => setTimeout(resolve, 1000));
          }
        }
      }

      // If all attempts failed
      throw new Error(
        `Failed to send email after ${maxAttempts} attempts: ${lastError.message}`
      );
    } catch (error) {
      console.error("Error in sendSetupEmail:", error);
      throw error;
    }
  }

  static async handleSelfServiceEnable(employee, baseUrl) {
    try {
      // Check if employee already has a valid setup token
      if (
        employee.selfServiceToken &&
        employee.selfServiceTokenExpiration > new Date() &&
        employee.essEmailSent
      ) {
        return {
          success: true,
          message: "Setup email already sent and token still valid",
        };
      }

      // Generate new token
      const token = await this.generateSetupToken(employee);
      const setupUrl = `${baseUrl}/self-service/setup/${token}`;

      // Only send email if not already sent or token was expired
      if (
        !employee.essEmailSent ||
        !employee.selfServiceTokenExpiration ||
        employee.selfServiceTokenExpiration <= new Date()
      ) {
        await this.sendSetupEmail(employee, setupUrl);
      } else {
      }

      return {
        success: true,
        message: "Self-service setup handled successfully",
      };
    } catch (error) {
      console.error("Error in self-service enable process:", error);
      throw error;
    }
  }

  static async validateToken(token) {
    const employee = await Employee.findOne({
      selfServiceToken: token,
      selfServiceTokenExpiration: { $gt: Date.now() },
    });

    if (!employee) {
      throw new Error("Invalid or expired token");
    }

    return employee;
  }
}

module.exports = SelfServiceTokenService;
