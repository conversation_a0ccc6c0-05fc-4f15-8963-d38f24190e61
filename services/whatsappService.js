const axios = require('axios');
const Employee = require('../models/Employee');
const Payslip = require('../models/Payslip');
const LeaveRequest = require('../models/LeaveRequest');
const LeaveType = require('../models/LeaveType');
const generatePayslipPdfLib = require('../utils/payslipPdfGenerator');
const generateIRP5PdfLib = require('../utils/irp5PdfGenerator');
const IRP5Service = require('./IRP5Service');
const { verifyToken, generateToken } = require('../utils/jwtUtils');
const queue = require('../config/queue');
const sessionService = require('../services/sessionService');
const WhatsAppRequestLog = require('../models/WhatsAppRequestLog');
const PayrollPeriod = require('../models/PayrollPeriod');
const PayrollService = require('../services/PayrollService');
const fs = require('fs').promises;
const path = require('path');
const { S3Client, PutObjectCommand, GetObjectCommand } = require('@aws-sdk/client-s3');
const { getSignedUrl } = require('@aws-sdk/s3-request-presigner');
const moment = require('moment');

// Define a temporary directory for file uploads
const UPLOADS_DIR = path.join(__dirname, '../temp_uploads');

// Ensure the upload directory exists
async function ensureUploadDirectoryExists() {
  try {
    await fs.mkdir(UPLOADS_DIR, { recursive: true });
  } catch (error) {
    console.error('Error ensuring upload directory exists:', error);
    throw error; // Re-throw to halt execution if directory cannot be created
  }
}

// Call this function once during service initialization
ensureUploadDirectoryExists().catch(err => {
  console.error('Failed to create upload directory on startup:', err);
  // Depending on requirements, you might want to exit the process
  // process.exit(1);
});

class WhatsAppService {
  constructor() {
    if (WhatsAppService.instance) {
      // Update the token if it has changed
      if (WhatsAppService.instance.accessToken !== process.env.WHATSAPP_ACCESS_TOKEN) {
        WhatsAppService.instance.accessToken = process.env.WHATSAPP_ACCESS_TOKEN;
      }
      return WhatsAppService.instance;
    }

    this.apiVersion = process.env.WHATSAPP_API_VERSION || 'v23.0';
    this.phoneNumberId = process.env.WHATSAPP_PHONE_NUMBER_ID;
    this.accessToken = process.env.WHATSAPP_ACCESS_TOKEN;
    this.baseUrl = `https://graph.facebook.com/${this.apiVersion}`;

    // Initialize S3 Client
    // It automatically picks up credentials and region from environment variables (AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, AWS_REGION)
    this.s3Client = new S3Client({});
    this.s3BucketName = process.env.S3_BUCKET_NAME; // Ensure you have this environment variable set

    // Validate required configuration
    if (!this.phoneNumberId) {
      throw new Error('WHATSAPP_PHONE_NUMBER_ID environment variable is required');
    }
    if (!this.accessToken) {
      throw new Error('WHATSAPP_ACCESS_TOKEN environment variable is required');
    }

    // Log configuration in non-production environments
    if (process.env.NODE_ENV !== 'production') {
    }

    if (!this.s3BucketName) {
      console.error('WARNING: S3_BUCKET_NAME environment variable is not set. PDF uploads to S3 will fail.');
    }

    WhatsAppService.instance = this;
  }

  // Method to update access token
  updateAccessToken(newToken) {
    this.accessToken = newToken;
  }

  async sendMessage(to, message) {
    try {
      // Input validation
      if (!to || !message) {
        throw new Error('Recipient phone number and message are required');
      }

      // Remove any '+' prefix and spaces from the phone number
      const normalizedPhone = to.replace(/[\\s+]/g, '');

      // Always get the latest token from environment variables
      const latestToken = process.env.WHATSAPP_ACCESS_TOKEN;
      if (latestToken && latestToken !== this.accessToken) {
        this.accessToken = latestToken;
      }

      
      const response = await axios.post(
        `${this.baseUrl}/${this.phoneNumberId}/messages`,
        {
          messaging_product: 'whatsapp',
          recipient_type: 'individual',
          to: normalizedPhone,
          type: 'text',
          text: { body: message }
        },
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      return response.data;
    } catch (error) {
      // Check if the error is due to an expired token
      if (error.response?.status === 401 && error.response?.data?.error?.code === 190) {
        console.error('WhatsApp access token expired or invalid');
      }

      console.error('WhatsApp API Error:', {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message,
        config: {
          url: error.config?.url,
          method: error.config?.method,
          headers: {
            ...error.config?.headers,
            Authorization: error.config?.headers?.Authorization ? 'Bearer [REDACTED]' : undefined
          }
        }
      });
      throw error;
    }
  }

  async sendDocument(to, documentUrl, caption) {
    try {
      // Input validation
      if (!to || !documentUrl) {
        throw new Error('Recipient phone number and document URL are required');
      }

      // Remove any '+' prefix and spaces from the phone number
      const normalizedPhone = to.replace(/[\\s+]/g, '');

      const response = await axios.post(
        `${this.baseUrl}/${this.phoneNumberId}/messages`,
        {
          messaging_product: 'whatsapp',
          to: normalizedPhone,
          type: 'document',
          document: {
            link: documentUrl, // Use the direct URL provided (should be S3 pre-signed URL)
            caption: caption || ''
          }
        },
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    } catch (error) {
      console.error('WhatsApp API Error:', {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message,
        config: {
          url: error.config?.url,
          method: error.config?.method,
          headers: {
            ...error.config?.headers,
            Authorization: error.config?.headers?.Authorization ? 'Bearer [REDACTED]' : undefined
          }
        }
      });
      throw error;
    }
  }

  async authenticateEmployee(phoneNumber) {
    try {
      
      // Input validation
      if (!phoneNumber) {
        console.error('Phone number is required for authentication');
        return null;
      }

      // Remove any '+' prefix and spaces from the phone number
      const normalizedPhone = phoneNumber.replace(/[\s+]/g, '');
      
      // Try different formats of the phone number
      // 1. As is (normalized)
      // 2. With country code if not present (add 27 for South Africa)
      // 3. Without country code if present (remove 27)
      const possibleFormats = [normalizedPhone];
      
      // If number doesn't start with country code, add it
      if (!normalizedPhone.startsWith('27') && normalizedPhone.length === 9) {
        possibleFormats.push(`27${normalizedPhone}`);
      }
      
      // If number starts with country code, also try without it
      if (normalizedPhone.startsWith('27') && normalizedPhone.length > 10) {
        possibleFormats.push(normalizedPhone.substring(2));
      }
      
      
      // Find employee by any of the possible phone number formats
      const employee = await Employee.findOne({
        'personalDetails.mobileNumber': { $in: possibleFormats }
      }).populate([{
        path: 'company',
        populate: {
          path: 'employerDetails'
        }
      }, 'payFrequency']);

      if (!employee) {
        return null;
      }

      
      // If the phone number in the database doesn't match the WhatsApp number format,
      // update it to ensure future matches are easier
      if (employee.personalDetails.mobileNumber !== normalizedPhone) {
        await this.updateEmployeeWhatsAppNumber(employee._id, normalizedPhone);
      }

      // Generate a temporary token for the session
      const token = generateToken({
        id: employee._id,
        username: employee.personalDetails.email,
        email: employee.personalDetails.email,
        role: 'employee',
        firstName: employee.firstName,
        lastName: employee.lastName
      });

      // Retrieve existing session and merge new data, preserving conversational state
      const existingSession = await sessionService.getSession(normalizedPhone);
      const newSessionData = {
        ...existingSession, // Preserve existing data
        employee,
        token,
        lastActivity: new Date()
      };
      await sessionService.setSession(normalizedPhone, newSessionData);

      return {
        employee,
        token
      };
    } catch (error) {
      console.error('Employee Authentication Error:', {
        phoneNumber,
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * Send the main interactive menu as a WhatsApp list message.
   * @param {string} to - Recipient phone number (E.164 format)
   * @param {string} firstName - Employee's first name for personalization
   */
  async sendInteractiveMenu(to, firstName) {
    const normalizedPhone = to.replace(/[\s+]/g, '');
    const latestToken = process.env.WHATSAPP_ACCESS_TOKEN;
    if (latestToken && latestToken !== this.accessToken) {
      this.accessToken = latestToken;
    }
    const menuBody = `Hello ${firstName} 👋, how can I help you today?`;
    const payload = {
      messaging_product: 'whatsapp',
      recipient_type: 'individual',
      to: normalizedPhone,
      type: 'interactive',
      interactive: {
        type: 'list',
        body: { text: menuBody },
        action: {
          button: 'Select an option',
          sections: [
            {
              title: 'Main Menu',
              rows: [
                { id: 'get_payslip', title: 'Get my Payslips', description: 'Get your latest payslips, up to the last 6 months.' },
                { id: 'leave_request', title: 'Leave Request', description: 'Request any type of leave.' },
                { id: 'irp5', title: 'My tax documents', description: 'Get your certificate for your tax return.' },
              ]
            }
          ]
        }
      }
    };
    const response = await axios.post(
      `${this.baseUrl}/${this.phoneNumberId}/messages`,
      payload,
      {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    );
    return response.data;
  }

  /**
   * Sends an interactive list message via WhatsApp.
   * @param {string} to - Recipient phone number (E.164 format)
   * @param {string} bodyText - The main text of the interactive message body.
   * @param {string} buttonText - The text for the button that opens the list.
   * @param {string} sectionTitle - The title for the section containing list items.
   * @param {Array<object>} listItems - An array of list item objects ({ id, title, description? }).
   */
  async sendInteractiveList(to, bodyText, buttonText, sectionTitle, listItems) {
    const normalizedPhone = to.replace(/[\s+]/g, '');
    // Always get the latest token from environment variables
    const latestToken = process.env.WHATSAPP_ACCESS_TOKEN;
    if (latestToken && latestToken !== this.accessToken) {
      this.accessToken = latestToken;
    }

    // Validate and sanitize list items for WhatsApp API v23.0 compliance
    const sanitizedListItems = listItems.map((item, index) => {
      // Ensure ID is valid (alphanumeric, underscore, hyphen only, max 256 chars)
      let sanitizedId = item.id.replace(/[^a-zA-Z0-9_-]/g, '_').substring(0, 256);
      if (!sanitizedId) {
        sanitizedId = `item_${index}`;
      }

      // Ensure title is within limits (max 24 characters)
      let sanitizedTitle = item.title ? item.title.substring(0, 24) : `Option ${index + 1}`;

      // Ensure description is within limits (max 72 characters)
      let sanitizedDescription = item.description ? item.description.substring(0, 72) : '';

      return {
        id: sanitizedId,
        title: sanitizedTitle,
        description: sanitizedDescription
      };
    });

    // Limit to maximum 10 items per list
    const limitedListItems = sanitizedListItems.slice(0, 10);

    const sections = [{
      title: sectionTitle.substring(0, 24), // Section title max 24 characters
      rows: limitedListItems
    }];

    const messagePayload = {
      messaging_product: 'whatsapp',
      recipient_type: 'individual',
      to: normalizedPhone,
      type: 'interactive',
      interactive: {
        type: 'list',
        body: { text: bodyText.substring(0, 1024) }, // Body text max 1024 characters
        action: {
          button: buttonText.substring(0, 20), // Button text max 20 characters
          sections: sections
        }
      }
    };

    try {

      // Log the full payload for debugging (in development only)
      if (process.env.NODE_ENV !== 'production') {
      }

      const response = await axios.post(
        `${this.baseUrl}/${this.phoneNumberId}/messages`,
        messagePayload,
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    } catch (error) {
      console.error('WhatsApp Interactive List API Error:', {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message,
        config: {
          url: error.config?.url,
          method: error.config?.method,
          headers: {
            ...error.config?.headers,
            Authorization: error.config?.headers?.Authorization ? 'Bearer [REDACTED]' : undefined
          }
        }
      });
      throw error;
    }
  }

  async handlePayslipRequest(employee, periodId) {
    let requestType = 'payslip'; // Use the allowed enum value
    let requestText = periodId ? `Payslip ID: ${periodId}` : 'Payslip list request';
    let logData = { employeeId: employee?._id, periodId: periodId }; // Always capture relevant IDs if available

    try {

      if (!employee) {
        const message = 'Authentication failed. Could not find employee.';
        await WhatsAppRequestLog.create({
          // Use optional chaining and default values as employee might be null
          from: employee?.personalDetails?.mobileNumber,
          to: process.env.WHATSAPP_PHONE_NUMBER_ID,
          requestType: requestType, // Use the allowed enum value
          requestData: logData,
          // Store response as a JSON string
          response: JSON.stringify({ status: 'failed', message }),
          timestamp: new Date(),
          // Ensure required fields are populated even on early failure if possible
          employee: employee?._id,
          company: employee?.company?._id, // Assuming company is populated on employee
          mobileNumber: employee?.personalDetails?.mobileNumber,
          requestText: requestText // Use determined request text
        });
        return { success: false, message: message };
      }

      // If no periodId is provided, send the interactive list of payslips
      if (!periodId) {
        const payslipPeriods = await PayrollPeriod.find({
          employee: employee._id,
          isFinalized: true // Only show finalized payslips
        })
        .sort({ endDate: -1 })
        .limit(10); // Limit to the last 10 finalized payslips

        if (!payslipPeriods || payslipPeriods.length === 0) {
          const message = 'No finalized payslips found for your account.';
           // Log the request and response
           await WhatsAppRequestLog.create({
            from: employee.personalDetails.mobileNumber,
            to: process.env.WHATSAPP_PHONE_NUMBER_ID,
            requestType: requestType, // Use the allowed enum value
            requestData: logData,
            // Store response as a JSON string
            response: JSON.stringify({ status: 'failed', message }),
            timestamp: new Date(),
            // Ensure required fields are populated
            employee: employee._id,
            company: employee.company?._id,
            mobileNumber: employee.personalDetails.mobileNumber,
            requestText: requestText
           });
          return { success: false, message };
        }

        // Generate the list items for the interactive message
        const listItems = payslipPeriods.map(period => ({
          id: period._id.toString(), // Use period ID as the unique identifier
          title: `Payslip: ${moment(period.endDate).format('YYYY-MM')}`, // Format as Month Year
          description: `Period: ${moment(period.startDate).format('DD/MM/YYYY')} - ${moment(period.endDate).format('DD/MM/YYYY')}`
        }));

        // --- Use the new sendInteractiveList method ---
        await this.sendInteractiveList(
          employee.personalDetails.mobileNumber,
          `Hi ${employee.firstName}, please select the payslip period you'd like:`,
          'View Payslips',
          'Select a Payslip Period',
          listItems
        );
         // Log the request and response (menu sent)
         await WhatsAppRequestLog.create({
          from: employee.personalDetails.mobileNumber,
          to: process.env.WHATSAPP_PHONE_NUMBER_ID,
          requestType: requestType, // Use the allowed enum value
          requestData: logData,
          // Store response as a JSON string
          response: JSON.stringify({ status: 'success', message: 'Interactive payslip list sent.' }),
          timestamp: new Date(),
           // Ensure required fields are populated
          employee: employee._id,
          company: employee.company?._id,
          mobileNumber: employee.personalDetails.mobileNumber,
          requestText: requestText
         });
        return { success: true, menuSent: true }; // Indicate that the menu was sent
      }

      // If a periodId is provided, find and generate the specific payslip
      const payrollPeriod = await PayrollPeriod.findOne({
        _id: periodId,
        employee: employee._id,
        isFinalized: true // Ensure it's finalized before sending
      });

      if (!payrollPeriod) {
        const message = 'No finalized payslip found for the specified period.';
         // Log the request and response
         await WhatsAppRequestLog.create({
          from: employee.personalDetails.mobileNumber,
          to: process.env.WHATSAPP_PHONE_NUMBER_ID,
          requestType: requestType, // Use the allowed enum value
          requestData: logData,
          // Store response as a JSON string
          response: JSON.stringify({ status: 'failed', message }),
          timestamp: new Date(),
           // Ensure required fields are populated
          employee: employee._id,
          company: employee.company?._id,
          mobileNumber: employee.personalDetails.mobileNumber,
          requestText: requestText
         });
        return { success: false, message };
      }

      // Get calculations for the period
      const calculations = await PayrollService.calculatePayrollTotals(
        employee._id,
        payrollPeriod.endDate
      );

      if (!calculations) {
         const message = 'Could not retrieve payroll calculations for this period.';
          // Log the request and response
          await WhatsAppRequestLog.create({
            from: employee.personalDetails.mobileNumber,
            to: process.env.WHATSAPP_PHONE_NUMBER_ID,
            requestType: requestType, // Use the allowed enum value
            requestData: logData,
            // Store response as a JSON string
            response: JSON.stringify({ status: 'failed', message }),
            timestamp: new Date(),
             // Ensure required fields are populated
            employee: employee._id,
            company: employee.company?._id,
            mobileNumber: employee.personalDetails.mobileNumber,
            requestText: requestText
           });
         return { success: false, message };
      }

      // --- Replace old PDF generation with new pdf-lib generator ---

      // Determine PDF password based on best practice (prefer personalDetails, fallback to top-level)
      let pdfPassword = undefined;
      const pd = employee.personalDetails || {};
      const idType = pd.idType || employee.idType;
      const idNumber = pd.idNumber || employee.idNumber;
      const passportNumber = pd.passportNumber || employee.passportNumber;

      if (idType === 'rsa' && idNumber) {
        pdfPassword = idNumber;
      } else if (idType === 'passport' && passportNumber) {
        pdfPassword = passportNumber;
      } // else: no password for 'none', 'other', or missing

      // Log data before generating PDF (services/whatsappService.js)

      // Use the new generatePayslipPdfLib function with password
      const pdfBuffer = await generatePayslipPdfLib(employee, payrollPeriod, calculations, pdfPassword);

      // Upload the PDF buffer to secure storage (e.g., S3)
      const filename = `payslip_${employee._id}_${moment(payrollPeriod.endDate).format('YYYY-MM')}.pdf`;
      const uploadResult = await this.uploadToSecureStorage(pdfBuffer, filename);

      if (!uploadResult || !uploadResult.url) {
         const message = 'Failed to upload payslip PDF to storage.';
          // Log the request and response
          await WhatsAppRequestLog.create({
            from: employee.personalDetails.mobileNumber,
            to: process.env.WHATSAPP_PHONE_NUMBER_ID,
            requestType: requestType, // Use the allowed enum value
            requestData: logData,
            // Store response as a JSON string
            response: JSON.stringify({ status: 'failed', message }),
            timestamp: new Date(),
             // Ensure required fields are populated
            employee: employee._id,
            company: employee.company?._id,
            mobileNumber: employee.personalDetails.mobileNumber,
            requestText: requestText
           });
         return { success: false, message };
      }

      const documentUrl = uploadResult.url; // The pre-signed S3 URL
      const caption = `Your Payslip for ${moment(payrollPeriod.endDate).format('MMMM YYYY')}`;

      // Send the document via WhatsApp
      await this.sendDocument(employee.personalDetails.mobileNumber, documentUrl, caption);

      // Notify the user about password protection if applied
      if (pdfPassword) {
        let passwordMsg = '';
        if (idType === 'rsa') {
          passwordMsg = 'Your payslip PDF is password-protected. Please use your South African ID number to open it.';
        } else if (idType === 'passport') {
          passwordMsg = 'Your payslip PDF is password-protected. Please use your passport number to open it.';
        }
        if (passwordMsg) {
          await this.sendMessage(employee.personalDetails.mobileNumber, passwordMsg);
        }
      }

       // Log the request and response
       await WhatsAppRequestLog.create({
        from: employee.personalDetails.mobileNumber,
        to: process.env.WHATSAPP_PHONE_NUMBER_ID,
        requestType: requestType, // Use the allowed enum value
        requestData: logData,
        // Store response as a JSON string
        response: JSON.stringify({ status: 'success', message: 'Payslip PDF sent successfully.' }),
        timestamp: new Date(),
         // Ensure required fields are populated
        employee: employee._id,
        company: employee.company?._id,
        mobileNumber: employee.personalDetails.mobileNumber,
        requestText: requestText
       });

      return { success: true, documentUrl, caption };

    } catch (error) {
      console.error('Error handling payslip request:', error);
       // Log the error - try to include available info even on failure
       await WhatsAppRequestLog.create({
        from: employee?.personalDetails?.mobileNumber, // Use optional chaining
        to: process.env.WHATSAPP_PHONE_NUMBER_ID,
        requestType: requestType, // Use the allowed enum value
        requestData: logData,
        // Store error response as a JSON string
        response: JSON.stringify({ status: 'error', message: error.message, stack: error.stack }),
        timestamp: new Date(),
         // Ensure required fields are populated if possible
        employee: employee?._id, // Use optional chaining
        company: employee?.company?._id, // Use optional chaining
        mobileNumber: employee?.personalDetails?.mobileNumber, // Use optional chaining
        requestText: requestText // Use determined request text
       });
      return { success: false, message: 'An error occurred while processing your payslip request.' };
    }
  }

  /**
   * Send an interactive list of leave types for the employee's company.
   * @param {object} employee - The employee object
   */
  async sendLeaveTypeList(employee) {
    try {
      const companyCode = employee.company.companyCode;
      const apiUrl = `${process.env.BASE_URL}/internal-api/leave-types/${companyCode}`;

      const response = await axios.get(apiUrl);
      if (!response.data.success || !Array.isArray(response.data.data) || response.data.data.length === 0) {
        await this.sendMessage(employee.personalDetails.mobileNumber, 'No leave types are currently available.');
        return;
      }

      // Build WhatsApp interactive list items with better validation
      const listItems = response.data.data.map((type, index) => ({
        id: `leave_type_${type._id}`,
        title: type.name || `Leave Type ${index + 1}`,
        description: (type.description || type.category || '').substring(0, 72)
      }));


      // Save session state: awaiting leave type selection
      await sessionService.setSession(employee.personalDetails.mobileNumber, {
        step: 'awaiting_leave_type',
        employeeId: employee._id,
        companyId: employee.company._id,
        leaveTypes: response.data.data // Store leave types for fallback
      });

      try {
        // Send the interactive list
        await this.sendInteractiveList(
          employee.personalDetails.mobileNumber,
          `Hi ${employee.firstName}, please select the type of leave you want to request:`,
          'Select Leave Type',
          'Leave Types',
          listItems
        );
      } catch (interactiveError) {
        console.error('Interactive list failed, falling back to text menu:', interactiveError);

        // Fallback to text-based menu if interactive list fails
        let textMenu = `Hi ${employee.firstName}, please select the type of leave you want to request:\n\n`;
        response.data.data.forEach((type, index) => {
          textMenu += `${index + 1}. ${type.name}\n`;
        });
        textMenu += '\nReply with the number of your choice (e.g., "1" for the first option).';

        await this.sendMessage(employee.personalDetails.mobileNumber, textMenu);

        // Update session to handle text-based selection
        await sessionService.setSession(employee.personalDetails.mobileNumber, {
          step: 'awaiting_leave_type_text',
          employeeId: employee._id,
          companyId: employee.company._id,
          leaveTypes: response.data.data
        });
      }

      // Log the leave type list request
      await WhatsAppRequestLog.create({
        employee: employee._id,
        company: employee.company._id,
        mobileNumber: employee.personalDetails.mobileNumber,
        requestType: 'leave',
        requestText: 'Leave type selection menu requested',
        timestamp: new Date(),
        status: 'success',
        response: JSON.stringify({
          status: 'success',
          message: 'Leave type selection menu sent',
          availableLeaveTypes: response.data.data.length
        })
      });

    } catch (error) {
      console.error('Error sending leave type list:', error);
      await this.sendMessage(employee.personalDetails.mobileNumber, 'Sorry, there was an error fetching leave types. Please try again later.');

      // Log the failed request
      try {
        await WhatsAppRequestLog.create({
          employee: employee._id,
          company: employee.company._id,
          mobileNumber: employee.personalDetails.mobileNumber,
          requestType: 'leave',
          requestText: 'Leave type selection menu requested',
          timestamp: new Date(),
          status: 'failed',
          response: JSON.stringify({
            status: 'failed',
            message: 'Error fetching leave types',
            error: error.message
          })
        });
      } catch (logError) {
        console.error('Error logging failed leave type list request:', logError);
      }
    }
  }

  /**
   * Handle leave type selection and start new leave request flow
   */
  async handleLeaveTypeSelection(employee, leaveTypeId) {
    // Save leave type in session
    await sessionService.setSession(employee.personalDetails.mobileNumber, {
      leaveRequest: {
        step: 'awaiting_from_date',
        leaveTypeId
      }
    });
    await this.sendMessage(employee.personalDetails.mobileNumber, 'Please input FROM date e.g. yyyy-mm-dd');
  }

  /**
   * Handle leave request steps after leave type selection
   */
  async handleModernLeaveRequest(employee, message) {
    const session = await sessionService.getSession(employee.personalDetails.mobileNumber);
    if (!session || !session.leaveRequest) {
      await this.sendMessage(employee.personalDetails.mobileNumber, 'Please start a leave request by selecting a leave type.');
      return;
    }
    const leaveReq = session.leaveRequest;
    if (leaveReq.step === 'awaiting_from_date') {
      // Save from date and prompt for to date
      leaveReq.fromDate = message.trim();
      leaveReq.step = 'awaiting_to_date';
      await sessionService.setSession(employee.personalDetails.mobileNumber, { leaveRequest: leaveReq });
      await this.sendMessage(employee.personalDetails.mobileNumber, 'Please input TO date e.g. yyyy-mm-dd');
      return;
    }
    if (leaveReq.step === 'awaiting_to_date') {
      // Save to date and prompt for reason
      leaveReq.toDate = message.trim();
      leaveReq.step = 'awaiting_reason';
      await sessionService.setSession(employee.personalDetails.mobileNumber, { leaveRequest: leaveReq });
      await this.sendMessage(employee.personalDetails.mobileNumber, 'Please enter reason');
      return;
    }
    if (leaveReq.step === 'awaiting_reason') {
      leaveReq.reason = message.trim();

      const fromDateMoment = moment(leaveReq.fromDate, 'YYYY-MM-DD', true); // Strict parsing
      const toDateMoment = moment(leaveReq.toDate, 'YYYY-MM-DD', true); // Strict parsing

      if (!fromDateMoment.isValid() || !toDateMoment.isValid()) {
        await this.sendMessage(employee.personalDetails.mobileNumber, 'Invalid date format. Please use yyyy-mm-dd for both from and to dates. Please start a new leave request by typing \"leave\"');
        await sessionService.removeSession(employee.personalDetails.mobileNumber); // Clear session to restart
        return;
      }

      if (fromDateMoment.isAfter(toDateMoment)) {
        await this.sendMessage(employee.personalDetails.mobileNumber, 'From date cannot be after to date. Please start a new leave request by typing \"leave\"');
        await sessionService.removeSession(employee.personalDetails.mobileNumber); // Clear session to restart
        return;
      }

      const numberOfDays = toDateMoment.diff(fromDateMoment, 'days') + 1; // Inclusive of start and end dates

      // Store calculated values in session for potential document upload step
      leaveReq.numberOfDays = numberOfDays;
      leaveReq.fromDateMoment = fromDateMoment.format('YYYY-MM-DD');
      leaveReq.toDateMoment = toDateMoment.format('YYYY-MM-DD');

      // Check if this is sick leave and requires document attachment
      const leaveType = await LeaveType.findById(leaveReq.leaveTypeId);
      const requiresDocument = this.checkSickLeaveDocumentRequirement(leaveType, fromDateMoment, toDateMoment, numberOfDays);

      if (requiresDocument) {
        // Set step to await document and save session
        leaveReq.step = 'awaiting_document';
        await sessionService.setSession(employee.personalDetails.mobileNumber, { leaveRequest: leaveReq });

        const documentMessage = `📋 Supporting Document Required\n\n` +
          `Your sick leave request (${numberOfDays} days) requires a supporting document because:\n` +
          `${this.getSickLeaveDocumentReason(fromDateMoment, toDateMoment, numberOfDays)}\n\n` +
          `Please attach a medical certificate or doctor's note.\n\n` +
          `You can:\n` +
          `• Send the document as an image/photo\n` +
          `• Send "skip" to submit without document (may require HR approval)\n` +
          `• Send "cancel" to cancel this request`;

        await this.sendMessage(employee.personalDetails.mobileNumber, documentMessage);
        return;
      }

      // If no document required, proceed with saving the leave request
      try {
        const newLeaveRequest = new LeaveRequest({
        company: employee.company._id,
          employee: employee._id,
          leaveType: leaveReq.leaveTypeId,
          startDate: fromDateMoment.toDate(),
          endDate: toDateMoment.toDate(),
          numberOfDays: numberOfDays,
          reason: leaveReq.reason,
          status: 'pending', // Default status for new requests
          createdBy: employee._id, // Employee creating the request
        });

        await newLeaveRequest.save();

        // Fetch leave type name for summary
        const leaveType = await LeaveType.findById(leaveReq.leaveTypeId);
        const leaveTypeName = leaveType ? leaveType.name : 'Leave';
        const summary = `Leave Application Summary:\nType: ${leaveTypeName}\nFrom: ${leaveReq.fromDate}\nTo: ${leaveReq.toDate}\nReason: ${leaveReq.reason}\nDays: ${numberOfDays}`;
        await this.sendMessage(employee.personalDetails.mobileNumber, summary);

        // Generate context-aware submission message
        const submissionMessage = this.generateLeaveResponseMessage(leaveTypeName, 'submission');
        await this.sendMessage(employee.personalDetails.mobileNumber, submissionMessage);

        // Log the successful leave request to WhatsAppRequestLog
        await WhatsAppRequestLog.create({
          employee: employee._id,
          company: employee.company._id,
          mobileNumber: employee.personalDetails.mobileNumber,
          requestType: 'leave',
          requestText: `Leave request: ${leaveTypeName} from ${leaveReq.fromDate} to ${leaveReq.toDate}`,
          timestamp: new Date(),
          status: 'success',
          response: JSON.stringify({
            status: 'success',
            message: 'Leave request submitted successfully',
            leaveRequestId: newLeaveRequest._id,
            leaveType: leaveTypeName,
            numberOfDays: numberOfDays
          }),
          leaveRequest: newLeaveRequest._id
        });

      } catch (error) {
        console.error('Error saving leave request:', error);
        await this.sendMessage(employee.personalDetails.mobileNumber, 'Sorry, there was an error submitting your leave request. Please try again later.');

        // Log the failed leave request to WhatsAppRequestLog
        try {
          await WhatsAppRequestLog.create({
            employee: employee._id,
            company: employee.company._id,
            mobileNumber: employee.personalDetails.mobileNumber,
            requestType: 'leave',
            requestText: `Leave request: ${leaveReq.fromDate} to ${leaveReq.toDate}`,
            timestamp: new Date(),
            status: 'failed',
            response: JSON.stringify({
              status: 'failed',
              message: 'Error submitting leave request',
              error: error.message
            })
          });
        } catch (logError) {
          console.error('Error logging failed leave request:', logError);
        }
      } finally {
        // Clear session regardless of success or failure in saving the request
        await sessionService.removeSession(employee.personalDetails.mobileNumber);
      }
      return;
    }
    if (leaveReq.step === 'awaiting_document') {
      // Handle document upload or skip/cancel commands
      const messageText = message.trim().toLowerCase();

      if (messageText === 'skip') {
        // Proceed without document but mark as requiring HR attention
        leaveReq.documentSkipped = true;
        leaveReq.step = 'ready_to_save';
      } else if (messageText === 'cancel') {
        // Cancel the leave request
        await sessionService.removeSession(employee.personalDetails.mobileNumber);
        await this.sendMessage(employee.personalDetails.mobileNumber, 'Leave request cancelled. You can start a new request anytime by typing "leave".');
        return;
      } else {
        // For now, accept any text as document confirmation (in real implementation, handle actual file uploads)
        leaveReq.documentProvided = true;
        leaveReq.documentNote = message.trim();
        leaveReq.step = 'ready_to_save';
        await this.sendMessage(employee.personalDetails.mobileNumber, '✅ Document received. Processing your leave request...');
      }

      // If ready to save, proceed with creating the leave request
      if (leaveReq.step === 'ready_to_save') {
        try {
          // Prepare leave request data
          const leaveRequestData = {
            company: employee.company._id,
            employee: employee._id,
            leaveType: leaveReq.leaveTypeId,
            startDate: moment(leaveReq.fromDateMoment).toDate(),
            endDate: moment(leaveReq.toDateMoment).toDate(),
            numberOfDays: leaveReq.numberOfDays,
            reason: leaveReq.reason,
            status: 'pending',
            createdBy: employee._id
          };

          // Add document information to reason if applicable
          if (leaveReq.documentProvided) {
            leaveRequestData.reason += '\n\n[Supporting document provided via WhatsApp]';
            if (leaveReq.documentNote) {
              leaveRequestData.reason += `\nDocument note: ${leaveReq.documentNote}`;
            }
          } else if (leaveReq.documentSkipped) {
            leaveRequestData.reason += '\n\n[Supporting document required but not provided - requires HR review]';
          }

          const newLeaveRequest = new LeaveRequest(leaveRequestData);

          await newLeaveRequest.save();

          // Fetch leave type name for summary
          const leaveType = await LeaveType.findById(leaveReq.leaveTypeId);
          const leaveTypeName = leaveType ? leaveType.name : 'Leave';

          let summary = `Leave Application Summary:\nType: ${leaveTypeName}\nFrom: ${leaveReq.fromDateMoment}\nTo: ${leaveReq.toDateMoment}\nReason: ${leaveReq.reason}\nDays: ${leaveReq.numberOfDays}`;

          if (leaveReq.documentProvided) {
            summary += '\n✅ Supporting document: Provided';
          } else if (leaveReq.documentSkipped) {
            summary += '\n⚠️ Supporting document: Skipped (requires HR review)';
          }

          await this.sendMessage(employee.personalDetails.mobileNumber, summary);

          // Generate context-aware submission message with document handling
          let finalMessage;
          if (leaveReq.documentSkipped) {
            finalMessage = 'Your leave request has been submitted for approval. Note: Since no supporting document was provided, this request will require additional HR review.';
          } else {
            finalMessage = this.generateLeaveResponseMessage(leaveTypeName, 'submission');
          }

          await this.sendMessage(employee.personalDetails.mobileNumber, finalMessage);

          // Log the successful leave request with document handling to WhatsAppRequestLog
          await WhatsAppRequestLog.create({
            employee: employee._id,
            company: employee.company._id,
            mobileNumber: employee.personalDetails.mobileNumber,
            requestType: 'leave',
            requestText: `Leave request: ${leaveTypeName} from ${leaveReq.fromDateMoment} to ${leaveReq.toDateMoment} (with document handling)`,
            timestamp: new Date(),
            status: 'success',
            response: JSON.stringify({
              status: 'success',
              message: 'Leave request with document handling submitted successfully',
              leaveRequestId: newLeaveRequest._id,
              leaveType: leaveTypeName,
              numberOfDays: leaveReq.numberOfDays,
              documentProvided: leaveReq.documentProvided || false,
              documentSkipped: leaveReq.documentSkipped || false
            }),
            leaveRequest: newLeaveRequest._id
          });

        } catch (error) {
          console.error('Error saving leave request with document handling:', error);
          await this.sendMessage(employee.personalDetails.mobileNumber, 'Sorry, there was an error submitting your leave request. Please try again later.');

          // Log the failed leave request with document handling to WhatsAppRequestLog
          try {
            await WhatsAppRequestLog.create({
              employee: employee._id,
              company: employee.company._id,
              mobileNumber: employee.personalDetails.mobileNumber,
              requestType: 'leave',
              requestText: `Leave request: ${leaveReq.fromDateMoment} to ${leaveReq.toDateMoment} (with document handling)`,
              timestamp: new Date(),
              status: 'failed',
              response: JSON.stringify({
                status: 'failed',
                message: 'Error submitting leave request with document handling',
                error: error.message
              })
            });
          } catch (logError) {
            console.error('Error logging failed leave request with document handling:', logError);
          }
        } finally {
          // Clear session regardless of success or failure
          await sessionService.removeSession(employee.personalDetails.mobileNumber);
        }
      }
      return;
    }
    // If session state is invalid, reset
    await sessionService.removeSession(employee.personalDetails.mobileNumber);
    await this.sendMessage(employee.personalDetails.mobileNumber, 'Sorry, something went wrong. Please start a new leave request.');
  }

  /**
   * Check if sick leave requires document attachment based on duration and dates
   * @param {object} leaveType - The leave type object
   * @param {moment} fromDate - Start date moment object
   * @param {moment} toDate - End date moment object
   * @param {number} numberOfDays - Number of leave days
   * @returns {boolean} - Whether document is required
   */
  checkSickLeaveDocumentRequirement(leaveType, fromDate, toDate, numberOfDays) {
    // Only check for sick leave types
    if (!leaveType || leaveType.category !== 'sick') {
      return false;
    }

    // Check condition 1: More than 2 days
    if (numberOfDays > 2) {
      return true;
    }

    // Check condition 2: Includes Monday (1) or Friday (5)
    const includesMonday = this.dateRangeIncludesWeekday(fromDate, toDate, 1);
    const includesFriday = this.dateRangeIncludesWeekday(fromDate, toDate, 5);

    if (includesMonday || includesFriday) {
      return true;
    }

    return false;
  }

  /**
   * Check if a date range includes a specific weekday
   * @param {moment} fromDate - Start date
   * @param {moment} toDate - End date
   * @param {number} weekday - Weekday number (1=Monday, 5=Friday)
   * @returns {boolean} - Whether the range includes the weekday
   */
  dateRangeIncludesWeekday(fromDate, toDate, weekday) {
    let currentDate = moment(fromDate);
    const endDate = moment(toDate);

    while (currentDate.isSameOrBefore(endDate)) {
      if (currentDate.isoWeekday() === weekday) {
        return true;
      }
      currentDate.add(1, 'day');
    }
    return false;
  }

  /**
   * Get the reason why a document is required for sick leave
   * @param {moment} fromDate - Start date
   * @param {moment} toDate - End date
   * @param {number} numberOfDays - Number of days
   * @returns {string} - Reason for document requirement
   */
  getSickLeaveDocumentReason(fromDate, toDate, numberOfDays) {
    const reasons = [];

    if (numberOfDays > 2) {
      reasons.push(`• Leave duration is ${numberOfDays} days (more than 2 days)`);
    }

    const includesMonday = this.dateRangeIncludesWeekday(fromDate, toDate, 1);
    const includesFriday = this.dateRangeIncludesWeekday(fromDate, toDate, 5);

    if (includesMonday) {
      reasons.push('• Leave includes a Monday');
    }
    if (includesFriday) {
      reasons.push('• Leave includes a Friday');
    }

    return reasons.join('\n');
  }

  /**
   * Handle legacy text-based leave request format
   * Format: "Leave request: [type] from YYYY-MM-DD to YYYY-MM-DD reason: [reason]"
   */
  async handleLeaveRequest(employee, messageText) {
    try {

      // Parse the leave request message
      const regex = /leave request:\s*(.+?)\s+from\s+(\d{4}-\d{2}-\d{2})\s+to\s+(\d{4}-\d{2}-\d{2})\s+reason:\s*(.+)/i;
      const match = messageText.match(regex);

      if (!match) {
        const errorMessage = 'Invalid leave request format. Please use: "Leave request: [type] from YYYY-MM-DD to YYYY-MM-DD reason: [your reason]"';

        // Log the failed request
        await WhatsAppRequestLog.create({
          employee: employee._id,
          company: employee.company._id,
          mobileNumber: employee.personalDetails.mobileNumber,
          requestType: 'leave',
          requestText: messageText,
          timestamp: new Date(),
          status: 'failed',
          response: JSON.stringify({
            status: 'failed',
            message: errorMessage
          })
        });

        return { success: false, message: errorMessage };
      }

      const [, leaveTypeName, fromDate, toDate, reason] = match;

      // Validate dates
      const fromDateMoment = moment(fromDate, 'YYYY-MM-DD', true);
      const toDateMoment = moment(toDate, 'YYYY-MM-DD', true);

      if (!fromDateMoment.isValid() || !toDateMoment.isValid()) {
        const errorMessage = 'Invalid date format. Please use YYYY-MM-DD format for dates.';

        // Log the failed request
        await WhatsAppRequestLog.create({
          employee: employee._id,
          company: employee.company._id,
          mobileNumber: employee.personalDetails.mobileNumber,
          requestType: 'leave',
          requestText: messageText,
          timestamp: new Date(),
          status: 'failed',
          response: JSON.stringify({
            status: 'failed',
            message: errorMessage
          })
        });

        return { success: false, message: errorMessage };
      }

      if (toDateMoment.isBefore(fromDateMoment)) {
        const errorMessage = 'End date cannot be before start date.';

        // Log the failed request
        await WhatsAppRequestLog.create({
          employee: employee._id,
          company: employee.company._id,
          mobileNumber: employee.personalDetails.mobileNumber,
          requestType: 'leave',
          requestText: messageText,
          timestamp: new Date(),
          status: 'failed',
          response: JSON.stringify({
            status: 'failed',
            message: errorMessage
          })
        });

        return { success: false, message: errorMessage };
      }

      // Find leave type by name
      const LeaveType = require('../models/LeaveType');
      const leaveType = await LeaveType.findOne({
        company: employee.company._id,
        name: { $regex: new RegExp(leaveTypeName.trim(), 'i') }
      });

      if (!leaveType) {
        const errorMessage = `Leave type "${leaveTypeName}" not found. Please contact HR for available leave types.`;

        // Log the failed request
        await WhatsAppRequestLog.create({
          employee: employee._id,
          company: employee.company._id,
          mobileNumber: employee.personalDetails.mobileNumber,
          requestType: 'leave',
          requestText: messageText,
          timestamp: new Date(),
          status: 'failed',
          response: JSON.stringify({
            status: 'failed',
            message: errorMessage
          })
        });

        return { success: false, message: errorMessage };
      }

      // Calculate number of days
      const numberOfDays = toDateMoment.diff(fromDateMoment, 'days') + 1;

      // Create leave request
      const LeaveRequest = require('../models/LeaveRequest');
      const newLeaveRequest = new LeaveRequest({
        company: employee.company._id,
        employee: employee._id,
        leaveType: leaveType._id,
        startDate: fromDateMoment.toDate(),
        endDate: toDateMoment.toDate(),
        numberOfDays: numberOfDays,
        reason: reason.trim(),
        status: 'pending',
        createdBy: employee._id
      });

      await newLeaveRequest.save();

      // Generate context-aware legacy success message
      const successMessage = this.generateLegacyLeaveSuccessMessage(
        leaveType.name,
        fromDate,
        toDate,
        numberOfDays,
        reason.trim()
      );

      // Log the successful request
      await WhatsAppRequestLog.create({
        employee: employee._id,
        company: employee.company._id,
        mobileNumber: employee.personalDetails.mobileNumber,
        requestType: 'leave',
        requestText: messageText,
        timestamp: new Date(),
        status: 'success',
        response: JSON.stringify({
          status: 'success',
          message: 'Leave request submitted successfully',
          leaveRequestId: newLeaveRequest._id,
          leaveType: leaveType.name,
          numberOfDays: numberOfDays
        }),
        leaveRequest: newLeaveRequest._id
      });

      return { success: true, message: successMessage };

    } catch (error) {
      console.error('Error handling legacy leave request:', error);
      const errorMessage = 'Sorry, there was an error processing your leave request. Please try again later.';

      // Log the failed request
      try {
        await WhatsAppRequestLog.create({
          employee: employee._id,
          company: employee.company._id,
          mobileNumber: employee.personalDetails.mobileNumber,
          requestType: 'leave',
          requestText: messageText,
          timestamp: new Date(),
          status: 'failed',
          response: JSON.stringify({
            status: 'failed',
            message: errorMessage,
            error: error.message
          })
        });
      } catch (logError) {
        console.error('Error logging failed legacy leave request:', logError);
      }

      return { success: false, message: errorMessage };
    }
  }

  async handleIRP5Request(employee, taxYear) {
    let requestType = 'irp5';
    let requestText = taxYear ? `IRP5 ${taxYear}` : 'IRP5 request';
    let logData = { employeeId: employee?._id, taxYear: taxYear };

    try {

      // Ensure employee has company populated
      if (!employee.company) {
        await employee.populate('company');
      }

      // If no tax year specified, show available years
      if (!taxYear) {
        const availableYears = await IRP5Service.getAvailableTaxYears(employee._id, employee.company._id);

        if (availableYears.length === 0) {
          const message = 'No payroll data found for IRP5 generation. Please contact HR if you believe this is an error.';
          await WhatsAppRequestLog.create({
            from: employee.personalDetails.mobileNumber,
            to: process.env.WHATSAPP_PHONE_NUMBER_ID,
            requestType: requestType,
            requestData: logData,
            response: JSON.stringify({ status: 'failed', message }),
            timestamp: new Date(),
            employee: employee._id,
            company: employee.company?._id,
            mobileNumber: employee.personalDetails.mobileNumber,
            requestText: requestText
          });
          return { success: false, message };
        }

        // Generate list of available tax years
        const listItems = availableYears.map(year => ({
          id: `irp5_${year.year}`,
          title: `IRP5 ${year.label}`,
          description: `Tax year: ${year.startDate} to ${year.endDate}`
        }));

        await this.sendInteractiveList(
          employee.personalDetails.mobileNumber,
          `Hi ${employee.firstName}, please select the tax year for your IRP5:`,
          'Select Tax Year',
          'Choose IRP5 Tax Year',
          listItems
        );

        await WhatsAppRequestLog.create({
          from: employee.personalDetails.mobileNumber,
          to: process.env.WHATSAPP_PHONE_NUMBER_ID,
          requestType: requestType,
          requestData: logData,
          response: JSON.stringify({ status: 'success', message: 'Tax year selection sent' }),
          timestamp: new Date(),
          employee: employee._id,
          company: employee.company?._id,
          mobileNumber: employee.personalDetails.mobileNumber,
          requestText: requestText
        });

        return { success: true, message: 'Please select a tax year from the list.' };
      }

      // Validate tax year format
      if (!/^\d{4}$/.test(taxYear)) {
        const message = 'Invalid tax year format. Please use YYYY format (e.g., 2025 for 2024/2025 tax year).';
        await WhatsAppRequestLog.create({
          from: employee.personalDetails.mobileNumber,
          to: process.env.WHATSAPP_PHONE_NUMBER_ID,
          requestType: requestType,
          requestData: logData,
          response: JSON.stringify({ status: 'failed', message }),
          timestamp: new Date(),
          employee: employee._id,
          company: employee.company?._id,
          mobileNumber: employee.personalDetails.mobileNumber,
          requestText: requestText
        });
        return { success: false, message };
      }

      // Check if IRP5 data is available for the requested tax year
      const isAvailable = await IRP5Service.isIRP5Available(employee._id, employee.company._id, taxYear);
      if (!isAvailable) {
        const message = `No payroll data found for tax year ${parseInt(taxYear) - 1}/${taxYear}. Please contact HR if you believe this is an error.`;
        await WhatsAppRequestLog.create({
          from: employee.personalDetails.mobileNumber,
          to: process.env.WHATSAPP_PHONE_NUMBER_ID,
          requestType: requestType,
          requestData: logData,
          response: JSON.stringify({ status: 'failed', message }),
          timestamp: new Date(),
          employee: employee._id,
          company: employee.company?._id,
          mobileNumber: employee.personalDetails.mobileNumber,
          requestText: requestText
        });
        return { success: false, message };
      }

      // Get payroll data for the tax year
      const payrollData = await IRP5Service.getEmployeePayrollDataForTaxYear(employee._id, employee.company._id, taxYear);

      // Validate that we have sufficient data for IRP5 generation
      if (!payrollData || payrollData.periods.length === 0) {
        const errorMsg = `No payroll data found for tax year ${parseInt(taxYear) - 1}/${taxYear}. Please ensure payroll has been processed for this period or contact HR for assistance.`;
        console.error('❌ WhatsApp IRP5: No payroll data found:', {
          employeeId: employee._id,
          taxYear,
          payrollData: payrollData
        });

        await WhatsAppRequestLog.create({
          from: employee.personalDetails.mobileNumber,
          to: process.env.WHATSAPP_PHONE_NUMBER_ID,
          requestType: requestType,
          requestData: logData,
          response: JSON.stringify({ status: 'failed', message: errorMsg }),
          timestamp: new Date(),
          employee: employee._id,
          company: employee.company?._id,
          mobileNumber: employee.personalDetails.mobileNumber,
          requestText: requestText
        });

        return { success: false, message: errorMsg };
      }


      // Generate IRP5 PDF
      const pdfBuffer = await generateIRP5PdfLib(employee, taxYear, payrollData);

      // Upload the PDF buffer to secure storage
      const filename = `irp5_${employee._id}_${taxYear}.pdf`;
      const uploadResult = await this.uploadToSecureStorage(pdfBuffer, filename);

      if (!uploadResult || !uploadResult.url) {
        throw new Error('Failed to upload IRP5 document to secure storage');
      }

      const documentUrl = uploadResult.url;
      const caption = `Your IRP5 Tax Certificate for ${parseInt(taxYear) - 1}/${taxYear}`;

      // Send the document via WhatsApp
      await this.sendDocument(employee.personalDetails.mobileNumber, documentUrl, caption);

      await WhatsAppRequestLog.create({
        from: employee.personalDetails.mobileNumber,
        to: process.env.WHATSAPP_PHONE_NUMBER_ID,
        requestType: requestType,
        requestData: logData,
        response: JSON.stringify({ status: 'success', message: 'IRP5 PDF sent successfully.' }),
        timestamp: new Date(),
        employee: employee._id,
        company: employee.company?._id,
        mobileNumber: employee.personalDetails.mobileNumber,
        requestText: requestText
      });

      return { success: true, documentUrl, caption };

    } catch (error) {
      console.error('❌ Error handling WhatsApp IRP5 request:', {
        error: error.message,
        stack: error.stack,
        employeeId: employee._id,
        taxYear,
        companyId: employee.company?._id
      });

      // Provide more specific error messages based on error type
      let userMessage = 'Failed to generate IRP5 document. Please try again later or contact HR.';

      if (error.message.includes('No payroll data')) {
        userMessage = `No payroll records found for tax year ${parseInt(taxYear) - 1}/${taxYear}. Please contact HR to verify your payroll data.`;
      } else if (error.message.includes('template')) {
        userMessage = 'IRP5 template issue detected. Please contact HR for assistance.';
      } else if (error.message.includes('upload') || error.message.includes('storage')) {
        userMessage = 'Document upload failed. Please try again in a few minutes or contact HR.';
      } else if (error.message.includes('PDF')) {
        userMessage = 'PDF generation failed. Please contact HR for assistance.';
      }

      await WhatsAppRequestLog.create({
        from: employee.personalDetails.mobileNumber,
        to: process.env.WHATSAPP_PHONE_NUMBER_ID,
        requestType: requestType,
        requestData: logData,
        response: JSON.stringify({
          status: 'failed',
          message: userMessage,
          technicalError: error.message
        }),
        timestamp: new Date(),
        employee: employee._id,
        company: employee.company?._id,
        mobileNumber: employee.personalDetails.mobileNumber,
        requestText: requestText
      });

      return { success: false, message: userMessage };
    }
  }

  async uploadToSecureStorage(buffer, filename) {
    if (!this.s3BucketName) {
      console.error('S3 bucket name is not configured. Cannot upload to S3.');
      // Fallback to local storage or throw an error, depending on desired behavior
      // For now, let's return null to indicate failure
      return null;
    }

    const uploadParams = {
      Bucket: this.s3BucketName,
      Key: filename, // S3 object key (path and filename)
      Body: buffer, // The PDF buffer
      ContentType: 'application/pdf' // Set content type
    };

    try {
      // Upload the object to S3
      const command = new PutObjectCommand(uploadParams);
      await this.s3Client.send(command);


      // Generate a pre-signed URL for temporary access
      const signedUrl = await getSignedUrl(this.s3Client, new GetObjectCommand({ Bucket: this.s3BucketName, Key: filename }), { expiresIn: 3600 }); // URL valid for 1 hour


      return { url: signedUrl }; // Return the S3 pre-signed URL

    } catch (error) {
      console.error(`Failed to upload file to S3 or generate pre-signed URL: ${error}`);
      // Log the failure to WhatsAppRequestLog if applicable?
      // This function is called within handlePayslipRequest, where logging already happens on failure.
      return null; // Return null or throw error on failure
    }
  }
  
  // Method to update an employee's WhatsApp number
  async updateEmployeeWhatsAppNumber(employeeId, whatsappNumber) {
    try {
      
      const result = await Employee.updateOne(
        { _id: employeeId },
        { $set: { 'personalDetails.mobileNumber': whatsappNumber } }
      );
      
      if (result.modifiedCount > 0) {
        return true;
      } else {
        return false;
      }
    } catch (error) {
      console.error('Error updating employee WhatsApp number:', error);
      return false;
    }
  }

  async sendTemplateMessage(to, templateName) {
    try {
      if (!to || !templateName) throw new Error('Recipient phone number and template name are required');
      const normalizedPhone = to.replace(/[\s+]/g, '');
      const latestToken = process.env.WHATSAPP_ACCESS_TOKEN;
      if (latestToken && latestToken !== this.accessToken) this.accessToken = latestToken;
      const response = await axios.post(
        `${this.baseUrl}/${this.phoneNumberId}/messages`,
        {
          messaging_product: 'whatsapp',
          to: normalizedPhone,
          type: 'template',
          template: {
            name: templateName,
            language: { code: 'en' }
          }
        },
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Generate context-aware legacy format success message
   * @param {string} leaveTypeName - The name of the leave type
   * @param {string} fromDate - Start date
   * @param {string} toDate - End date
   * @param {number} numberOfDays - Number of days
   * @param {string} reason - Leave reason
   * @returns {string} - Complete legacy format message with context-aware ending
   */
  generateLegacyLeaveSuccessMessage(leaveTypeName, fromDate, toDate, numberOfDays, reason) {
    const normalizedLeaveType = leaveTypeName.toLowerCase();

    let contextMessage;
    if (normalizedLeaveType.includes('sick')) {
      contextMessage = 'Take care of yourself and we hope you feel better soon! 🌟 Your request is pending approval.';
    } else if (normalizedLeaveType.includes('family') || normalizedLeaveType.includes('compassionate')) {
      contextMessage = 'We understand the importance of family time and supporting loved ones. 💙 Your request is pending approval.';
    } else {
      // Default message for Annual Leave and other types (keep unchanged)
      contextMessage = 'Your request is pending approval.';
    }

    return `Leave request submitted successfully!\n\nType: ${leaveTypeName}\nFrom: ${fromDate}\nTo: ${toDate}\nDays: ${numberOfDays}\nReason: ${reason}\n\n${contextMessage}`;
  }

  /**
   * Generate context-aware response message based on leave type
   * @param {string} leaveTypeName - The name of the leave type
   * @param {string} messageType - Type of message: 'submission', 'approval', or 'rejection'
   * @param {object} options - Additional options like employeeName, dates, etc.
   * @returns {string} - Context-aware message
   */
  generateLeaveResponseMessage(leaveTypeName, messageType, options = {}) {
    const { employeeName, fromDate, toDate, numberOfDays, rejectionReason } = options;

    // Normalize leave type name for comparison (case-insensitive)
    const normalizedLeaveType = leaveTypeName.toLowerCase();

    if (messageType === 'submission') {
      // Context-aware submission messages
      if (normalizedLeaveType.includes('sick')) {
        return 'Your sick leave request has been submitted for approval. Take care of yourself and we hope you feel better soon! 🌟 You will be notified when your leave is approved.';
      } else if (normalizedLeaveType.includes('family') || normalizedLeaveType.includes('compassionate')) {
        return 'Your family leave request has been submitted for approval. We understand the importance of family time and supporting loved ones. 💙 You will be notified when your leave is approved.';
      } else {
        // Default message for Annual Leave and other types (keep unchanged)
        return 'Your leave request has been submitted for approval. You will be notified when your leave is approved.';
      }
    } else if (messageType === 'approval') {
      // Context-aware approval messages
      if (normalizedLeaveType.includes('sick')) {
        return `Hi ${employeeName},\n\nGood news! Your sick leave request for ${leaveTypeName} from ${fromDate} to ${toDate} (${numberOfDays} days) has been APPROVED. 🌟\n\nTake care of yourself and get well soon!\n\nPanda Payroll`;
      } else if (normalizedLeaveType.includes('family') || normalizedLeaveType.includes('compassionate')) {
        return `Hi ${employeeName},\n\nGood news! Your family leave request for ${leaveTypeName} from ${fromDate} to ${toDate} (${numberOfDays} days) has been APPROVED. 💙\n\nWe hope everything goes well with your family. Take care!\n\nPanda Payroll`;
      } else {
        // Default message for Annual Leave and other types (keep unchanged)
        return `Hi ${employeeName},\n\nGood news! Your leave request for ${leaveTypeName} from ${fromDate} to ${toDate} (${numberOfDays} days) has been APPROVED. 🎉\n\nEnjoy your time off!\n\nPanda Payroll`;
      }
    } else if (messageType === 'rejection') {
      // Context-aware rejection messages
      const reasonText = rejectionReason ? `\n\nReason: ${rejectionReason}` : '';

      if (normalizedLeaveType.includes('sick')) {
        return `Hi ${employeeName},\n\nWe regret to inform you that your sick leave request for ${leaveTypeName} from ${fromDate} to ${toDate} (${numberOfDays} days) has been REJECTED.${reasonText}\n\nPlease contact HR if you have any questions or need to discuss this further.\n\nPanda Payroll`;
      } else if (normalizedLeaveType.includes('family') || normalizedLeaveType.includes('compassionate')) {
        return `Hi ${employeeName},\n\nWe regret to inform you that your family leave request for ${leaveTypeName} from ${fromDate} to ${toDate} (${numberOfDays} days) has been REJECTED.${reasonText}\n\nPlease contact HR if you have any questions or need to discuss this further.\n\nPanda Payroll`;
      } else {
        // Default message for Annual Leave and other types
        return `Hi ${employeeName},\n\nWe regret to inform you that your leave request for ${leaveTypeName} from ${fromDate} to ${toDate} (${numberOfDays} days) has been REJECTED.${reasonText}\n\nPlease contact HR if you have any questions or need to discuss this further.\n\nPanda Payroll`;
      }
    }

    // Fallback to default message
    if (messageType === 'submission') {
      return 'Your leave request has been submitted for approval. You will be notified when your leave is approved.';
    } else if (messageType === 'approval') {
      return `Hi ${employeeName},\n\nGood news! Your leave request for ${leaveTypeName} from ${fromDate} to ${toDate} (${numberOfDays} days) has been APPROVED. 🎉\n\nEnjoy your time off!\n\nPanda Payroll`;
    } else if (messageType === 'rejection') {
      const reasonText = rejectionReason ? `\n\nReason: ${rejectionReason}` : '';
      return `Hi ${employeeName},\n\nWe regret to inform you that your leave request for ${leaveTypeName} from ${fromDate} to ${toDate} (${numberOfDays} days) has been REJECTED.${reasonText}\n\nPlease contact HR if you have any questions.\n\nPanda Payroll`;
    }

    return 'Leave request processed.';
  }

  /**
   * Sends a WhatsApp notification to an employee when their leave request is approved.
   * @param {object} leaveRequest - The LeaveRequest document.
   */
  async sendLeaveApprovalNotification(leaveRequest) {
    try {
      // Ensure employee and leaveType are populated
      if (!leaveRequest.employee.personalDetails || !leaveRequest.leaveType.name) {
        await leaveRequest.populate([
          { path: 'employee', select: 'personalDetails.mobileNumber firstName lastName' },
          { path: 'leaveType', select: 'name' }
        ]);
      }

      const employeeMobile = leaveRequest.employee.personalDetails.mobileNumber;
      const employeeName = leaveRequest.employee.firstName;
      const leaveTypeName = leaveRequest.leaveType.name;
      const fromDate = moment(leaveRequest.startDate).format('YYYY-MM-DD');
      const toDate = moment(leaveRequest.endDate).format('YYYY-MM-DD');
      const numberOfDays = leaveRequest.numberOfDays;

      // Generate context-aware approval message
      const message = this.generateLeaveResponseMessage(leaveTypeName, 'approval', {
        employeeName,
        fromDate,
        toDate,
        numberOfDays
      });

      // Send message and only update flag if successful
      const messageResult = await this.sendMessage(employeeMobile, message);

      // Only update notificationSent status if message was actually sent successfully
      if (messageResult) {
        leaveRequest.notificationSent = true;
        await leaveRequest.save();
      }

    } catch (error) {
      console.error('Error sending WhatsApp leave approval notification:', error);
      // Do not update notificationSent flag on error to allow retry
      // Do not rethrow, as the core approval process should not be blocked by notification failure
    }
  }

  /**
   * Sends a WhatsApp notification to an employee when their leave request is rejected.
   * @param {object} leaveRequest - The LeaveRequest document.
   * @param {string} rejectionReason - The reason for rejection.
   */
  async sendLeaveRejectionNotification(leaveRequest, rejectionReason) {
    try {
      // Ensure employee and leaveType are populated
      if (!leaveRequest.employee.personalDetails || !leaveRequest.leaveType.name) {
        await leaveRequest.populate([
          { path: 'employee', select: 'personalDetails.mobileNumber firstName lastName' },
          { path: 'leaveType', select: 'name' }
        ]);
      }

      const employeeMobile = leaveRequest.employee.personalDetails.mobileNumber;
      const employeeName = leaveRequest.employee.firstName;
      const leaveTypeName = leaveRequest.leaveType.name;
      const fromDate = moment(leaveRequest.startDate).format('YYYY-MM-DD');
      const toDate = moment(leaveRequest.endDate).format('YYYY-MM-DD');
      const numberOfDays = leaveRequest.numberOfDays;

      // Generate context-aware rejection message
      const message = this.generateLeaveResponseMessage(leaveTypeName, 'rejection', {
        employeeName,
        fromDate,
        toDate,
        numberOfDays,
        rejectionReason
      });

      // Send message and only update flag if successful
      const messageResult = await this.sendMessage(employeeMobile, message);

      // Only update notificationSent status if message was actually sent successfully
      if (messageResult) {
        leaveRequest.notificationSent = true;
        await leaveRequest.save();
      }

    } catch (error) {
      console.error('Error sending WhatsApp leave rejection notification:', error);
      // Do not update notificationSent flag on error to allow retry
      // Do not rethrow, as the core rejection process should not be blocked by notification failure
    }
  }
}

// Create and export a singleton instance
const whatsappService = new WhatsAppService();
module.exports = whatsappService; 