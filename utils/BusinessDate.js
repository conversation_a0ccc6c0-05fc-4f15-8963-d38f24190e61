/**
 * BusinessDate - Timezone-independent date operations for South African business logic
 * 
 * This utility class provides pure mathematical date operations without any timezone
 * dependencies, ensuring consistent behavior across all server environments.
 * 
 * All dates are handled as YYYY-MM-DD strings representing South African business dates.
 */

class BusinessDate {
  /**
   * Get today's business date in YYYY-MM-DD format
   * Uses UTC to avoid timezone dependencies - completely timezone-independent
   * @returns {string} Today's date in YYYY-MM-DD format
   */
  static today() {
    const now = new Date();
    const year = now.getUTCFullYear();
    const month = String(now.getUTCMonth() + 1).padStart(2, '0');
    const day = String(now.getUTCDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  /**
   * Format a Date object to YYYY-MM-DD string
   * @param {Date} date - Date object to format
   * @returns {string} Date in YYYY-MM-DD format
   */
  static formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  /**
   * Parse YYYY-MM-DD string to Date object (for calculations only)
   * @param {string} dateStr - Date string in YYYY-MM-DD format
   * @returns {Date} Date object
   */
  static parseDate(dateStr) {
    if (!this.isValid(dateStr)) {
      throw new Error(`Invalid date format: ${dateStr}. Expected YYYY-MM-DD`);
    }
    const [year, month, day] = dateStr.split('-').map(Number);
    return new Date(year, month - 1, day);
  }

  /**
   * Validate YYYY-MM-DD date string format and value
   * @param {string} dateStr - Date string to validate
   * @returns {boolean} True if valid
   */
  static isValid(dateStr) {
    if (typeof dateStr !== 'string') return false;
    if (!/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) return false;
    
    const [year, month, day] = dateStr.split('-').map(Number);
    const date = new Date(year, month - 1, day);
    
    return date.getFullYear() === year &&
           date.getMonth() === month - 1 &&
           date.getDate() === day;
  }

  /**
   * Add days to a date string
   * @param {string} dateStr - Date in YYYY-MM-DD format
   * @param {number} days - Number of days to add (can be negative)
   * @returns {string} New date in YYYY-MM-DD format
   */
  static addDays(dateStr, days) {
    const date = this.parseDate(dateStr);
    date.setDate(date.getDate() + days);
    return this.formatDate(date);
  }

  /**
   * Add months to a date string
   * @param {string} dateStr - Date in YYYY-MM-DD format
   * @param {number} months - Number of months to add (can be negative)
   * @returns {string} New date in YYYY-MM-DD format
   */
  static addMonths(dateStr, months) {
    const date = this.parseDate(dateStr);
    date.setMonth(date.getMonth() + months);
    return this.formatDate(date);
  }

  /**
   * Get the last day of the month for a given date
   * @param {string} dateStr - Date in YYYY-MM-DD format
   * @returns {string} Last day of month in YYYY-MM-DD format
   */
  static endOfMonth(dateStr) {
    const [year, month] = dateStr.split('-').map(Number);
    const lastDay = new Date(year, month, 0).getDate();
    return `${year}-${String(month).padStart(2, '0')}-${String(lastDay).padStart(2, '0')}`;
  }

  /**
   * Get the first day of the month for a given date
   * @param {string} dateStr - Date in YYYY-MM-DD format
   * @returns {string} First day of month in YYYY-MM-DD format
   */
  static startOfMonth(dateStr) {
    const [year, month] = dateStr.split('-').map(Number);
    return `${year}-${String(month).padStart(2, '0')}-01`;
  }

  /**
   * Set a specific day of the month
   * @param {string} dateStr - Date in YYYY-MM-DD format
   * @param {number} day - Day of month (1-31)
   * @returns {string} Date with specified day in YYYY-MM-DD format
   */
  static setDayOfMonth(dateStr, day) {
    const [year, month] = dateStr.split('-').map(Number);
    const date = new Date(year, month - 1, day);
    return this.formatDate(date);
  }

  /**
   * Get the day of the month (1-31)
   * @param {string} dateStr - Date in YYYY-MM-DD format
   * @returns {number} Day of month (1-31)
   */
  static getDayOfMonth(dateStr) {
    return this.parseDate(dateStr).getDate();
  }

  /**
   * Calculate days between two dates (inclusive)
   * @param {string} startDate - Start date in YYYY-MM-DD format
   * @param {string} endDate - End date in YYYY-MM-DD format
   * @returns {number} Number of days between dates (inclusive)
   */
  static daysBetween(startDate, endDate) {
    const start = this.parseDate(startDate);
    const end = this.parseDate(endDate);
    const diffTime = end.getTime() - start.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 for inclusive
  }

  /**
   * Check if first date is before second date
   * @param {string} date1 - First date in YYYY-MM-DD format
   * @param {string} date2 - Second date in YYYY-MM-DD format
   * @returns {boolean} True if date1 is before date2
   */
  static isBefore(date1, date2) {
    return date1 < date2; // String comparison works for YYYY-MM-DD format
  }

  /**
   * Check if first date is after second date
   * @param {string} date1 - First date in YYYY-MM-DD format
   * @param {string} date2 - Second date in YYYY-MM-DD format
   * @returns {boolean} True if date1 is after date2
   */
  static isAfter(date1, date2) {
    return date1 > date2; // String comparison works for YYYY-MM-DD format
  }

  /**
   * Check if two dates are the same
   * @param {string} date1 - First date in YYYY-MM-DD format
   * @param {string} date2 - Second date in YYYY-MM-DD format
   * @returns {boolean} True if dates are the same
   */
  static isSame(date1, date2) {
    return date1 === date2;
  }

  /**
   * Check if first date is same or before second date
   * @param {string} date1 - First date in YYYY-MM-DD format
   * @param {string} date2 - Second date in YYYY-MM-DD format
   * @returns {boolean} True if date1 is same or before date2
   */
  static isSameOrBefore(date1, date2) {
    return date1 <= date2;
  }

  /**
   * Check if first date is same or after second date
   * @param {string} date1 - First date in YYYY-MM-DD format
   * @param {string} date2 - Second date in YYYY-MM-DD format
   * @returns {boolean} True if date1 is same or after date2
   */
  static isSameOrAfter(date1, date2) {
    return date1 >= date2;
  }

  /**
   * Get the day of week for a date (0 = Sunday, 6 = Saturday)
   * @param {string} dateStr - Date in YYYY-MM-DD format
   * @returns {number} Day of week (0-6)
   */
  static getDayOfWeek(dateStr) {
    return this.parseDate(dateStr).getDay();
  }

  /**
   * Find the next occurrence of a specific day of week
   * @param {string} dateStr - Starting date in YYYY-MM-DD format
   * @param {number} targetDay - Target day of week (0 = Sunday, 6 = Saturday)
   * @returns {string} Next occurrence of target day in YYYY-MM-DD format
   */
  static getNextDayOfWeek(dateStr, targetDay) {
    const currentDay = this.getDayOfWeek(dateStr);
    let daysToAdd = (targetDay - currentDay + 7) % 7;
    if (daysToAdd === 0) daysToAdd = 7; // If already on target day, go to next week
    return this.addDays(dateStr, daysToAdd);
  }

  /**
   * Convert various date inputs to YYYY-MM-DD string format
   * @param {string|Date} input - Date input to convert
   * @returns {string} Date in YYYY-MM-DD format
   */
  static normalize(input) {
    if (typeof input === 'string') {
      if (this.isValid(input)) {
        return input;
      }
      // Try to parse other string formats
      const date = new Date(input);
      if (!isNaN(date.getTime())) {
        return this.formatDate(date);
      }
    }
    
    if (input instanceof Date) {
      return this.formatDate(input);
    }
    
    throw new Error(`Cannot normalize date input: ${input}`);
  }

  /**
   * Convert business date to Date object for database storage
   * @param {string} dateStr - Date in YYYY-MM-DD format
   * @param {boolean} endOfDay - If true, set to end of day (23:59:59.999Z)
   * @returns {Date} Date object for database storage
   */
  static toDate(dateStr, endOfDay = false) {
    if (!this.isValid(dateStr)) {
      throw new Error(`Invalid date format: ${dateStr}. Expected YYYY-MM-DD`);
    }
    if (endOfDay) {
      return new Date(dateStr + 'T23:59:59.999Z');
    }
    return new Date(dateStr + 'T00:00:00.000Z');
  }

  /**
   * Convert Date object back to YYYY-MM-DD string using UTC to avoid timezone issues
   * @param {Date} dateObj - Date object to convert
   * @returns {string} Date in YYYY-MM-DD format
   */
  static fromDate(dateObj) {
    if (!(dateObj instanceof Date)) {
      throw new Error('Input must be a Date object');
    }
    // Use UTC methods to avoid timezone conversion issues
    const year = dateObj.getUTCFullYear();
    const month = String(dateObj.getUTCMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getUTCDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  /**
   * Get the month (1-12)
   * @param {string} dateStr - Date in YYYY-MM-DD format
   * @returns {number} Month (1-12)
   */
  static getMonth(dateStr) {
    return this.parseDate(dateStr).getMonth() + 1;
  }

  /**
   * Get the year
   * @param {string} dateStr - Date in YYYY-MM-DD format
   * @returns {number} Year
   */
  static getYear(dateStr) {
    return this.parseDate(dateStr).getFullYear();
  }

  /**
   * Add years to a business date
   * @param {string} dateStr - Date in YYYY-MM-DD format
   * @param {number} years - Number of years to add (can be negative)
   * @returns {string} New date in YYYY-MM-DD format
   */
  static addYears(dateStr, years) {
    const date = this.parseDate(dateStr);
    date.setFullYear(date.getFullYear() + years);
    return this.formatDate(date);
  }

  /**
   * Calculate difference in days between two business dates
   * @param {string} date1 - First date in YYYY-MM-DD format
   * @param {string} date2 - Second date in YYYY-MM-DD format
   * @returns {number} Number of days (positive if date1 is after date2)
   */
  static diffInDays(date1, date2) {
    const d1 = this.parseDate(date1);
    const d2 = this.parseDate(date2);
    const diffTime = d1.getTime() - d2.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  /**
   * Get the day of week name for a business date
   * @param {string} dateStr - Date in YYYY-MM-DD format
   * @returns {string} Day name (e.g., 'Monday', 'Tuesday')
   */
  static getDayOfWeekName(dateStr) {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    return days[this.getDayOfWeek(dateStr)];
  }
  /**
   * Calculate period end date based on pay frequency and DOA
   * @param {string} doaStr - Employee DOA in YYYY-MM-DD format
   * @param {object} payFrequency - Pay frequency configuration
   * @returns {string} Period end date in YYYY-MM-DD format
   */
  static calculatePeriodEndDate(doaStr, payFrequency) {
    const startDate = this.normalize(doaStr);

    this.debug('calculatePeriodEndDate', {
      doa: doaStr,
      startDate,
      frequency: payFrequency.frequency,
      lastDayOfPeriod: payFrequency.lastDayOfPeriod
    });

    if (payFrequency.frequency === 'weekly') {
      // Map day names to numbers (0 = Sunday, 6 = Saturday)
      const daysOfWeek = {
        sunday: 0, monday: 1, tuesday: 2, wednesday: 3,
        thursday: 4, friday: 5, saturday: 6
      };

      const targetDay = daysOfWeek[payFrequency.lastDayOfPeriod.toLowerCase()];
      const currentDay = this.getDayOfWeek(startDate);

      // Calculate days to add to reach the next target day
      let daysToAdd = (targetDay - currentDay + 7) % 7;

      // If we're already on the target day, use today as the end date
      if (daysToAdd === 0) {
        return startDate;
      } else {
        return this.addDays(startDate, daysToAdd);
      }
    } else if (payFrequency.frequency === 'monthly') {
      if (payFrequency.lastDayOfPeriod === 'monthend') {
        return this.endOfMonth(startDate);
      } else {
        // Use the custom day of month
        const day = parseInt(payFrequency.lastDayOfPeriod);
        let endDate = this.setDayOfMonth(startDate, day);

        // If the start date is after the target day, move to next month
        if (this.getDayOfMonth(startDate) > day) {
          endDate = this.addMonths(endDate, 1);
        }

        return endDate;
      }
    } else if (payFrequency.frequency === 'biweekly' || payFrequency.frequency === 'bi-weekly') {
      return this.addDays(startDate, 13);
    } else {
      // Default to month end
      return this.endOfMonth(startDate);
    }
  }

  /**
   * Calculate next period end date based on current period end date and pay frequency
   * @param {string} currentPeriodEndDate - Current period end date in YYYY-MM-DD format
   * @param {object} payFrequency - Pay frequency configuration
   * @returns {string} Next period end date in YYYY-MM-DD format
   */
  static calculateNextPeriodEndDate(currentPeriodEndDate, payFrequency) {
    const nextStartDate = this.addDays(currentPeriodEndDate, 1);

    this.debug('calculateNextPeriodEndDate', {
      currentPeriodEndDate,
      nextStartDate,
      frequency: payFrequency.frequency,
      lastDayOfPeriod: payFrequency.lastDayOfPeriod
    });

    if (payFrequency.frequency === 'weekly') {
      // Map day names to numbers (0 = Sunday, 6 = Saturday)
      const daysOfWeek = {
        sunday: 0, monday: 1, tuesday: 2, wednesday: 3,
        thursday: 4, friday: 5, saturday: 6
      };

      const targetDay = daysOfWeek[payFrequency.lastDayOfPeriod.toLowerCase()];
      const currentDay = this.getDayOfWeek(nextStartDate);

      // Calculate days to add to reach the next target day
      let daysToAdd = (targetDay - currentDay + 7) % 7;

      // If we're already on the target day, use today as the end date
      if (daysToAdd === 0) {
        return nextStartDate;
      } else {
        return this.addDays(nextStartDate, daysToAdd);
      }
    } else if (payFrequency.frequency === 'monthly') {
      if (payFrequency.lastDayOfPeriod === 'monthend') {
        return this.endOfMonth(nextStartDate);
      } else {
        // Use the custom day of month
        const day = parseInt(payFrequency.lastDayOfPeriod);
        let endDate = this.setDayOfMonth(nextStartDate, day);

        // If the start date is after the target day, move to next month
        if (this.getDayOfMonth(nextStartDate) > day) {
          endDate = this.addMonths(endDate, 1);
        }

        return endDate;
      }
    } else if (payFrequency.frequency === 'biweekly' || payFrequency.frequency === 'bi-weekly') {
      // For bi-weekly with custom dates, handle alternating pattern
      if (payFrequency.lastDayOfPeriod !== 'monthend' && payFrequency.biWeeklyInterimDay) {
        const lastDay = parseInt(payFrequency.lastDayOfPeriod);
        const interimDay = parseInt(payFrequency.biWeeklyInterimDay);
        const currentPeriodEndDay = this.getDayOfMonth(currentPeriodEndDate);

        // If current period ended on the last day (e.g., 7th)
        if (currentPeriodEndDay === lastDay) {
          // Next period should end on interim day (e.g., 1st)
          let endDate = this.setDayOfMonth(nextStartDate, interimDay);
          if (this.getDayOfMonth(nextStartDate) > interimDay) {
            endDate = this.addMonths(endDate, 1);
          }
          return endDate;
        }
        // If current period ended on interim day (e.g., 1st)
        else if (currentPeriodEndDay === interimDay) {
          // Next period should end on last day (e.g., 7th)
          let endDate = this.setDayOfMonth(nextStartDate, lastDay);
          if (this.getDayOfMonth(nextStartDate) > lastDay) {
            endDate = this.addMonths(endDate, 1);
          }
          return endDate;
        }
      }

      // Default bi-weekly: add 14 days
      return this.addDays(nextStartDate, 13);
    } else {
      // Default to month end
      return this.endOfMonth(nextStartDate);
    }
  }

  /**
   * Calculate prorated salary using BusinessDate for timezone independence
   * @param {string} doaStr - Employee DOA in YYYY-MM-DD format
   * @param {number} basicSalary - Basic salary amount
   * @param {object} employee - Employee object with payFrequency
   * @param {string} periodStartStr - Period start date in YYYY-MM-DD format
   * @param {string} periodEndStr - Period end date in YYYY-MM-DD format
   * @returns {object} Prorated salary calculation result
   */
  static calculateProratedSalary(doaStr, basicSalary, employee, periodStartStr, periodEndStr) {

    const startDate = this.normalize(doaStr);
    const periodStartDate = this.normalize(periodStartStr);
    const periodEndDate = this.normalize(periodEndStr);


    // Determine if this is the first period that includes DOA
    const isFirstPeriodWithDOA = this.isSameOrAfter(startDate, periodStartDate) &&
                                 this.isSameOrBefore(startDate, periodEndDate);

    // Determine if this is the first period that includes DOA
    // DOA must be within the period (between start and end dates)

    // Calculate total days in period and worked days
    let totalDaysInPeriod = this.diffInDays(periodEndDate, periodStartDate) + 1;
    let workedDays = totalDaysInPeriod;

    if (isFirstPeriodWithDOA) {
      // Employee started mid-period, calculate from DOA to period end
      workedDays = this.diffInDays(periodEndDate, startDate) + 1;
    }

    // Adjust for future employees (DOA after period end)
    if (this.isAfter(startDate, periodEndDate)) {
      workedDays = 0;
    }

    // Calculate prorated percentage
    const proratedPercentage = totalDaysInPeriod > 0 ? (workedDays / totalDaysInPeriod) * 100 : 0;

    // Calculate period salary based on frequency
    let periodSalary = basicSalary; // Already in correct frequency

    // Calculate prorated salary
    const proratedSalary = (periodSalary * proratedPercentage) / 100;

    // Calculate annual salary
    const frequencyMultiplier = employee.payFrequency?.frequency === 'monthly' ? 12 :
                               employee.payFrequency?.frequency === 'weekly' ? 52 : 26;
    const annualSalary = basicSalary * frequencyMultiplier;


    return {
      proratedPercentage: proratedPercentage.toFixed(2),
      proratedSalary: proratedSalary.toFixed(2),
      fullPeriodSalary: periodSalary.toFixed(2),
      workedDays,
      totalDaysInPeriod,
      frequency: employee.payFrequency?.frequency || 'monthly',
      annualSalary,
      isFirstPeriodWithDOA,
    };
  }

  /**
   * Debug helper to log date operations
   * @param {string} operation - Description of operation
   * @param {object} data - Data to log
   */
  static debug(operation, data) {
  }
}

module.exports = BusinessDate;
