/**
 * ENHANCED PAYSLIP GENERATOR
 * Example implementation using the new PDF Layout Engine
 * Demonstrates automated, scalable payslip generation
 */

const { PDFDocument, rgb, StandardFonts } = require("pdf-lib");
const fs = require("fs").promises;
const path = require("path");
const { PDFLayoutEngine } = require('./pdfLayoutEngine');
const { PayslipDataProcessor } = require('./payslipLayoutConfig');
const { formatCurrency, formatDate, formatPeriod } = require('./formatters');

class EnhancedPayslipGenerator {
  constructor() {
    this.pdfDoc = null;
    this.page = null;
    this.layoutEngine = null;
    this.fonts = {};
  }

  /**
   * INITIALIZE PDF DOCUMENT
   */
  async initialize() {
    this.pdfDoc = await PDFDocument.create();
    this.page = this.pdfDoc.addPage([595, 842]); // A4 size
    
    // Load fonts
    this.fonts.regular = await this.pdfDoc.embedFont(StandardFonts.Helvetica);
    this.fonts.bold = await this.pdfDoc.embedFont(StandardFonts.HelveticaBold);
    
    // Initialize layout engine
    this.layoutEngine = new PDFLayoutEngine(this.page, {
      width: 595,
      height: 842,
      margins: { top: 50, right: 50, bottom: 50, left: 50 },
      defaultFont: this.fonts.regular,
      defaultFontSize: 9
    });
  }

  /**
   * GENERATE PAYSLIP WITH AUTOMATED LAYOUT
   * No manual positioning required!
   */
  async generatePayslip(payrollData) {
    await this.initialize();
    
    // Process data into layout-ready format
    const processor = new PayslipDataProcessor(payrollData);
    const layoutData = processor.process();
    
    // Render each section automatically
    await this.renderHeader(layoutData.header);
    this.renderEmployeeDetails(layoutData.employeeDetails);
    this.renderMainTables(layoutData.tables);
    this.renderFooter(layoutData.footer);
    
    return this.pdfDoc;
  }

  /**
   * RENDER HEADER SECTION
   * Automatically handles logo, company info, and draft status
   */
  async renderHeader(headerData) {
    let currentY = this.layoutEngine.currentY;

    // Draft status (if applicable)
    if (headerData.isDraft) {
      this.layoutEngine.drawText(
        'This is a draft payslip - Not finalized',
        this.layoutEngine.workingWidth / 2,
        currentY,
        {
          font: this.fonts.bold,
          size: 10,
          color: rgb(0.9, 0.3, 0.3),
          align: 'center'
        }
      );
      currentY = this.layoutEngine.addVerticalSpace(25);
    }

    // Company logo (if exists)
    if (headerData.company.logo) {
      try {
        await this.renderLogo(headerData.company.logo, currentY);
        currentY = this.layoutEngine.addVerticalSpace(80);
      } catch (error) {
        console.error('Logo rendering error:', error);
        currentY = this.layoutEngine.addVerticalSpace(20);
      }
    }

    // Company name (centered)
    this.layoutEngine.drawText(
      headerData.company.name,
      this.layoutEngine.workingWidth / 2,
      currentY,
      {
        font: this.fonts.bold,
        size: 14,
        align: 'center'
      }
    );
    currentY = this.layoutEngine.addVerticalSpace(20);

    // Company address (right-aligned)
    if (headerData.company.address) {
      this.layoutEngine.drawText(
        headerData.company.address,
        this.layoutEngine.workingWidth - 10,
        currentY,
        {
          size: 9,
          align: 'right'
        }
      );
    }

    this.layoutEngine.currentY = currentY - 30;
  }

  /**
   * RENDER EMPLOYEE DETAILS
   * Automatically spaced and aligned
   */
  renderEmployeeDetails(employeeData) {
    const section = this.layoutEngine.createSection('EMPLOYEE DETAILS', {
      fontSize: 10,
      underline: true,
      spacing: { top: 20, bottom: 15 }
    });

    // Create simple two-column table for employee details
    const detailsTable = this.layoutEngine.createTable({
      columns: [
        { weight: 1, align: 'left' },
        { weight: 2, align: 'left' }
      ],
      rowHeight: 18
    });

    // Add employee detail rows
    detailsTable
      .addRow(['Period:', employeeData.period])
      .addRow(['Employee Name:', employeeData.employeeName])
      .addRow(['Pay Frequency:', employeeData.payFrequency])
      .addRow(['Employment Date:', employeeData.employmentDate]);

    this.layoutEngine.currentY = detailsTable.render(this.layoutEngine.currentY);
    this.layoutEngine.addVerticalSpace(20);
  }

  /**
   * RENDER MAIN TABLES
   * Automatically handles complex multi-column layouts
   */
  renderMainTables(tablesData) {
    // Income & Employer Contributions Table
    const incomeEmployerTable = this.layoutEngine.createTable({
      columns: [
        { weight: 2, align: 'left' },
        { weight: 1, align: 'right' },
        { weight: 0.3 }, // Spacer
        { weight: 2, align: 'left' },
        { weight: 1, align: 'right' }
      ],
      rowHeight: 20,
      headerHeight: 25
    });

    incomeEmployerTable.addHeader(tablesData.incomeEmployer.header);
    tablesData.incomeEmployer.rows.forEach(row => {
      incomeEmployerTable.addRow(row);
    });

    this.layoutEngine.currentY = incomeEmployerTable.render(this.layoutEngine.currentY);
    this.layoutEngine.addVerticalSpace(25);

    // Deductions & Tax Credits Table
    const deductionsTaxTable = this.layoutEngine.createTable({
      columns: [
        { weight: 2, align: 'left' },
        { weight: 1, align: 'right' },
        { weight: 0.3 }, // Spacer
        { weight: 2, align: 'left' },
        { weight: 1, align: 'right' }
      ],
      rowHeight: 20,
      headerHeight: 25
    });

    deductionsTaxTable.addHeader(tablesData.deductionsTax.header);
    tablesData.deductionsTax.rows.forEach(row => {
      deductionsTaxTable.addRow(row);
    });

    this.layoutEngine.currentY = deductionsTaxTable.render(this.layoutEngine.currentY);
    this.layoutEngine.addVerticalSpace(30);

    // Net Pay Table (prominent display)
    const netPayTable = this.layoutEngine.createTable({
      columns: [
        { weight: 3, align: 'left' },
        { weight: 1, align: 'right' }
      ],
      rowHeight: 35
    });

    netPayTable.addRow(tablesData.netPay.rows[0], {
      style: {
        font: this.fonts.bold,
        size: 14
      }
    });

    this.layoutEngine.currentY = netPayTable.render(this.layoutEngine.currentY);
  }

  /**
   * RENDER FOOTER
   */
  renderFooter(footerData) {
    // Position footer at bottom of page
    const footerY = this.layoutEngine.margins.bottom + 30;
    
    this.layoutEngine.drawText(
      footerData.statusText,
      this.layoutEngine.workingWidth / 2,
      footerY,
      {
        size: 8,
        align: 'center',
        color: rgb(0.5, 0.5, 0.5)
      }
    );
  }

  /**
   * RENDER LOGO WITH AUTOMATIC SIZING
   */
  async renderLogo(logoPath, y) {
    const logoFile = await fs.readFile(path.join(__dirname, '..', logoPath));
    const logoImage = await this.pdfDoc.embedPng(logoFile);
    
    // Automatic sizing with max constraints
    const maxWidth = 100;
    const maxHeight = 60;
    const aspectRatio = logoImage.width / logoImage.height;
    
    let width = maxWidth;
    let height = width / aspectRatio;
    
    if (height > maxHeight) {
      height = maxHeight;
      width = height * aspectRatio;
    }

    this.page.drawImage(logoImage, {
      x: this.layoutEngine.margins.left,
      y: y - height - 20,
      width: width,
      height: height,
    });
  }
}

/**
 * USAGE EXAMPLE
 * Shows how easy it is to generate payslips with the new system
 */
async function generatePayslipExample(payrollData) {
  const generator = new EnhancedPayslipGenerator();
  const pdfDoc = await generator.generatePayslip(payrollData);
  
  // Save or return PDF
  const pdfBytes = await pdfDoc.save();
  return pdfBytes;
}

module.exports = {
  EnhancedPayslipGenerator,
  generatePayslipExample
};
