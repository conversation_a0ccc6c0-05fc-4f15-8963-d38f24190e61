/**
 * FORMATTING UTILITIES
 * Consistent formatting functions for payslip data
 */

/**
 * Format currency values consistently
 */
const formatCurrency = (amount) => {
  const safeAmount = Number(amount) || 0;
  return new Intl.NumberFormat("en-ZA", {
    style: "currency",
    currency: "ZAR",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(safeAmount);
};

/**
 * Format dates consistently
 */
const formatDate = (date) => {
  if (!date) return 'N/A';
  return new Date(date).toLocaleDateString('en-ZA');
};

/**
 * Format period display
 */
const formatPeriod = (startDate, endDate) => {
  if (!startDate || !endDate) return 'N/A';
  return `${formatDate(startDate)} - ${formatDate(endDate)}`;
};

module.exports = {
  formatCurrency,
  formatDate,
  formatPeriod
};
