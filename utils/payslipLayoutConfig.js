/**
 * PAYSLIP LAYOUT CONFIGURATION
 * Defines the structure and styling for automated payslip generation
 * Easily configurable and scalable for different payslip formats
 */

const { formatCurrency } = require('./formatters');

/**
 * PAYSLIP LAYOUT TEMPLATES
 * Different templates for various payslip types
 */
const PAYSLIP_LAYOUTS = {
  standard: {
    // Header section configuration
    header: {
      logo: {
        maxWidth: 100,
        maxHeight: 60,
        position: 'top-left',
        padding: { top: 20, bottom: 10 }
      },
      company: {
        name: { fontSize: 14, fontWeight: 'bold', align: 'center' },
        address: { fontSize: 9, align: 'right', spacing: 15 }
      },
      draft: {
        text: 'This is a draft payslip - Not finalized',
        fontSize: 10,
        color: { r: 0.9, g: 0.3, b: 0.3 },
        position: 'top-center'
      }
    },

    // Employee details section
    employeeDetails: {
      spacing: { top: 30, bottom: 20 },
      fields: [
        { label: 'Period', key: 'period', fontSize: 10, fontWeight: 'bold' },
        { label: 'Employee Name', key: 'employeeName', fontSize: 9 },
        { label: 'Pay Frequency', key: 'payFrequency', fontSize: 9 },
        { label: 'Employment Date', key: 'employmentDate', fontSize: 9 }
      ],
      lineSpacing: 18
    },

    // Main content tables configuration
    tables: {
      // Income and Employer Contributions (side by side)
      incomeEmployer: {
        columns: [
          { weight: 2, align: 'left' },   // Income labels
          { weight: 1, align: 'right' },  // Income amounts
          { weight: 0.5 },                // Spacer
          { weight: 2, align: 'left' },   // Employer labels
          { weight: 1, align: 'right' }   // Employer amounts
        ],
        rowHeight: 22,
        headerHeight: 25
      },

      // Deductions and Tax Credits (side by side)
      deductionsTax: {
        columns: [
          { weight: 2, align: 'left' },   // Deduction labels
          { weight: 1, align: 'right' },  // Deduction amounts
          { weight: 0.5 },                // Spacer
          { weight: 2, align: 'left' },   // Tax credit labels
          { weight: 1, align: 'right' }   // Tax credit amounts
        ],
        rowHeight: 22,
        headerHeight: 25
      },

      // Net Pay (full width)
      netPay: {
        columns: [
          { weight: 3, align: 'left' },
          { weight: 1, align: 'right' }
        ],
        rowHeight: 30,
        fontSize: 14,
        fontWeight: 'bold'
      }
    },

    // Spacing between sections
    sectionSpacing: 25,

    // Footer configuration
    footer: {
      spacing: { top: 30 },
      statusText: {
        fontSize: 8,
        align: 'center',
        color: { r: 0.5, g: 0.5, b: 0.5 }
      }
    }
  }
};

/**
 * DYNAMIC FIELD CONFIGURATION
 * Easily add/remove fields without manual positioning
 */
const FIELD_DEFINITIONS = {
  income: [
    { key: 'basicSalary', label: 'Basic Salary', required: true },
    { key: 'overtime', label: 'Overtime', conditional: true },
    { key: 'allowances', label: 'Allowances', conditional: true, expandable: true },
    { key: 'totalIncome', label: 'Total Income', summary: true, fontWeight: 'bold' }
  ],

  deductions: [
    { key: 'medicalAid', label: 'Medical Aid', conditional: true },
    { key: 'uif', label: 'UIF', required: true },
    { key: 'paye', label: 'PAYE', required: true },
    { key: 'customDeductions', label: 'Other Deductions', conditional: true, expandable: true },
    { key: 'totalDeductions', label: 'Total Deductions', summary: true, fontWeight: 'bold' }
  ],

  employerContributions: [
    { key: 'sdl', label: 'SDL', required: true },
    { key: 'uifEmployer', label: 'UIF - Employer', required: true },
    { key: 'customContributions', label: 'Other Contributions', conditional: true, expandable: true }
  ],

  taxCredits: [
    { key: 'medicalAidTaxCredit', label: 'Medical Aid Tax Credit', conditional: true },
    { key: 'customCredits', label: 'Other Credits', conditional: true, expandable: true }
  ]
};

/**
 * PAYSLIP DATA PROCESSOR
 * Converts payroll data into layout-ready format
 */
class PayslipDataProcessor {
  constructor(payrollData, layoutConfig = 'standard') {
    this.data = payrollData;
    this.layout = PAYSLIP_LAYOUTS[layoutConfig];
    this.processedData = {};
  }

  /**
   * PROCESS ALL PAYSLIP DATA
   * Converts raw payroll data into structured layout data
   */
  process() {
    this.processedData = {
      header: this.processHeader(),
      employeeDetails: this.processEmployeeDetails(),
      tables: this.processTables(),
      footer: this.processFooter()
    };

    return this.processedData;
  }

  processHeader() {
    return {
      company: {
        name: this.data.company?.name || 'Company Name',
        address: this.formatAddress(this.data.company?.address),
        logo: this.data.company?.logo
      },
      isDraft: !this.data.isFinalized
    };
  }

  processEmployeeDetails() {
    return {
      period: this.data.period || 'N/A',
      employeeName: `${this.data.employee?.firstName || ''} ${this.data.employee?.lastName || ''}`.trim(),
      payFrequency: this.data.employee?.payFrequency || 'Monthly',
      employmentDate: this.data.employee?.employmentDate || 'N/A'
    };
  }

  processTables() {
    return {
      incomeEmployer: this.buildIncomeEmployerTable(),
      deductionsTax: this.buildDeductionsTaxTable(),
      netPay: this.buildNetPayTable()
    };
  }

  buildIncomeEmployerTable() {
    const incomeRows = this.buildSectionRows('income');
    const employerRows = this.buildSectionRows('employerContributions');
    
    // Combine rows side by side
    const maxRows = Math.max(incomeRows.length, employerRows.length);
    const combinedRows = [];

    for (let i = 0; i < maxRows; i++) {
      const incomeRow = incomeRows[i] || ['', ''];
      const employerRow = employerRows[i] || ['', ''];
      
      combinedRows.push([
        incomeRow[0],     // Income label
        incomeRow[1],     // Income amount
        '',               // Spacer
        employerRow[0],   // Employer label
        employerRow[1]    // Employer amount
      ]);
    }

    return {
      header: ['INCOME', '', '', 'EMPLOYER CONTRIBUTIONS', ''],
      rows: combinedRows
    };
  }

  buildDeductionsTaxTable() {
    const deductionRows = this.buildSectionRows('deductions');
    const taxRows = this.buildSectionRows('taxCredits');
    
    const maxRows = Math.max(deductionRows.length, taxRows.length);
    const combinedRows = [];

    for (let i = 0; i < maxRows; i++) {
      const deductionRow = deductionRows[i] || ['', ''];
      const taxRow = taxRows[i] || ['', ''];
      
      combinedRows.push([
        deductionRow[0],  // Deduction label
        deductionRow[1],  // Deduction amount
        '',               // Spacer
        taxRow[0],        // Tax credit label
        taxRow[1]         // Tax credit amount
      ]);
    }

    return {
      header: ['DEDUCTIONS', '', '', 'TAX CREDITS', ''],
      rows: combinedRows
    };
  }

  buildNetPayTable() {
    return {
      rows: [['NET PAY', formatCurrency(this.data.netPay || 0)]]
    };
  }

  buildSectionRows(sectionKey) {
    const fields = FIELD_DEFINITIONS[sectionKey];
    const rows = [];

    fields.forEach(field => {
      const value = this.getFieldValue(field);
      
      if (value !== null && value !== undefined && value !== 0) {
        rows.push([
          field.label,
          typeof value === 'number' ? formatCurrency(value) : value.toString()
        ]);
      }
    });

    return rows;
  }

  getFieldValue(field) {
    // Navigate through nested data structure
    const keys = field.key.split('.');
    let value = this.data;
    
    for (const key of keys) {
      value = value?.[key];
    }

    return value;
  }

  formatAddress(address) {
    if (!address) return '';
    return `${address.city || ''} ${address.postalCode || ''}`.trim();
  }

  processFooter() {
    return {
      statusText: this.data.isFinalized ? 
        'This payslip has been finalized' : 
        'This is a draft payslip - Not finalized'
    };
  }
}

module.exports = {
  PAYSLIP_LAYOUTS,
  FIELD_DEFINITIONS,
  PayslipDataProcessor
};
