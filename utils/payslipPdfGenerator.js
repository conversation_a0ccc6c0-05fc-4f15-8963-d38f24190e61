const { PDFDocument, rgb, StandardFonts } = require("pdf-lib");
const fs = require("fs").promises;
const path = require("path");
const moment = require("moment-timezone");
const PayrollService = require("../services/PayrollService");
const { execFile } = require("child_process");
const os = require("os");

const DEFAULT_TIMEZONE = "Africa/Johannesburg";

// Helper function to format currency safely
const formatCurrency = (amount) => {
  const safeAmount = Number(amount) || 0;
  return new Intl.NumberFormat("en-ZA", {
    style: "currency",
    currency: "ZAR",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(safeAmount);
};

function runQpdfEncrypt(inputPath, outputPath, password) {
  return new Promise((resolve, reject) => {
    execFile(
      "qpdf",
      [
        "--encrypt",
        password,
        password,
        "256",
        "--",
        inputPath,
        outputPath,
      ],
      (error, stdout, stderr) => {
        if (error) {
          reject(
            new Error(
              `qpdf encryption failed: ${stderr || error.message}`
            )
          );
        } else {
          resolve();
        }
      }
    );
  });
}

/**
 * Generates a payslip PDF using pdf-lib based on the web route design (routes/payslip.js).
 * If pdfPassword is provided, uses qpdf to password-protect the PDF.
 * @param {object} employee - The employee document (expected to be populated with company and payFrequency).
 * @param {object} payrollPeriod - The payroll period document.
 * @param {object} calculations - The payroll calculations object.
 * @param {string} [pdfPassword] - Optional password to protect the PDF (employee ID or passport number).
 * @returns {Promise<Buffer>} - A promise that resolves with the PDF buffer.
 */
async function generatePayslipPdfLib(employee, payrollPeriod, calculations, pdfPassword) {
  try {
    // Create PDF document
    const pdfDoc = await PDFDocument.create();
    const page = pdfDoc.addPage([595.28, 841.89]); // A4 size
    const { width, height } = page.getSize();

    // Load fonts
    const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const helveticaBoldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

    // EXACT MATCH: Helper functions identical to standard payslip (routes/payslip.js)
    const drawText = (text, x, y, options = {}) => {
      const {
        font = helveticaFont,
        size = 8,
        color = rgb(0, 0, 0),
        align = "left",
      } = options;

      const textWidth = font.widthOfTextAtSize(text.toString(), size);
      const textX =
        align === "center"
          ? x - textWidth / 2
          : align === "right"
          ? x - textWidth
          : x;
      page.drawText(text.toString(), { x: textX, y, font, size, color });
    };

    // EXACT MATCH: Table layout identical to standard payslip
    const tableLayout = {
      startY: height - 200,
      lineHeight: 22,
      col1: 50,   // Left labels
      col2: 250,  // Left amounts
      col3: 320,  // Right labels
      col4: 500   // Right amounts
    };

    // EXACT MATCH: Advanced drawTextWithSpace with dynamic column positioning
    const drawTextWithSpace = (label, amount, x, y, options = {}) => {
      drawText(label, x, y, options);

      // DYNAMIC COLUMN POSITIONING: Use correct amount column based on label position
      let amountColumnX;
      if (x <= tableLayout.col2) {
        // Left side labels (Income, Deductions) use left amount column
        amountColumnX = tableLayout.col2; // 250px - prevents overlap
      } else {
        // Right side labels (Employer Contributions, Tax Credits) use right amount column
        amountColumnX = tableLayout.col4; // 500px - clear separation
      }

      drawText(formatCurrency(amount), amountColumnX, y, {
        ...options,
        align: "right",
      });
    };

    // Destructure all needed values - Fixed data structure mapping
    const {
      totalIncome = calculations?.totalIncome || calculations?.grossIncome || 0,
      basicSalary = calculations?.basicSalary || 0,
      deductions: {
        statutory: { paye = 0, uif = 0 } = {},
        courtOrders: { maintenanceOrder = 0 } = {},
        total: totalDeductions = 0,
      } = {},
      netPay = calculations?.netPay || 0,
    } = calculations || {};

    // Extract basic salary value properly (handle object structure)
    const basicSalaryValue = typeof basicSalary === 'object'
      ? (basicSalary.full || basicSalary.prorated || 0)
      : (basicSalary || 0);

    // Ensure totalIncome is never undefined - add fallback
    const safeTotalIncome = totalIncome || basicSalaryValue || 0;

    // Calculate SDL (1% of total income) with safe value
    const sdl = safeTotalIncome * 0.01;

    // Calculate medical aid credit
    let medicalAidCredit = 0;
    if (employee?.payFrequency?.frequency) {
      const medicalAidImpact = await PayrollService.calculateMedicalAidTaxImpact(
        payrollPeriod?.medical || {},
        employee.payFrequency.frequency,
        totalIncome
      );
      medicalAidCredit = medicalAidImpact?.taxCredit || 0;
    }

    // EXACT MATCH: Format dates using BusinessDate fields to prevent timezone issues
    // ISSUE RESOLVED: Period end date now shows correct last day of month
    const formattedStartDate = payrollPeriod?.startDateBusiness ?
      moment(payrollPeriod.startDateBusiness).format("DD/MM/YYYY") :
      moment(payrollPeriod?.startDate || new Date()).format("DD/MM/YYYY");

    const formattedEndDate = payrollPeriod?.endDateBusiness ?
      moment(payrollPeriod.endDateBusiness).format("DD/MM/YYYY") :
      moment(payrollPeriod?.endDate || new Date()).format("DD/MM/YYYY");

    const periodDisplay = `${formattedStartDate} - ${formattedEndDate}`;

    // Add DRAFT watermark if period is not finalized
    if (!payrollPeriod?.isFinalized) {
      const draftText = "DRAFT";
      const fontSize = 60;
      const draftWidth = helveticaBoldFont.widthOfTextAtSize(draftText, fontSize);
      page.drawText(draftText, {
        x: (width - draftWidth) / 2,
        y: height / 2,
        font: helveticaBoldFont,
        size: fontSize,
        color: rgb(0.9, 0.3, 0.3),
        opacity: 0.3,
        rotate: {
          type: "degrees",
          angle: 45,
          origin: {
            x: width / 2,
            y: height / 2,
          },
        },
      });
      const noticeText = "This is a draft payslip - Not finalized";
      page.drawText(noticeText, {
        x: 50,
        y: height - 30,
        font: helveticaBoldFont,
        size: 10,
        color: rgb(0.9, 0.3, 0.3),
      });
    }

    // Add logo if it exists
    let companyLogo = employee?.company?.employerDetails?.logo;
    if (companyLogo) {
      try {
        const logoPath = path.join(__dirname, "..", companyLogo);
        const logoFile = await fs.readFile(logoPath);
        const logoImage = await pdfDoc.embedPng(logoFile);
        const logoWidth = 100;
        const logoHeight = (logoWidth / logoImage.width) * logoImage.height;
        page.drawImage(logoImage, {
          x: 50,
          y: height - 20 - logoHeight,
          width: logoWidth,
          height: logoHeight,
        });
      } catch (error) {
        console.error("Error loading logo:", error);
      }
    }

    // Company name (centered, 8pt)
    const companyName = employee?.company?.employerDetails?.tradingName || employee?.company?.name || "Company Name";
    drawText(companyName, width / 2, height - 50, { font: helveticaBoldFont, size: 8, align: "center" });

    // Company address (right-aligned)
    const addressY = height - 100;
    const cityTown = employee?.company?.employerDetails?.physicalAddress?.cityTown || "City";
    const postalCode = employee?.company?.employerDetails?.physicalAddress?.code || "Postal Code";
    drawText(cityTown, width - 50, addressY, { align: "right" });
    drawText(postalCode, width - 50, addressY - 15, { align: "right" });

    // Update the period display based on frequency
    drawText(`Period: ${periodDisplay}`, 50, height - 145, {
      font: helveticaBoldFont,
      size: 8,
    });

    // Update employee details section with period-specific information
    const startY = height - 100;
    drawText(`Employee Name: ${employee?.firstName || ""} ${employee?.lastName || ""}`.trim(), 50, startY);
    drawText(`Pay Frequency: ${employee?.payFrequency?.frequency ? employee.payFrequency.frequency.charAt(0).toUpperCase() + employee.payFrequency.frequency.slice(1) : "Not specified"}`, 50, startY - 15);
    const employmentDate = employee?.doa ? moment(employee.doa).format("DD/MM/YYYY") : "Not specified";
    drawText(`Employment Date: ${employmentDate}`, 50, startY - 30);

    // Horizontal line under employee details
    const lineY = startY - 50;
    page.drawLine({
      start: { x: 50, y: lineY },
      end: { x: width - 50, y: lineY },
      thickness: 1,
      color: rgb(0, 0, 0),
    });

    // EXACT MATCH: Table layout identical to standard payslip (routes/payslip.js)
    const sections = {
      income: {
        startY: tableLayout.startY,
        title: "INCOME",
        items: 2  // Basic Salary + Total Income
      },
      deductions: {
        startY: tableLayout.startY - (tableLayout.lineHeight * 4),
        title: "DEDUCTIONS",
        items: 4  // Medical Aid + UIF + PAYE + Total
      },
      employer: {
        startY: tableLayout.startY,
        title: "EMPLOYER CONTRIBUTIONS",
        items: 2  // SDL + UIF Employer
      },
      taxCredit: {
        startY: tableLayout.startY - (tableLayout.lineHeight * 3),
        title: "TAX CREDITS",
        items: 1  // Medical Aid Tax Credit
      }
    };

    // SECTION HEADERS with enhanced styling and clear separation
    // Income section header
    drawText(sections.income.title, tableLayout.col1, sections.income.startY, {
      font: helveticaBoldFont,
      size: 10,
      color: rgb(0.2, 0.2, 0.2),
    });

    // Draw underline for Income section
    page.drawLine({
      start: { x: tableLayout.col1, y: sections.income.startY - 5 },
      end: { x: tableLayout.col2 + 30, y: sections.income.startY - 5 },
      thickness: 1,
      color: rgb(0.3, 0.3, 0.3),
    });

    // Employer Contribution section header
    drawText(sections.employer.title, tableLayout.col3, sections.employer.startY, {
      font: helveticaBoldFont,
      size: 10,
      color: rgb(0.2, 0.2, 0.2),
    });

    // Draw underline for Employer section
    page.drawLine({
      start: { x: tableLayout.col3, y: sections.employer.startY - 5 },
      end: { x: tableLayout.col4 + 30, y: sections.employer.startY - 5 },
      thickness: 1,
      color: rgb(0.3, 0.3, 0.3),
    });

    // INCOME SECTION with enhanced spacing and organization
    const incomeY = sections.income.startY - tableLayout.lineHeight;

    // Basic Salary (clean single line)
    drawTextWithSpace(
      "Basic Salary",
      safeTotalIncome,
      tableLayout.col1,
      incomeY,
      { size: 9 }
    );

    // Total Income with visual emphasis
    drawTextWithSpace(
      "Total Income",
      safeTotalIncome,
      tableLayout.col1,
      incomeY - tableLayout.lineHeight,
      { font: helveticaBoldFont, size: 10 }
    );

    // DEDUCTIONS SECTION with proper spacing and organization
    const deductionsY = sections.deductions.startY;

    // Deductions section header
    drawText(sections.deductions.title, tableLayout.col1, deductionsY, {
      font: helveticaBoldFont,
      size: 10,
      color: rgb(0.2, 0.2, 0.2),
    });

    // Draw underline for Deductions section
    page.drawLine({
      start: { x: tableLayout.col1, y: deductionsY - 5 },
      end: { x: tableLayout.col2 + 30, y: deductionsY - 5 },
      thickness: 1,
      color: rgb(0.3, 0.3, 0.3),
    });

    // Individual deduction items with consistent spacing
    let currentDeductionY = deductionsY - tableLayout.lineHeight;

    // Medical Aid
    drawTextWithSpace(
      "Medical Aid",
      payrollPeriod?.medical?.medicalAid || 0,
      tableLayout.col1,
      currentDeductionY,
      { size: 9 }
    );
    currentDeductionY -= tableLayout.lineHeight;

    // UIF
    drawTextWithSpace("UIF", uif, tableLayout.col1, currentDeductionY, { size: 9 });
    currentDeductionY -= tableLayout.lineHeight;

    // PAYE
    drawTextWithSpace("PAYE", paye, tableLayout.col1, currentDeductionY, { size: 9 });
    currentDeductionY -= tableLayout.lineHeight;

    // Total Deductions with visual emphasis
    drawTextWithSpace(
      "Total Deductions",
      totalDeductions,
      tableLayout.col1,
      currentDeductionY,
      { font: helveticaBoldFont, size: 10 }
    );

    // EMPLOYER CONTRIBUTIONS SECTION with proper spacing
    const employerY = sections.employer.startY - tableLayout.lineHeight;

    // SDL
    drawTextWithSpace("SDL", sdl, tableLayout.col3, employerY, { size: 9 });

    // UIF - Employer
    drawTextWithSpace(
      "UIF - Employer",
      uif,
      tableLayout.col3,
      employerY - tableLayout.lineHeight,
      { size: 9 }
    );

    // TAX CREDITS SECTION with proper spacing
    const taxCreditY = sections.taxCredit.startY;

    // Tax Credit section header
    drawText(sections.taxCredit.title, tableLayout.col3, taxCreditY, {
      font: helveticaBoldFont,
      size: 10,
      color: rgb(0.2, 0.2, 0.2),
    });

    // Draw underline for Tax Credits section
    page.drawLine({
      start: { x: tableLayout.col3, y: taxCreditY - 5 },
      end: { x: tableLayout.col4 + 30, y: taxCreditY - 5 },
      thickness: 1,
      color: rgb(0.3, 0.3, 0.3),
    });

    // Medical Aid Tax Credit
    drawTextWithSpace(
      "Medical Aid Tax Credit",
      medicalAidCredit,
      tableLayout.col3,
      taxCreditY - tableLayout.lineHeight,
      { size: 9 }
    );

    // EXACT MATCH: Net Pay positioning identical to standard payslip
    const netPayY = 80;

    // Top line above Net Pay
    page.drawLine({
      start: { x: 50, y: netPayY + 25 },
      end: { x: width - 50, y: netPayY + 25 },
      thickness: 1,
      color: rgb(0, 0, 0),
    });

    // FIXED: Net Pay with enhanced styling and right-column amount positioning
    // ISSUE RESOLVED: Net Pay amount now aligns with right column (same as SDL, UIF-Employer)
    drawText("NET PAY", 50, netPayY, {
      font: helveticaBoldFont,
      size: 14,  // Larger font for prominence
    });

    // Position Net Pay amount in right column for visual consistency
    drawText(formatCurrency(netPay), tableLayout.col4, netPayY, {
      font: helveticaBoldFont,
      size: 14,
      align: "right",
    });

    // Bottom line below Net Pay
    page.drawLine({
      start: { x: 50, y: netPayY - 10 },
      end: { x: width - 50, y: netPayY - 10 },
      thickness: 1,
      color: rgb(0, 0, 0),
    });

    // Add finalization status to the footer
    const statusText = payrollPeriod?.isFinalized ? "FINALIZED PAYSLIP" : "DRAFT PAYSLIP - NOT FOR OFFICIAL USE";
    drawText(statusText, width / 2, 30, {
      font: helveticaBoldFont,
      size: 8,
      color: payrollPeriod?.isFinalized ? rgb(0, 0, 0) : rgb(0.9, 0.3, 0.3),
      align: "center",
    });

    // Serialize the PDF
    let pdfBytes = await pdfDoc.save();

    // If no password, return the buffer as before
    if (!pdfPassword) {
      return Buffer.from(pdfBytes);
    }

    // If password is set, use qpdf to encrypt the PDF
    // Write the unprotected PDF to a temp file
    const tmpDir = os.tmpdir();
    const inputPath = path.join(tmpDir, `payslip_unprotected_${Date.now()}_${Math.random().toString(36).slice(2)}.pdf`);
    const outputPath = path.join(tmpDir, `payslip_protected_${Date.now()}_${Math.random().toString(36).slice(2)}.pdf`);
    await fs.writeFile(inputPath, pdfBytes);

    try {
      await runQpdfEncrypt(inputPath, outputPath, pdfPassword);
      const protectedBuffer = await fs.readFile(outputPath);
      // Clean up temp files
      await fs.unlink(inputPath).catch(() => {});
      await fs.unlink(outputPath).catch(() => {});
      return protectedBuffer;
    } catch (err) {
      // Clean up temp files on error
      await fs.unlink(inputPath).catch(() => {});
      await fs.unlink(outputPath).catch(() => {});
      throw err;
    }
  } catch (error) {
    console.error("Error generating payslip PDF via utility:", error);
    throw error;
  }
}

module.exports = generatePayslipPdfLib; 