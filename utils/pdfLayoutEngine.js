/**
 * ADVANCED PDF LAYOUT ENGINE
 * Automated table-based layout system for payslip generation
 * Eliminates manual positioning and provides scalable, maintainable layouts
 */

class PDFLayoutEngine {
  constructor(page, options = {}) {
    this.page = page;
    this.width = options.width || 595;
    this.height = options.height || 842;
    this.margins = options.margins || { top: 50, right: 50, bottom: 50, left: 50 };
    this.defaultFont = options.defaultFont;
    this.defaultFontSize = options.defaultFontSize || 9;
    
    // Calculate working area
    this.workingWidth = this.width - this.margins.left - this.margins.right;
    this.workingHeight = this.height - this.margins.top - this.margins.bottom;
    this.currentY = this.height - this.margins.top;
  }

  /**
   * AUTOMATED TABLE SYSTEM
   * Creates perfectly aligned tables with automatic spacing
   */
  createTable(config) {
    const table = new PDFTable(this, config);
    return table;
  }

  /**
   * SECTION MANAGEMENT
   * Automatically manages vertical spacing between sections
   */
  createSection(title, options = {}) {
    const section = new PDFSection(this, title, options);
    this.currentY = section.render(this.currentY);
    return section;
  }

  /**
   * AUTOMATIC POSITIONING
   * Eliminates manual coordinate calculations
   */
  drawText(text, x, y, options = {}) {
    const finalOptions = {
      font: options.font || this.defaultFont,
      size: options.size || this.defaultFontSize,
      ...options
    };

    this.page.drawText(text.toString(), {
      x: x + this.margins.left,
      y: y,
      ...finalOptions
    });
  }

  /**
   * RESPONSIVE COLUMN SYSTEM
   * Automatically calculates column widths and positions
   */
  calculateColumns(columnConfig) {
    const totalWeight = columnConfig.reduce((sum, col) => sum + (col.weight || 1), 0);
    let currentX = 0;
    
    return columnConfig.map(col => {
      const width = (this.workingWidth * (col.weight || 1)) / totalWeight;
      const column = {
        x: currentX,
        width: width,
        align: col.align || 'left',
        ...col
      };
      currentX += width;
      return column;
    });
  }

  /**
   * AUTOMATIC SPACING MANAGEMENT
   */
  addVerticalSpace(space) {
    this.currentY -= space;
    return this.currentY;
  }

  /**
   * BOUNDARY CHECKING
   * Prevents content overflow
   */
  checkBounds(requiredHeight) {
    return this.currentY - requiredHeight >= this.margins.bottom;
  }
}

/**
 * ADVANCED TABLE CLASS
 * Handles complex table layouts with automatic alignment
 */
class PDFTable {
  constructor(layoutEngine, config) {
    this.engine = layoutEngine;
    this.config = {
      columns: config.columns || [],
      rowHeight: config.rowHeight || 20,
      headerHeight: config.headerHeight || 25,
      borderWidth: config.borderWidth || 1,
      alternateRowColors: config.alternateRowColors || false,
      ...config
    };
    
    this.columns = this.engine.calculateColumns(this.config.columns);
    this.rows = [];
  }

  /**
   * ADD TABLE HEADER
   */
  addHeader(headerData) {
    this.header = headerData;
    return this;
  }

  /**
   * ADD TABLE ROWS
   * Supports dynamic content with automatic formatting
   */
  addRow(rowData, options = {}) {
    this.rows.push({
      data: rowData,
      height: options.height || this.config.rowHeight,
      style: options.style || {},
      ...options
    });
    return this;
  }

  /**
   * RENDER TABLE
   * Automatically positions and aligns all content
   */
  render(startY) {
    let currentY = startY;

    // Render header if exists
    if (this.header) {
      currentY = this.renderHeader(currentY);
    }

    // Render rows
    this.rows.forEach((row, index) => {
      currentY = this.renderRow(row, currentY, index);
    });

    return currentY;
  }

  renderHeader(y) {
    const headerY = y - this.config.headerHeight;
    
    this.columns.forEach((col, index) => {
      if (this.header[index]) {
        const textX = this.getTextPosition(col, this.header[index]);
        this.engine.drawText(this.header[index], textX, headerY + 5, {
          font: this.engine.defaultFont,
          size: this.config.headerFontSize || 10
        });
      }
    });

    return headerY;
  }

  renderRow(row, y, rowIndex) {
    const rowY = y - row.height;
    
    this.columns.forEach((col, colIndex) => {
      if (row.data[colIndex] !== undefined) {
        const textX = this.getTextPosition(col, row.data[colIndex]);
        this.engine.drawText(row.data[colIndex], textX, rowY + 5, {
          ...row.style,
          size: row.style.size || this.engine.defaultFontSize
        });
      }
    });

    return rowY;
  }

  /**
   * INTELLIGENT TEXT POSITIONING
   * Automatically aligns text based on column configuration
   */
  getTextPosition(column, text) {
    switch (column.align) {
      case 'center':
        return column.x + (column.width / 2);
      case 'right':
        return column.x + column.width - 10; // 10px padding from right
      default: // left
        return column.x + 10; // 10px padding from left
    }
  }
}

/**
 * SECTION MANAGEMENT CLASS
 * Handles section headers and spacing
 */
class PDFSection {
  constructor(layoutEngine, title, options = {}) {
    this.engine = layoutEngine;
    this.title = title;
    this.options = {
      fontSize: 12,
      fontWeight: 'bold',
      underline: true,
      spacing: { top: 20, bottom: 15 },
      ...options
    };
  }

  render(currentY) {
    // Add top spacing
    currentY -= this.options.spacing.top;

    // Render title
    this.engine.drawText(this.title, 0, currentY, {
      font: this.engine.defaultFont,
      size: this.options.fontSize
    });

    // Add underline if requested
    if (this.options.underline) {
      this.engine.page.drawLine({
        start: { x: this.engine.margins.left, y: currentY - 5 },
        end: { x: this.engine.width - this.engine.margins.right, y: currentY - 5 },
        thickness: 1,
        color: { r: 0.3, g: 0.3, b: 0.3 }
      });
    }

    // Add bottom spacing
    currentY -= this.options.spacing.bottom;

    return currentY;
  }
}

module.exports = { PDFLayoutEngine, PDFTable, PDFSection };
