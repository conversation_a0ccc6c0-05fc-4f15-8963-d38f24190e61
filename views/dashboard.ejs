<!-- dashboard.ejs -->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="PandaPayroll Dashboard - Manage your payroll efficiently with comprehensive analytics and insights" />
    <title>Dashboard | <%= company.name %></title>

    <!-- Critical CSS - Inline for Fastest Loading -->
    <style>
      /* Critical above-the-fold styles inlined with CLS prevention */
      /* Critical CSS - Expanded for better FCP */
      :root{--primary-color:#3b82f6;--secondary-color:#2563eb;--background-color:#f9fafb;--surface-color:#ffffff;--text-primary:#111827;--text-secondary:#4b5563;--border-color:#e5e7eb;--success-color:#10b981;--warning-color:#f59e0b;--space-4:1rem;--space-6:1.5rem;--radius-lg:0.75rem}
      *,*::before,*::after{box-sizing:border-box}
      body{margin:0;padding:0;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,sans-serif;background:var(--background-color);color:var(--text-primary);line-height:1.6}
      .layout-wrapper{display:flex;min-height:100vh;background:var(--background-color)}
      .content-wrapper{flex:1;display:flex;flex-direction:column;margin-left:280px}
      .main-container{flex:1;padding:var(--space-6);max-width:100%;overflow-x:hidden;contain:layout style}
      .greeting-card{background:var(--surface-color);border-radius:var(--radius-lg);padding:var(--space-6);margin-bottom:var(--space-6);border:1px solid var(--border-color);display:flex;align-items:center;justify-content:space-between;contain:layout style;min-height:80px;transform:translateZ(0)}
      .greeting-text h1{font-size:24px;font-weight:600;color:var(--text-primary);margin:0 0 8px 0;display:flex;align-items:center;gap:12px;line-height:1.2}
      .greeting-text p{color:var(--text-secondary);margin:0;font-size:14px;line-height:1.4}
      .key-metrics{display:grid;grid-template-columns:repeat(auto-fit,minmax(280px,1fr));gap:var(--space-6);margin-bottom:var(--space-6);contain:layout}
      .metric-card{background:var(--surface-color);border-radius:var(--radius-lg);padding:var(--space-6);border:1px solid var(--border-color);display:flex;align-items:center;gap:var(--space-4);contain:layout style;min-height:96px;transform:translateZ(0)}
      .card-icon{width:48px;height:48px;border-radius:var(--radius-lg);display:flex;align-items:center;justify-content:center;color:white;font-size:20px;flex-shrink:0}
      .card-icon.employees{background:linear-gradient(135deg,#3b82f6 0%,#2563eb 100%)}
      .card-icon.terminations{background:linear-gradient(135deg,#f59e0b 0%,#d97706 100%)}
      .card-icon.payroll{background:linear-gradient(135deg,#10b981 0%,#059669 100%)}
      .card-content h3{font-size:24px;font-weight:700;color:var(--text-primary);margin:0 0 4px 0;line-height:1.2}
      .card-content p{color:var(--text-secondary);margin:0;font-size:14px;font-weight:500;line-height:1.4}
      .three-card-row{display:flex;gap:var(--space-6);margin-bottom:var(--space-6);contain:layout}
      .chart-card-container{flex:1;min-width:0}
      .charts-card{background:var(--surface-color);border-radius:var(--radius-lg);padding:var(--space-6);border:1px solid var(--border-color);contain:layout style;min-height:400px;transform:translateZ(0)}
      .chart-header{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:var(--space-4)}
      .chart-title-section h3{font-size:18px;font-weight:600;color:var(--text-primary);margin:0;display:flex;align-items:center;gap:8px}
      .notifications-card-container{flex:0 0 280px}
      .notification-card{background:var(--surface-color);border-radius:var(--radius-lg);padding:var(--space-6);border:1px solid var(--border-color);height:100%;contain:layout style}
      .notification-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:var(--space-4)}
      .notification-header h3{font-size:18px;font-weight:600;color:var(--text-primary);margin:0;display:flex;align-items:center;gap:8px}
      .calendar-card-container{flex:0 0 320px}
      .calendar-card-reference{background:var(--surface-color);border-radius:16px;border:1px solid var(--border-color);overflow:hidden;contain:layout style;min-height:400px;transform:translateZ(0)}
      .schedule-compliance-footer{padding:16px 20px 20px;border-top:1px solid var(--border-color);background:var(--surface-color);border-radius:0 0 16px 16px}
      .schedule-compliance-btn{width:100%;font-size:14px !important;padding:10px 16px !important;height:auto !important;min-height:40px !important;justify-content:center !important}
      .calendar-art-section{width:100%;height:120px;overflow:hidden;border-radius:16px 16px 0 0;position:relative}
      .calendar-art-image-reference{width:100%;height:100%;object-fit:cover;border-radius:16px 16px 0 0;opacity:0;transition:opacity 0.3s ease}
      .wave{will-change:transform;transform:translateZ(0)}
      @keyframes loading{0%{background-position:200% 0}100%{background-position:-200% 0}}
      .chart-skeleton{display:none}
      /* Sidebar critical styles */
      .sidebar{position:fixed;left:0;top:0;width:280px;height:100vh;background:var(--surface-color);border-right:1px solid var(--border-color);z-index:1000;transform:translateZ(0)}
      .header{position:sticky;top:0;background:var(--surface-color);border-bottom:1px solid var(--border-color);padding:var(--space-4) var(--space-6);z-index:100;transform:translateZ(0)}
      @media (max-width:768px){.content-wrapper{margin-left:0}.main-container{padding:var(--space-4)}.greeting-card{flex-direction:column;align-items:flex-start;gap:var(--space-4);min-height:120px}.greeting-text h1{font-size:1.25rem}.key-metrics{grid-template-columns:1fr;gap:var(--space-4)}.metric-card{min-height:80px}.three-card-row{flex-direction:column}.chart-card-container,.notifications-card-container,.calendar-card-container{flex:none}}
    </style>

    <!-- Preload Critical Resources for Speed Index -->
    <link rel="preload" href="/css/header.css" as="style" />
    <link rel="preload" href="/css/sidebar.css" as="style" />
    <!-- Preload LCP image with high priority - PNG only (WebP fallback removed) -->
    <link rel="preload" href="/images/calendarArt.png?v=1" as="image" fetchpriority="high" />
    <!-- Preload critical scripts for faster execution -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/apexcharts/3.35.3/apexcharts.min.js" as="script" />

    <!-- Eliminate render-blocking CSS - Load all non-critical -->
    <link rel="stylesheet" href="/css/header.css" media="print" onload="this.media='all'" />
    <link rel="stylesheet" href="/css/sidebar.css" media="print" onload="this.media='all'" />
    <link rel="stylesheet" href="/css/dashboard.css" media="print" onload="this.media='all'" />
    <link rel="stylesheet" href="/css/styles.css" media="print" onload="this.media='all'" />
    <link rel="stylesheet" href="/css/onboarding.css" media="print" onload="this.media='all'" />
    <link rel="stylesheet" href="/css/mobile-nav.css" media="print" onload="this.media='all'" />
    <link rel="stylesheet" href="/css/toast.css" media="print" onload="this.media='all'" />
    <link rel="stylesheet" href="/css/event-modals.css" media="print" onload="this.media='all'" />

    <!-- Fallback for browsers without JS -->
    <noscript>
      <link rel="stylesheet" href="/css/header.css" />
      <link rel="stylesheet" href="/css/sidebar.css" />
      <link rel="stylesheet" href="/css/dashboard.css" />
    </noscript>

    <!-- Resource Hints for Performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" />
    <link rel="dns-prefetch" href="https://cdn.jsdelivr.net" />
    <link rel="dns-prefetch" href="https://unpkg.com" />

    <!-- Optimized Font Loading -->
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap"
      rel="stylesheet"
      media="print"
      onload="this.media='all'"
    />

    <!-- Font fallback to prevent layout shift -->
    <style>
      body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; }
      .font-loaded body { font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; }
    </style>

    <!-- Defer Non-Critical Scripts with low priority -->
    <script defer src="https://unpkg.com/@phosphor-icons/web@2.0.3/dist/index.umd.js" fetchpriority="low"></script>
    <script defer src="/js/notifications.js" fetchpriority="low"></script>

    <!-- Optimized Script Loading - Remove unused Chart.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/apexcharts/3.35.3/apexcharts.min.js" defer fetchpriority="high"></script>

    <script>
      // Ensure libraries are loaded before use
      window.chartLibrariesReady = false;
      window.loadChartLibraries = function() {
        return new Promise((resolve) => {
          if (window.chartLibrariesReady) {
            resolve();
            return;
          }

          const checkLibraries = () => {
            if (typeof ApexCharts !== 'undefined') {
              window.chartLibrariesReady = true;
              resolve();
            } else {
              setTimeout(checkLibraries, 100);
            }
          };
          checkLibraries();
        });
      };

      // Lazy load FullCalendar only when needed
      window.loadCalendarLibrary = function() {
        return new Promise((resolve) => {
          if (typeof FullCalendar !== 'undefined') {
            resolve();
            return;
          }

          const script = document.createElement('script');
          script.src = 'https://cdn.jsdelivr.net/npm/fullcalendar@6.1.10/index.global.min.js';
          script.onload = () => resolve();
          script.onerror = () => resolve(); // Graceful fallback
          document.head.appendChild(script);
        });
      };
    </script>
  </head>
  <body>
    <div class="layout-wrapper">
      <%- include('partials/sidebar', { user: user, company: company }) %>
      <div class="content-wrapper">
        <%- include('partials/header', { user: user }) %>

        <main class="main-container">
          <!-- Greeting Card -->
          <div class="greeting-card">
            <div class="greeting-content">
              <div class="greeting-text">
                <h1>Hi, <%= user.firstName %> <span class="wave">👋</span></h1>
                <p>Manage your payroll with Panda</p>
              </div>
            </div>
            <div class="greeting-decoration">
              <!-- Decorative illustration will be handled by CSS -->
            </div>
          </div>

          <!-- Three Card Row: Chart (50%) + Notifications (20%) + Calendar (30%) -->
          <div class="three-card-row">
            <!-- Headcount Chart (50%) -->
            <div class="chart-card-container">
              <div class="charts-card">
                <div class="chart-header">
                  <div class="chart-title-section">
                    <h3><i class="ph ph-chart-line"></i> Headcount Trends</h3>
                    <div class="chart-legend">
                      <span class="legend-item">
                        <i class="ph ph-circle"></i> Monthly Headcount
                      </span>
                    </div>
                  </div>

                  <!-- Integrated Time Period Filters -->
                  <div class="chart-filters">
                    <form action="" method="GET" class="chart-filter-form">
                      <div class="filter-group-inline">
                        <label for="month-chart">
                          <i class="ph ph-calendar-day"></i>
                        </label>
                        <select name="month" id="month-chart" class="filter-select-compact">
                          <% for (let i = 1; i <= 12; i++) { %>
                            <option value="<%= i %>" <%= i === filterMonth ? 'selected' : '' %>>
                              <%= moment().month(i-1).format('MMM') %>
                            </option>
                          <% } %>
                        </select>
                      </div>
                      <div class="filter-group-inline">
                        <label for="year-chart">
                          <i class="ph ph-calendar-blank"></i>
                        </label>
                        <select name="year" id="year-chart" class="filter-select-compact">
                          <%
                            const currentYear = moment().year();
                            const startYear = currentYear - 5;
                            const endYear = moment().quarter() >= 4 ? currentYear + 1 : currentYear;

                            for (let i = endYear; i >= startYear; i--) {
                          %>
                            <option value="<%= i %>" <%= i === filterYear ? 'selected' : '' %>>
                              <%= i %>
                            </option>
                          <% } %>
                        </select>
                      </div>
                      <button type="submit" class="filter-button-compact">
                        <i class="ph ph-arrows-clockwise"></i>
                      </button>
                    </form>
                  </div>
                </div>
                <div id="bar-chart" style="min-height: 350px; position: relative;">
                  <!-- Skeleton loader for chart -->
                  <div class="chart-skeleton" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: loading 1.5s infinite; border-radius: 8px; opacity: 0.6;"></div>
                </div>
              </div>
            </div>

            <!-- Notifications Card (20%) -->
            <div class="notifications-card-container">
              <div class="notification-card">
                <div class="notification-header">
                  <h3><i class="ph ph-bell"></i> Updates</h3>
                  <a href="/notifications" class="view-all-link">
                    <i class="ph ph-arrow-right"></i>
                  </a>
                </div>
                <div class="notification-body" style="min-height: 120px; contain: layout;">
                  <% if (latestNotification) { %>
                  <div class="notification-item">
                    <div class="notification-meta">
                      <span class="notification-date">
                        <i class="ph ph-clock"></i>
                        <%= moment(latestNotification.createdAt).fromNow() %>
                      </span>
                    </div>
                    <h4 class="notification-title" style="line-height: 1.3; margin: 8px 0;">
                      <%= latestNotification.title %>
                    </h4>
                    <div class="notification-content" style="line-height: 1.4;">
                      <%- latestNotification.content %>
                    </div>
                  </div>
                  <% } else { %>
                  <div class="no-notifications" style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 120px;">
                    <i class="ph ph-inbox" style="font-size: 24px; margin-bottom: 8px; opacity: 0.5;"></i>
                    <p style="margin: 0; color: #6b7280; font-size: 14px;">No new notifications</p>
                  </div>
                  <% } %>
                </div>
              </div>
            </div>

            <!-- Payroll Calendar (30%) - Reference Image Design -->
            <div class="calendar-card-container">
              <div class="calendar-card-reference">
                <!-- Calendar Art Image - Optimized -->
                <div class="calendar-art-section">
                  <img
                    src="/images/calendarArt.png?v=1"
                    alt="Calendar Art"
                    class="calendar-art-image-reference"
                    loading="eager"
                    decoding="sync"
                    fetchpriority="high"
                    width="320"
                    height="120"
                    style="aspect-ratio: 320/120; display: block; transform: translateZ(0);"
                    onload="this.style.opacity='1'; console.log('Calendar art loaded successfully');"
                    onerror="console.warn('Calendar art image failed to load:', this.src); this.style.background='linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%)'; this.style.display='flex'; this.style.alignItems='center'; this.style.justifyContent='center'; this.innerHTML='<span style=color:#6b7280;font-size:14px;>📅</span>';"
                  >
                </div>

                <!-- Calendar Header with Navigation -->
                <div class="calendar-header-reference">
                  <button class="nav-btn-reference prev-btn" onclick="changeCalendarMonth(-1)">
                    <i class="ph ph-caret-left"></i>
                  </button>
                  <h3 class="calendar-month-title-reference" id="calendarMonthTitleCompact"></h3>
                  <button class="nav-btn-reference next-btn" onclick="changeCalendarMonth(1)">
                    <i class="ph ph-caret-right"></i>
                  </button>
                </div>

                <!-- Calendar Grid -->
                <div class="calendar-grid-reference">
                  <!-- Weekday Headers -->
                  <div class="calendar-weekdays-reference">
                    <div class="weekday-reference">Sun</div>
                    <div class="weekday-reference">Mon</div>
                    <div class="weekday-reference">Tue</div>
                    <div class="weekday-reference">Wed</div>
                    <div class="weekday-reference">Thu</div>
                    <div class="weekday-reference">Fri</div>
                    <div class="weekday-reference">Sat</div>
                  </div>

                  <!-- Calendar Days -->
                  <div class="calendar-days-reference" id="calendarDaysCompact">
                    <!-- Days will be generated by JavaScript -->
                  </div>
                </div>

                <!-- Upcoming Schedule Section -->
                <div class="upcoming-schedule-reference">
                  <h4 class="schedule-title-reference">Upcoming Schedule</h4>
                  <p class="schedule-date-reference">Wednesday, 15 November 2023</p>

                  <div class="schedule-list-reference">
                    <div class="schedule-item-reference">
                      <div class="schedule-icon-reference payroll-icon-reference">
                        <i class="ph ph-money"></i>
                      </div>
                      <div class="schedule-details-reference">
                        <span class="schedule-title-text-reference">Payroll December</span>
                        <span class="schedule-time-reference">10:00 AM</span>
                      </div>
                      <button class="schedule-action-btn-reference">
                        <i class="ph ph-caret-right"></i>
                      </button>
                    </div>

                    <div class="schedule-item-reference">
                      <div class="schedule-icon-reference interview-icon-reference">
                        <i class="ph ph-user-plus"></i>
                      </div>
                      <div class="schedule-details-reference">
                        <span class="schedule-title-text-reference">Interview Illustrator</span>
                        <span class="schedule-time-reference">11:00 AM</span>
                      </div>
                      <button class="schedule-action-btn-reference">
                        <i class="ph ph-caret-right"></i>
                      </button>
                    </div>

                    <div class="schedule-item-reference">
                      <div class="schedule-icon-reference meeting-icon-reference">
                        <i class="ph ph-users"></i>
                      </div>
                      <div class="schedule-details-reference">
                        <span class="schedule-title-text-reference">Internal Meeting</span>
                        <span class="schedule-time-reference">04:00 PM</span>
                      </div>
                      <button class="schedule-action-btn-reference">
                        <i class="ph ph-caret-right"></i>
                      </button>
                    </div>
                  </div>

                  <!-- Setup Compliance Button - Positioned after schedule for better UX -->
                  <div class="schedule-compliance-footer">
                    <button id="initializeComplianceBtn" class="create-reports-btn schedule-compliance-btn">
                      <i class="ph ph-gear"></i>
                      Setup Compliance
                    </button>
                  </div>
                </div>

                <!-- Hidden Quick Actions for Functionality -->
                <div class="calendar-actions-hidden" style="display: none;">
                  <button id="addEventBtnCompact" class="btn-compact btn-primary-compact">
                    <i class="ph ph-plus"></i>
                    Add Event
                  </button>
                  <button id="viewFullCalendarBtn" class="btn-compact btn-secondary-compact">
                    <i class="ph ph-calendar"></i>
                    Full View
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Company Overview & Management Section - Two Column Layout -->
          <div class="two-column-section">
            <!-- Company Overview Column -->
            <div class="overview-column">
              <div class="chart-header">
                <div class="chart-title-section">
                  <h3><i class="ph ph-chart-line-up"></i> Company Overview</h3>
                  <div class="chart-legend">
                    <span class="legend-item">
                      <i class="ph ph-calendar"></i> <%= moment().format('MMMM YYYY') %>
                    </span>
                  </div>
                </div>
              </div>

              <div class="compact-cards-stack">
                <!-- Employee Growth Card -->
                <div class="compact-card">
                  <div class="compact-card-header">
                    <div class="compact-icon growth">
                      <i class="ph ph-users"></i>
                    </div>
                    <div class="compact-info">
                      <h4>Employee Growth</h4>
                      <span class="compact-subtitle">Monthly change</span>
                    </div>
                  </div>
                  <div class="compact-value">
                    <%= ((joiners - terminations) / headcount * 100).toFixed(1) %>%
                  </div>
                </div>

                <!-- Retention Rate Card -->
                <div class="compact-card">
                  <div class="compact-card-header">
                    <div class="compact-icon retention">
                      <i class="ph ph-chart-line-up"></i>
                    </div>
                    <div class="compact-info">
                      <h4>Retention Rate</h4>
                      <span class="compact-subtitle">Current period</span>
                    </div>
                  </div>
                  <div class="compact-value">
                    <%= ((headcount - terminations) / headcount * 100).toFixed(1) %>%
                  </div>
                </div>

                <!-- Cost Centers Card -->
                <div class="compact-card">
                  <div class="compact-card-header">
                    <div class="compact-icon distribution">
                      <i class="ph ph-chart-pie"></i>
                    </div>
                    <div class="compact-info">
                      <h4>Cost Centers</h4>
                      <span class="compact-subtitle">Active divisions</span>
                    </div>
                  </div>
                  <div class="compact-value">
                    <%= costCenterDistribution.length %>
                  </div>
                </div>
              </div>
            </div>

            <!-- Company Management Column -->
            <div class="management-column">
              <div class="chart-header">
                <div class="chart-title-section">
                  <h3><i class="ph ph-buildings"></i> Company Management</h3>
                  <div class="chart-legend">
                    <span class="legend-item">
                      <i class="ph ph-users"></i> Manage organizations
                    </span>
                  </div>
                </div>
              </div>

              <div class="compact-cards-stack">
                <% if (companies && companies.length > 0) { %>
                  <% companies.forEach(function(company) { %>
                    <div class="compact-card company-card">
                      <div class="compact-card-header">
                        <div class="compact-icon company">
                          <i class="ph ph-buildings"></i>
                        </div>
                        <div class="compact-info">
                          <h4><%= company.name %></h4>
                          <span class="compact-subtitle">
                            <i class="ph ph-users"></i> <%= company.employeeCount %> employees
                          </span>
                        </div>
                      </div>
                      <div class="compact-actions">
                        <span class="company-status <%= company._id.toString() === (currentCompany?._id?.toString() || '') ? 'active' : 'inactive' %>">
                          <%= company._id.toString() === (currentCompany?._id?.toString() || '') ? 'Current' : 'Inactive' %>
                        </span>
                        <a href="/companies" class="compact-action-btn">
                          <i class="ph ph-gear"></i>
                        </a>
                      </div>
                    </div>
                  <% }); %>
                <% } else { %>
                  <div class="compact-card empty-state-card">
                    <div class="compact-card-header">
                      <div class="compact-icon empty">
                        <i class="ph ph-buildings"></i>
                      </div>
                      <div class="compact-info">
                        <h4>No Companies</h4>
                        <span class="compact-subtitle">Get started by adding your first company</span>
                      </div>
                    </div>
                    <div class="compact-actions">
                      <a href="/admin/rfi-wizard" class="compact-action-btn primary">
                        <i class="ph ph-plus"></i>
                      </a>
                    </div>
                  </div>
                <% } %>
              </div>
            </div>
          </div>

          <!-- Key Metrics Section -->
          <div class="dashboard-section">
            <div class="chart-header">
              <div class="chart-title-section">
                <h3><i class="ph ph-chart-bar"></i> Key Metrics</h3>
                <div class="chart-legend">
                  <span class="legend-item">
                    <i class="ph ph-calendar"></i> Current month overview
                  </span>
                </div>
              </div>
            </div>

            <div class="dashboard-cards-grid">
              <!-- Total Headcount -->
              <div class="dashboard-card modern">
                <div class="card-header">
                  <div class="card-icon headcount">
                    <i class="ph ph-users-three"></i>
                  </div>
                  <div class="card-title">
                    <h3>Total Headcount</h3>
                    <span class="card-subtitle">Active employees</span>
                  </div>
                </div>
                <div class="card-content">
                  <div class="card-value"><%= headcount %></div>
                  <div class="card-trend <%= (joiners > terminations) ? 'positive' : 'negative' %>">
                    <i class="ph <%= (joiners > terminations) ? 'ph-trend-up' : 'ph-trend-down' %>"></i>
                    <%= Math.abs(joiners - terminations) %> net change
                  </div>
                </div>
              </div>

              <!-- New Joiners -->
              <div class="dashboard-card modern">
                <div class="card-header">
                  <div class="card-icon joiners">
                    <i class="ph ph-user-plus"></i>
                  </div>
                  <div class="card-title">
                    <h3>New Joiners</h3>
                    <span class="card-subtitle">This month</span>
                  </div>
                </div>
                <div class="card-content">
                  <div class="card-value"><%= joiners %></div>
                  <div class="card-trend positive">
                    <i class="ph ph-calendar"></i>
                    This month
                  </div>
                </div>
              </div>

              <!-- Terminations -->
              <div class="dashboard-card modern">
                <div class="card-header">
                  <div class="card-icon terminations">
                    <i class="ph ph-user-minus"></i>
                  </div>
                  <div class="card-title">
                    <h3>Terminations</h3>
                    <span class="card-subtitle">This month</span>
                  </div>
                </div>
                <div class="card-content">
                  <div class="card-value"><%= terminations %></div>
                  <div class="card-trend negative">
                    <i class="ph ph-calendar"></i>
                    This month
                  </div>
                </div>
              </div>
            </div>
          </div>






        </main>
      </div>
      <%- include('partials/mobile-bottom-nav') %>
    </div>

    <!-- Event Management Modals -->
    <!-- Add Event Modal -->
    <div id="addEventModal" class="modal" style="display: none;">
      <div class="modal-content">
        <div class="modal-header">
          <h2><i class="ph ph-plus"></i> Add New Event</h2>
          <button class="modal-close" onclick="closeAddEventModal()">
            <i class="ph ph-x"></i>
          </button>
        </div>
        <div class="modal-body">
          <form id="addEventForm">
            <div class="form-group">
              <label for="eventTitle">
                <i class="ph ph-text-aa"></i> Event Title
              </label>
              <input type="text" id="eventTitle" name="title" required
                     placeholder="Enter event title" class="form-control">
            </div>

            <div class="form-group">
              <label for="eventDate">
                <i class="ph ph-calendar"></i> Date
              </label>
              <input type="date" id="eventDate" name="date" required class="form-control">
            </div>

            <div class="form-group">
              <label for="eventType">
                <i class="ph ph-tag"></i> Event Type
              </label>
              <select id="eventType" name="type" class="form-control">
                <option value="custom">Custom Event</option>
                <option value="cutoff">Payroll Cut-off</option>
                <option value="processing">Processing Deadline</option>
                <option value="payment">Payment Date</option>
                <option value="emp201">EMP201 Monthly Submission</option>
                <option value="emp501">EMP501 Reconciliation</option>
                <option value="irp5">IRP5/IT3(a) Submission</option>
                <option value="uif">UIF Submission</option>
                <option value="sdl">SDL Submission</option>
                <option value="eti">Employment Tax Incentive</option>
                <option value="paye">PAYE Related</option>
                <option value="compliance">General Compliance</option>
                <option value="reminder">Reminder</option>
              </select>
            </div>

            <div class="form-group">
              <label for="eventDescription">
                <i class="ph ph-note"></i> Description (Optional)
              </label>
              <textarea id="eventDescription" name="description"
                        placeholder="Enter event description" class="form-control" rows="3"></textarea>
            </div>

            <div class="form-group">
              <label for="eventPriority">
                <i class="ph ph-flag"></i> Priority
              </label>
              <select id="eventPriority" name="priority" class="form-control">
                <option value="low">Low</option>
                <option value="medium" selected>Medium</option>
                <option value="high">High</option>
              </select>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" onclick="closeAddEventModal()">
            <i class="ph ph-x"></i> Cancel
          </button>
          <button type="button" class="btn btn-primary" onclick="submitAddEvent()">
            <i class="ph ph-check"></i> Add Event
          </button>
        </div>
      </div>
    </div>

    <!-- Event Details Modal -->
    <div id="eventDetailsModal" class="modal" style="display: none;">
      <div class="modal-content">
        <div class="modal-header">
          <h2><i class="ph ph-calendar-check"></i> Event Details</h2>
          <button class="modal-close" onclick="closeEventDetailsModal()">
            <i class="ph ph-x"></i>
          </button>
        </div>
        <div class="modal-body">
          <div id="eventDetailsContent">
            <!-- Event details will be populated here -->
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" onclick="closeEventDetailsModal()">
            <i class="ph ph-x"></i> Close
          </button>
          <button type="button" class="btn btn-primary" onclick="editCurrentEvent()" style="display: none;">
            <i class="ph ph-pencil"></i> Edit Event
          </button>
        </div>
      </div>
    </div>

    <!-- Keep your existing chart scripts -->
    <script>
      // Chart Configuration with safety checks
      var headcountData = <%- JSON.stringify(headcountPerMonth || []) %>;
      var chartData = headcountData.length > 0 ? headcountData.map(item => item.count || 0) : [0];

      var barChartOptions = {
          series: [{
            name: 'Headcount',
            data: chartData
          }],
          chart: {
            type: 'bar',
            height: 350,
            toolbar: {
              show: false
            },
            animations: {
              enabled: false
            }
          },
          colors: ['#fbbf24'], // Lighter orange matching the reference design
          fill: {
            type: 'gradient',
            gradient: {
              shade: 'light',
              type: 'vertical',
              shadeIntensity: 0.3,
              gradientToColors: ['#c4b5fd'], // Lighter purple gradient end
              inverseColors: false,
              opacityFrom: 0.7,
              opacityTo: 0.2,
              stops: [0, 100],
              colorStops: [
                {
                  offset: 0,
                  color: '#fbbf24',
                  opacity: 0.7
                },
                {
                  offset: 30,
                  color: '#fcd34d',
                  opacity: 0.6
                },
                {
                  offset: 70,
                  color: '#ddd6fe',
                  opacity: 0.4
                },
                {
                  offset: 100,
                  color: '#c4b5fd',
                  opacity: 0.2
                }
              ]
            }
          },
          plotOptions: {
            bar: {
              borderRadius: 32, // Very rounded tops for modern look
              borderRadiusApplication: 'end',
              columnWidth: '60%',
              distributed: false
            }
          },
          stroke: {
            show: false
          },
          dataLabels: {
            enabled: false
          },
          xaxis: {
            categories: <%- JSON.stringify(chartCategories || []) %>,
            labels: {
              style: {
                colors: '#6b7280', // Reference design text color
                fontSize: '12px',
                fontWeight: 500
              }
            }
          },
          yaxis: {
            title: {
              text: 'Headcount',
              style: {
                color: '#64748b'
              }
            },
            labels: {
              formatter: function(value) {
                return Math.round(value);
              }
            }
          },
          grid: {
            show: true,
            borderColor: 'rgba(139, 92, 246, 0.1)',
            strokeDashArray: 0,
            position: 'back',
            xaxis: {
              lines: {
                show: false
              }
            },
            yaxis: {
              lines: {
                show: true
              }
            },
            padding: {
              top: 20,
              right: 20,
              bottom: 20,
              left: 20
            }
          },
          markers: {
            size: 6,
            colors: ['#8b5cf6'],
            strokeColors: '#ffffff',
            strokeWidth: 2,
            hover: {
              size: 8,
              sizeOffset: 2
            }
          },
          tooltip: {
            enabled: true,
            theme: 'light',
            style: {
              fontSize: '14px',
              fontFamily: 'Inter, sans-serif'
            },
            custom: function({series, seriesIndex, dataPointIndex, w}) {
              const value = series[seriesIndex][dataPointIndex];
              const category = w.globals.labels[dataPointIndex];
              return `
                <div style="
                  background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
                  color: white;
                  padding: 12px 16px;
                  border-radius: 12px;
                  box-shadow: 0 10px 25px rgba(139, 92, 246, 0.3);
                  border: none;
                  font-weight: 500;
                ">
                  <div style="font-size: 12px; opacity: 0.9; margin-bottom: 4px;">${category}</div>
                  <div style="font-size: 16px; font-weight: 600;">${value} employees</div>
                </div>
              `;
            }
          }
        };



      // Handle chart filter form submission
      document.querySelector('.chart-filter-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const month = document.getElementById('month-chart').value;
        const year = document.getElementById('year-chart').value;
        const currentUrl = new URL(window.location.href);

        // Update URL parameters
        currentUrl.searchParams.set('month', month);
        currentUrl.searchParams.set('year', year);

        // Reload page with new filters
        window.location.href = currentUrl.toString();
      });

      // Global chart variable
      var barChart;

      // Update chart when filters change
      function updateChart() {
        try {
          console.log('updateChart called, barChart exists:', !!barChart);
          console.log('headcountData:', headcountData);
          console.log('chartData:', chartData);

          if (barChart && typeof barChart.updateOptions === 'function' && headcountData && headcountData.length > 0) {
            console.log('Updating chart with data...');

            barChart.updateOptions({
              xaxis: {
                categories: <%- JSON.stringify(chartCategories || []) %>
              }
            });

            barChart.updateSeries([{
              name: 'Headcount',
              data: chartData
            }]);

            console.log('Chart updated successfully');
          } else {
            console.log('Chart update skipped - conditions not met');
          }
        } catch (error) {
          console.error('Error in updateChart:', error);
        }
      }

      // ===== PAYROLL CALENDAR FUNCTIONALITY =====

      let payrollCalendar;
      let payrollEvents = [];
      let calendarStats = {
        pending: 0,
        overdue: 0,
        thisMonth: 0
      };

      // Initialize Payroll Calendar
      function initializePayrollCalendar() {
        const calendarEl = document.getElementById('payrollCalendar');

        if (!calendarEl) {
          // Commented out to prevent console errors - calendar element not present on dashboard
          // console.error('Calendar element not found');
          return;
        }

        try {
          payrollCalendar = new FullCalendar.Calendar(calendarEl, {
          initialView: 'dayGridMonth',
          headerToolbar: {
            left: 'prev,next today',
            center: 'title',
            right: 'dayGridMonth,timeGridWeek,listWeek'
          },
          height: 'auto',
          contentHeight: 'auto',
          aspectRatio: 1.35,
          events: payrollEvents,
          eventClick: function(info) {
            showEventDetails(info.event);
          },
          dateClick: function(info) {
            showAddEventModal(info.date);
          },
          eventDidMount: function(info) {
            // Add custom styling based on event type and status
            const event = info.event;
            const type = event.extendedProps.type;
            const status = event.extendedProps.status;

            // Apply type-based colors
            if (type === 'emp201') {
              info.el.style.backgroundColor = '#ef4444';
              info.el.style.borderColor = '#dc2626';
            } else if (type === 'emp501') {
              info.el.style.backgroundColor = '#dc2626';
              info.el.style.borderColor = '#b91c1c';
            } else if (type === 'irp5' || type === 'it3a') {
              info.el.style.backgroundColor = '#6366f1';
              info.el.style.borderColor = '#4f46e5';
            } else if (type === 'uif') {
              info.el.style.backgroundColor = '#8b5cf6';
              info.el.style.borderColor = '#7c3aed';
            } else if (type === 'sdl') {
              info.el.style.backgroundColor = '#06b6d4';
              info.el.style.borderColor = '#0891b2';
            } else if (type === 'eti') {
              info.el.style.backgroundColor = '#10b981';
              info.el.style.borderColor = '#059669';
            } else if (type === 'paye') {
              info.el.style.backgroundColor = '#f59e0b';
              info.el.style.borderColor = '#d97706';
            } else if (type === 'compliance') {
              info.el.style.backgroundColor = '#6b7280';
              info.el.style.borderColor = '#4b5563';
            } else if (type === 'cutoff') {
              info.el.style.backgroundColor = '#f59e0b';
              info.el.style.borderColor = '#d97706';
            } else if (type === 'custom') {
              info.el.style.backgroundColor = '#22c55e';
              info.el.style.borderColor = '#16a34a';
            }

            // Apply status-based styling
            if (status === 'pending') {
              info.el.style.opacity = '0.8';
              info.el.style.borderStyle = 'dashed';
            } else if (status === 'overdue') {
              info.el.style.opacity = '0.6';
              info.el.classList.add('overdue-event');
            } else if (status === 'completed') {
              info.el.style.opacity = '0.5';
              info.el.style.textDecoration = 'line-through';
            }
          }
        });

        payrollCalendar.render();

        // Force calendar to resize after render
        setTimeout(() => {
          if (payrollCalendar) {
            payrollCalendar.updateSize();
          }
        }, 100);

        } catch (error) {
          console.error('Error creating FullCalendar:', error);
        }
      }

      // Load payroll events from API
      async function loadPayrollEvents() {
        try {
          const response = await fetch('/api/payroll-calendar/events');
          const data = await response.json();

          if (data.success) {
            payrollEvents = data.data.map(event => ({
              id: event._id,
              title: event.title,
              start: event.date,
              end: event.endDate || event.date,
              allDay: true,
              extendedProps: {
                type: event.type,
                status: event.status,
                description: event.description,
                priority: event.priority,
                assignedTo: event.assignedTo
              }
            }));

            if (payrollCalendar) {
              payrollCalendar.removeAllEvents();
              payrollCalendar.addEventSource(payrollEvents);
            }

            updateUpcomingEvents();
          }
        } catch (error) {
          console.error('Error loading payroll events:', error);
        }
      }

      // Load calendar statistics
      async function loadCalendarStats() {
        try {
          const response = await fetch('/api/payroll-calendar/stats');
          const data = await response.json();

          if (data.success) {
            calendarStats = data.data;
            updateStatsDisplay();
          }
        } catch (error) {
          console.error('Error loading calendar stats:', error);
        }
      }

      // Update stats display - commented out to prevent console errors
      function updateStatsDisplay() {
        // Commented out to prevent console errors - elements not present on dashboard
        /*
        document.getElementById('pendingCount').textContent = calendarStats.pending || 0;
        document.getElementById('overdueCount').textContent = calendarStats.overdue || 0;
        document.getElementById('thisMonthCount').textContent = calendarStats.thisMonth || 0;
        */
      }

      // Update upcoming events list
      async function updateUpcomingEvents() {
        try {
          const response = await fetch('/api/payroll-calendar/upcoming');
          const data = await response.json();

          if (data.success) {
            const eventsList = document.getElementById('upcomingEventsList');
            // Commented out to prevent console errors - element not present on dashboard
            if (!eventsList) return;
            eventsList.innerHTML = '';

            if (data.data.length === 0) {
              eventsList.innerHTML = '<p style="color: #64748b; text-align: center; padding: 1rem;">No upcoming events</p>';
              return;
            }

            data.data.forEach(event => {
              const eventEl = document.createElement('div');
              eventEl.className = 'event-item';

              const eventDate = new Date(event.date);
              const today = new Date();
              const daysUntil = Math.ceil((eventDate - today) / (1000 * 60 * 60 * 24));

              const priorityColors = {
                'critical': '#ef4444',
                'high': '#f59e0b',
                'medium': '#6366f1',
                'low': '#6b7280'
              };

              const typeLabels = {
                'emp201': 'EMP201',
                'emp501': 'EMP501',
                'irp5': 'IRP5/IT3a',
                'it3a': 'IT3a',
                'uif': 'UIF',
                'sdl': 'SDL',
                'eti': 'ETI',
                'paye': 'PAYE',
                'compliance': 'Compliance',
                'cutoff': 'Cut-off',
                'processing': 'Processing',
                'payment': 'Payment',
                'custom': 'Custom',
                'reminder': 'Reminder'
              };

              const priorityIcons = {
                'critical': 'ph-warning-circle',
                'high': 'ph-warning',
                'medium': 'ph-info',
                'low': 'ph-note'
              };

              eventEl.innerHTML = `
                <div class="event-item-header">
                  <div>
                    <div class="event-title">${event.title}</div>
                    <div class="event-date">
                      <i class="ph ph-calendar"></i>
                      ${eventDate.toLocaleDateString('en-US', {
                        weekday: 'short',
                        month: 'short',
                        day: 'numeric'
                      })}
                      ${daysUntil === 0 ? '(Today)' :
                        daysUntil === 1 ? '(Tomorrow)' :
                        daysUntil > 0 ? `(${daysUntil} days)` : '(Overdue)'}
                    </div>
                  </div>
                  <div class="event-type" style="background: ${priorityColors[event.priority || 'medium']}20; color: ${priorityColors[event.priority || 'medium']};">
                    ${typeLabels[event.type] || event.type.toUpperCase()}
                  </div>
                </div>
                <div class="event-priority ${event.priority || 'medium'}">
                  <i class="ph ${priorityIcons[event.priority || 'medium']}"></i>
                  ${(event.priority || 'medium').charAt(0).toUpperCase() + (event.priority || 'medium').slice(1)} Priority
                </div>
              `;

              eventEl.addEventListener('click', () => showEventDetails(event));
              eventsList.appendChild(eventEl);
            });
          }
        } catch (error) {
          console.error('Error loading upcoming events:', error);
        }
      }

      // Show event details modal
      function showEventDetails(event) {
        const modal = document.getElementById('eventDetailsModal');
        const content = document.getElementById('eventDetailsContent');

        const eventDate = new Date(event.start || event.date);
        const typeLabels = {
          'custom': 'Custom Event',
          'cutoff': 'Payroll Cut-off',
          'processing': 'Processing Deadline',
          'payment': 'Payment Date',
          'emp201': 'EMP201 Monthly Submission',
          'emp501': 'EMP501 Reconciliation',
          'irp5': 'IRP5/IT3(a) Submission',
          'it3a': 'IT3(a) Submission',
          'uif': 'UIF Submission',
          'sdl': 'SDL Submission',
          'eti': 'Employment Tax Incentive',
          'paye': 'PAYE Related',
          'compliance': 'General Compliance',
          'reminder': 'Reminder'
        };

        content.innerHTML = `
          <div class="event-details">
            <div class="detail-item">
              <label><i class="ph ph-text-aa"></i> Title:</label>
              <span>${event.title}</span>
            </div>
            <div class="detail-item">
              <label><i class="ph ph-calendar"></i> Date:</label>
              <span>${eventDate.toLocaleDateString()}</span>
            </div>
            <div class="detail-item">
              <label><i class="ph ph-tag"></i> Type:</label>
              <span>${typeLabels[event.extendedProps?.type || event.type] || 'Unknown'}</span>
            </div>
            ${event.extendedProps?.description || event.description ? `
            <div class="detail-item">
              <label><i class="ph ph-note"></i> Description:</label>
              <span>${event.extendedProps?.description || event.description}</span>
            </div>
            ` : ''}
            <div class="detail-item">
              <label><i class="ph ph-flag"></i> Priority:</label>
              <span class="priority-badge priority-${event.extendedProps?.priority || 'medium'}">
                ${(event.extendedProps?.priority || 'medium').charAt(0).toUpperCase() + (event.extendedProps?.priority || 'medium').slice(1)}
              </span>
            </div>
          </div>
        `;

        modal.style.display = 'flex';
        setTimeout(() => modal.classList.add('show'), 10);
      }

      // Show add event modal
      function showAddEventModal(date) {
        const modal = document.getElementById('addEventModal');
        const dateInput = document.getElementById('eventDate');

        // Reset form
        document.getElementById('addEventForm').reset();

        // Set the selected date (fix timezone issue)
        if (date) {
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          dateInput.value = `${year}-${month}-${day}`;
        }

        modal.style.display = 'flex';
        setTimeout(() => modal.classList.add('show'), 10);
      }

      // Close modals
      function closeAddEventModal() {
        const modal = document.getElementById('addEventModal');
        modal.classList.remove('show');
        setTimeout(() => modal.style.display = 'none', 300);
      }

      function closeEventDetailsModal() {
        const modal = document.getElementById('eventDetailsModal');
        modal.classList.remove('show');
        setTimeout(() => modal.style.display = 'none', 300);
      }

      // Submit add event form
      function submitAddEvent() {
        const form = document.getElementById('addEventForm');
        const formData = new FormData(form);

        const eventData = {
          title: formData.get('title'),
          date: formData.get('date'),
          type: formData.get('type'),
          description: formData.get('description'),
          priority: formData.get('priority'),
          status: 'pending'
        };

        // Validate required fields
        if (!eventData.title || !eventData.date) {
          Notifications.error('Please fill in all required fields');
          return;
        }

        addNewEvent(eventData);
        closeAddEventModal();
      }

      // Show confirmation toast with action buttons
      function showConfirmationToast(message, onConfirm, actionText = 'Confirm') {
        // Create toast container if it doesn't exist
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
          toastContainer = document.createElement('div');
          toastContainer.id = 'toast-container';
          toastContainer.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 10000;';
          document.body.appendChild(toastContainer);
        }

        // Create confirmation toast
        const toast = document.createElement('div');
        toast.className = 'toast toast-warning show';
        toast.style.cssText = 'position: relative; margin-bottom: 10px; transform: translateY(0); opacity: 1;';

        toast.innerHTML = `
          <div class="toast-content">
            <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 15px;">
              <i class="ph ph-warning" style="font-size: 20px;"></i>
              <span>${message}</span>
            </div>
            <div class="toast-actions" style="display: flex; gap: 10px; justify-content: flex-end;">
              <button class="action-button cancel-btn" style="background: rgba(255,255,255,0.9); border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                <i class="ph ph-x"></i> Cancel
              </button>
              <button class="action-button confirm-btn" style="background: rgba(255,255,255,0.9); border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-weight: bold;">
                <i class="ph ph-check"></i> ${actionText}
              </button>
            </div>
          </div>
        `;

        // Add event listeners
        const cancelBtn = toast.querySelector('.cancel-btn');
        const confirmBtn = toast.querySelector('.confirm-btn');

        cancelBtn.addEventListener('click', () => {
          toast.remove();
        });

        confirmBtn.addEventListener('click', () => {
          toast.remove();
          onConfirm();
        });

        toastContainer.appendChild(toast);

        // Auto-remove after 10 seconds
        setTimeout(() => {
          if (toast.parentNode) {
            toast.remove();
          }
        }, 10000);
      }

      // Close modals when clicking outside
      window.addEventListener('click', function(event) {
        const addModal = document.getElementById('addEventModal');
        const detailsModal = document.getElementById('eventDetailsModal');

        if (event.target === addModal) {
          closeAddEventModal();
        }
        if (event.target === detailsModal) {
          closeEventDetailsModal();
        }
      });

      // Close modals with Escape key
      document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
          const addModal = document.getElementById('addEventModal');
          const detailsModal = document.getElementById('eventDetailsModal');

          if (addModal.classList.contains('show')) {
            closeAddEventModal();
          }
          if (detailsModal.classList.contains('show')) {
            closeEventDetailsModal();
          }
        }
      });

      // Add new event
      async function addNewEvent(eventData) {
        try {
          const response = await fetch('/api/payroll-calendar/events', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(eventData)
          });

          const data = await response.json();

          if (data.success) {
            await loadPayrollEvents();
            await loadCalendarStats();
            await loadCompactCalendarEvents(); // Also update compact calendar
            Notifications.success('Event added successfully!');
          } else {
            Notifications.error('Error adding event: ' + data.message);
          }
        } catch (error) {
          console.error('Error adding event:', error);
          Notifications.error('Error adding event');
        }
      }

      // Initialize comprehensive South African compliance events
      async function initializeComplianceEvents(force = false) {
        try {
          const year = new Date().getFullYear();
          const response = await fetch('/api/payroll-calendar/initialize-comprehensive-compliance', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ year: year, force: force })
          });

          const data = await response.json();

          if (data.success) {
            await loadPayrollEvents();
            await loadCalendarStats();

            // Show detailed success message
            const details = data.details;
            let message = data.message;
            if (details && details.createdByType) {
              const typesList = Object.entries(details.createdByType)
                .map(([type, count]) => `${count} ${type.toUpperCase()}`)
                .join(', ');
              message += `\n\nCreated events: ${typesList}`;
            }

            Notifications.success(message);
          } else if (data.isDuplicate) {
            // Handle duplicate events with detailed feedback
            handleDuplicateEventsResponse(data);
          } else {
            Notifications.error('Error: ' + data.message);
          }
        } catch (error) {
          console.error('Error initializing compliance events:', error);
          Notifications.error('Error initializing compliance events');
        }
      }

      // Handle duplicate events response with detailed options
      function handleDuplicateEventsResponse(data) {
        const existingTypes = data.data.existingTypes.join(', ');
        const existingCount = data.data.existingCount;

        const message = `Found ${existingCount} existing compliance events for ${data.data.year}.\n\nExisting types: ${existingTypes}\n\nWould you like to:`;

        showCustomConfirmationToast(
          message,
          [
            {
              text: 'Cancel',
              style: 'secondary',
              action: () => {}
            },
            {
              text: 'View Existing Events',
              style: 'info',
              action: () => {
                // Filter calendar to show existing compliance events
                document.getElementById('typeFilter').value = 'all';
                loadPayrollEvents();
                Notifications.info('Showing existing compliance events in calendar');
              }
            },
            {
              text: 'Force Recreate',
              style: 'warning',
              action: () => {
                showConfirmationToast(
                  'This will DELETE all existing comprehensive compliance events and create new ones. This action cannot be undone. Continue?',
                  () => initializeComplianceEvents(true),
                  'Force Recreate Events'
                );
              }
            }
          ],
          'Duplicate Compliance Events Found'
        );
      }

      // Custom confirmation toast with multiple action buttons
      function showCustomConfirmationToast(message, actions, title = 'Confirmation') {
        const toastContainer = document.getElementById('toast-container');
        if (!toastContainer) return;

        const toastId = 'toast-' + Date.now();

        const actionsHtml = actions.map(action =>
          `<button class="btn btn-${action.style} btn-sm me-2" onclick="handleCustomToastAction('${toastId}', ${actions.indexOf(action)})">${action.text}</button>`
        ).join('');

        const toastHtml = `
          <div id="${toastId}" class="toast align-items-center border-0" role="alert" aria-live="assertive" aria-atomic="true" data-bs-autohide="false">
            <div class="d-flex">
              <div class="toast-body">
                <div class="d-flex align-items-start">
                  <div class="me-3">
                    <i class="ph ph-warning-circle text-warning" style="font-size: 1.5rem;"></i>
                  </div>
                  <div class="flex-grow-1">
                    <h6 class="mb-1">${title}</h6>
                    <p class="mb-2 small">${message.replace(/\n/g, '<br>')}</p>
                    <div class="d-flex flex-wrap gap-1">
                      ${actionsHtml}
                    </div>
                  </div>
                </div>
              </div>
              <button type="button" class="btn-close me-2 m-auto" onclick="closeCustomToast('${toastId}')" aria-label="Close"></button>
            </div>
          </div>
        `;

        toastContainer.insertAdjacentHTML('beforeend', toastHtml);

        // Store actions for later use
        window.customToastActions = window.customToastActions || {};
        window.customToastActions[toastId] = actions;

        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement);
        toast.show();
      }

      // Handle custom toast action
      function handleCustomToastAction(toastId, actionIndex) {
        const actions = window.customToastActions[toastId];
        if (actions && actions[actionIndex]) {
          actions[actionIndex].action();
        }
        closeCustomToast(toastId);
      }

      // Close custom toast
      function closeCustomToast(toastId) {
        const toastElement = document.getElementById(toastId);
        if (toastElement) {
          const toast = bootstrap.Toast.getInstance(toastElement);
          if (toast) {
            toast.hide();
          }
          setTimeout(() => {
            toastElement.remove();
            if (window.customToastActions) {
              delete window.customToastActions[toastId];
            }
          }, 300);
        }
      }

      // Event listeners - moved inside DOMContentLoaded
      function attachEventListeners() {
        console.log('🔧 Attaching event listeners...');

        const addEventBtn = document.getElementById('addEventBtn');
        const initializeComplianceBtn = document.getElementById('initializeComplianceBtn');
        // Commented out to prevent console errors - buttons not present on dashboard
        // const sendRemindersBtn = document.getElementById('sendRemindersBtn');
        // const reminderStatusBtn = document.getElementById('reminderStatusBtn');

        console.log('📋 Button elements found:', {
          addEventBtn: !!addEventBtn,
          initializeComplianceBtn: !!initializeComplianceBtn
          // sendRemindersBtn and reminderStatusBtn commented out - not present on dashboard
        });

        if (addEventBtn) {
          addEventBtn.addEventListener('click', () => {
            showAddEventModal(new Date());
          });
          console.log('✅ Add Event button listener attached');
        }

        if (initializeComplianceBtn) {
          initializeComplianceBtn.addEventListener('click', () => {
            // Show confirmation toast with action buttons
            showConfirmationToast(
              'This will create comprehensive South African payroll compliance events including EMP201, EMP501, IRP5/IT3a, UIF, SDL, and other SARS deadlines for the current year. Continue?',
              () => initializeComplianceEvents(),
              'Initialize Compliance Events'
            );
          });
          console.log('✅ Initialize Compliance button listener attached');
        }

        // Commented out to prevent console errors - buttons not present on dashboard
        /*
        if (sendRemindersBtn) {
          sendRemindersBtn.addEventListener('click', (e) => {
            console.log('📧 Send Reminders button clicked');
            e.preventDefault();
            showConfirmationToast(
              'This will manually trigger email reminders for all upcoming compliance events. Continue?',
              () => sendEmailReminders(),
              'Send Email Reminders'
            );
          });
          console.log('✅ Send Reminders button listener attached');
        } else {
          console.error('❌ Send Reminders button not found');
        }

        if (reminderStatusBtn) {
          reminderStatusBtn.addEventListener('click', (e) => {
            console.log('📊 Reminder Status button clicked');
            e.preventDefault();
            showReminderStatus();
          });
          console.log('✅ Reminder Status button listener attached');
        } else {
          console.error('❌ Reminder Status button not found');
        }
        */
      }

      // Send email reminders manually
      async function sendEmailReminders() {
        console.log('📧 sendEmailReminders function called');

        try {
          console.log('📡 Making API request to /api/payroll-calendar/send-reminders');

          const response = await fetch('/api/payroll-calendar/send-reminders', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            }
          });

          console.log('📡 API Response status:', response.status, response.statusText);

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const data = await response.json();
          console.log('📧 API Response data:', data);

          if (data.success) {
            const result = data.data;
            let message = `Email reminders processed successfully!\n\n`;
            message += `✅ Sent: ${result.successCount}\n`;
            message += `❌ Failed: ${result.failureCount}\n`;
            message += `📊 Total Processed: ${result.totalProcessed}`;

            console.log('✅ Showing success notification');
            Notifications.success(message);
          } else {
            console.error('❌ API returned error:', data.message);
            Notifications.error('Error: ' + data.message);
          }
        } catch (error) {
          console.error('❌ Error sending email reminders:', error);
          Notifications.error('Error sending email reminders: ' + error.message);
        }
      }

      // Show reminder status
      async function showReminderStatus() {
        console.log('📊 showReminderStatus function called');

        try {
          console.log('📡 Making API request to /api/payroll-calendar/reminder-status');

          const response = await fetch('/api/payroll-calendar/reminder-status');
          console.log('📡 API Response status:', response.status, response.statusText);

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const data = await response.json();
          console.log('📊 API Response data:', data);

          if (data.success) {
            console.log('✅ Displaying reminder status modal');
            displayReminderStatusModal(data.data, data.summary);
          } else {
            console.error('❌ API returned error:', data.message);
            Notifications.error('Error: ' + data.message);
          }
        } catch (error) {
          console.error('❌ Error fetching reminder status:', error);
          Notifications.error('Error fetching reminder status: ' + error.message);
        }
      }

      // Display reminder status in a modal
      function displayReminderStatusModal(reminderData, summary) {
        console.log('📋 displayReminderStatusModal called with:', { reminderData, summary });

        const modalHtml = `
          <div id="reminderStatusModal" class="modal" style="display: block;">
            <div class="modal-content" style="max-width: 800px;">
              <div class="modal-header">
                <h2><i class="ph ph-info"></i> Email Reminder Status</h2>
                <button class="modal-close" onclick="closeReminderStatusModal()">
                  <i class="ph ph-x"></i>
                </button>
              </div>
              <div class="modal-body">
                <div class="reminder-summary mb-4">
                  <h4>Summary</h4>
                  <div class="row">
                    <div class="col-md-4">
                      <div class="stat-card">
                        <div class="stat-number">${summary.totalEvents}</div>
                        <div class="stat-label">Total Events</div>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="stat-card">
                        <div class="stat-number">${summary.eventsWithPendingReminders}</div>
                        <div class="stat-label">Events with Pending Reminders</div>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="stat-card">
                        <div class="stat-number">${summary.totalPendingReminders}</div>
                        <div class="stat-label">Total Pending Reminders</div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="reminder-details">
                  <h4>Event Details</h4>
                  <div class="table-responsive">
                    <table class="table table-striped">
                      <thead>
                        <tr>
                          <th>Event</th>
                          <th>Date</th>
                          <th>Days Until</th>
                          <th>Sent Reminders</th>
                          <th>Pending Reminders</th>
                          <th>Status</th>
                        </tr>
                      </thead>
                      <tbody>
                        ${reminderData.map(event => `
                          <tr>
                            <td>
                              <div class="event-title">${event.title}</div>
                              <small class="text-muted">${event.type.toUpperCase()}</small>
                            </td>
                            <td>${new Date(event.date).toLocaleDateString()}</td>
                            <td>
                              <span class="badge ${event.daysUntilEvent <= 3 ? 'bg-danger' : event.daysUntilEvent <= 7 ? 'bg-warning' : 'bg-info'}">
                                ${event.daysUntilEvent} days
                              </span>
                            </td>
                            <td>
                              ${event.sentReminders.map(r => `
                                <span class="badge bg-success me-1">${r.daysBeforeEvent}d</span>
                              `).join('')}
                            </td>
                            <td>
                              ${event.pendingReminders.map(r => `
                                <span class="badge bg-warning me-1">${r}d</span>
                              `).join('')}
                            </td>
                            <td>
                              ${event.hasPendingReminders ?
                                '<span class="badge bg-warning">Pending</span>' :
                                '<span class="badge bg-success">Up to date</span>'
                              }
                            </td>
                          </tr>
                        `).join('')}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeReminderStatusModal()">
                  <i class="ph ph-x"></i> Close
                </button>
                ${summary.totalPendingReminders > 0 ? `
                  <button type="button" class="btn btn-primary" onclick="closeReminderStatusModal(); sendEmailReminders();">
                    <i class="ph ph-paper-plane"></i> Send Pending Reminders
                  </button>
                ` : ''}
              </div>
            </div>
          </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
        console.log('✅ Reminder status modal inserted into DOM');

        // Verify modal was inserted
        const insertedModal = document.getElementById('reminderStatusModal');
        if (insertedModal) {
          console.log('✅ Modal element found in DOM');
        } else {
          console.error('❌ Modal element not found after insertion');
        }
      }

      // Close reminder status modal
      function closeReminderStatusModal() {
        const modal = document.getElementById('reminderStatusModal');
        if (modal) {
          modal.remove();
        }
      }

      // Filter controls - moved inside attachEventListeners
      function attachCalendarControls() {
        // Filter controls
        const typeFilter = document.getElementById('typeFilter');
        const statusFilter = document.getElementById('statusFilter');

        if (typeFilter) {
          typeFilter.addEventListener('change', (e) => {
            filterEvents();
          });
        }

        if (statusFilter) {
          statusFilter.addEventListener('change', (e) => {
            filterEvents();
          });
        }
      }

      function filterEvents() {
        const typeFilter = document.getElementById('typeFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;

        let filteredEvents = [...payrollEvents];

        if (typeFilter !== 'all') {
          filteredEvents = filteredEvents.filter(event => event.extendedProps.type === typeFilter);
        }

        if (statusFilter !== 'all') {
          filteredEvents = filteredEvents.filter(event => event.extendedProps.status === statusFilter);
        }

        payrollCalendar.removeAllEvents();
        payrollCalendar.addEventSource(filteredEvents);
      }





      // Update time indicator
      function updateTimeIndicator() {
        const timeElement = document.getElementById('current-time');
        if (timeElement) {
          const now = new Date();
          const timeString = now.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
          });
          const dateString = now.toLocaleDateString('en-US', {
            weekday: 'short',
            month: 'short',
            day: 'numeric'
          });
          timeElement.textContent = `${dateString} • ${timeString}`;
        }
      }

      // Initialize everything when page loads
      document.addEventListener('DOMContentLoaded', function() {
        // Initialize time indicator
        updateTimeIndicator();
        setInterval(updateTimeIndicator, 60000); // Update every minute

        // Initialize Charts - Wait for libraries to load
        window.loadChartLibraries().then(() => {
          try {
            if (typeof ApexCharts !== 'undefined') {
              // Optimize chart options for faster rendering and better Speed Index
              const optimizedOptions = {
                ...barChartOptions,
                chart: {
                  ...barChartOptions.chart,
                  animations: {
                    enabled: false // Disable animations for faster initial render
                  },
                  redrawOnParentResize: false, // Reduce redraws
                  redrawOnWindowResize: false
                }
              };

              barChart = new ApexCharts(document.querySelector("#bar-chart"), optimizedOptions);

              // Use scheduler to avoid blocking main thread
              const renderChart = () => {
                if ('scheduler' in window && 'postTask' in scheduler) {
                  scheduler.postTask(() => {
                    barChart.render().then(() => {
                      // Hide skeleton loader when chart is ready
                      const skeleton = document.querySelector('.chart-skeleton');
                      if (skeleton) {
                        skeleton.style.display = 'none';
                      }
                      console.log('Chart rendered successfully via scheduler');
                      // Update chart after successful render
                      setTimeout(() => updateChart(), 50);
                    }).catch(error => {
                      console.error('Chart render error (scheduler):', error);
                    });
                  }, { priority: 'user-blocking' });
                } else {
                  // Fallback: Use requestIdleCallback or setTimeout
                  const renderFn = () => {
                    barChart.render().then(() => {
                      const skeleton = document.querySelector('.chart-skeleton');
                      if (skeleton) {
                        skeleton.style.display = 'none';
                      }
                      console.log('Chart rendered successfully via fallback');
                      // Update chart after successful render
                      setTimeout(() => updateChart(), 50);
                    }).catch(error => {
                      console.error('Chart render error (fallback):', error);
                    });
                  };

                  if ('requestIdleCallback' in window) {
                    requestIdleCallback(renderFn, { timeout: 100 });
                  } else {
                    setTimeout(renderFn, 0);
                  }
                }
              };

              // Start rendering
              renderChart();

            } else {
              console.warn('ApexCharts not available, skipping chart initialization');
              // Hide skeleton even if chart fails to load
              const skeleton = document.querySelector('.chart-skeleton');
              if (skeleton) {
                skeleton.style.display = 'none';
              }
            }

          // Add resize handler for charts
          window.addEventListener('resize', function() {
            barChart.updateOptions({
              chart: {
                height: window.innerWidth < 768 ? 300 : 350
              }
            });
          });

          // Add animation on scroll for charts
          const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
              if (entry.isIntersecting && chartData) {
                barChart.updateSeries([{
                  data: chartData
                }]);
              }
            });
          });

          observer.observe(document.querySelector('.charts-card'));
          } catch (error) {
            console.error('Error initializing charts:', error);
          }
        }).catch(error => {
          console.error('Error loading chart libraries:', error);
        });

        // Always attach event listeners first (critical for reminder functionality)
        try {
          attachEventListeners();
          console.log('✅ Event listeners attached successfully');
        } catch (error) {
          console.error('❌ Error attaching event listeners:', error);
        }

        // Initialize Payroll Calendar - Lazy load FullCalendar
        window.loadCalendarLibrary().then(() => {
          if (typeof FullCalendar !== 'undefined') {
            try {
              initializePayrollCalendar();
              loadPayrollEvents();
              loadCalendarStats();
              attachCalendarControls();
            } catch (error) {
              console.error('Error initializing payroll calendar:', error);

              // Show error message to user
              const calendarEl = document.getElementById('payrollCalendar');
              if (calendarEl) {
                calendarEl.innerHTML = `
                  <div class="alert alert-danger" role="alert">
                    <h5>Calendar Loading Error</h5>
                    <p>There was an error loading the payroll calendar. Please refresh the page or contact support.</p>
                    <small>Error: ${error.message}</small>
                  </div>
                `;
              }
            }
          } else {
            console.error('FullCalendar library not loaded');

            // Show fallback message to user
            const calendarEl = document.getElementById('payrollCalendar');
            if (calendarEl) {
              calendarEl.innerHTML = `
                <div class="alert alert-warning" role="alert">
                  <h5>Calendar Library Not Available</h5>
                  <p>The calendar library failed to load. Please check your internet connection and refresh the page.</p>
                  <button class="btn btn-primary" onclick="location.reload()">Refresh Page</button>
                </div>
              `;
            }
          }
        }).catch(error => {
          console.error('Error loading calendar libraries:', error);
        });

        // Initialize reference design calendar (compact calendar)
        try {
          initializeReferenceCalendar();
          console.log('✅ Reference calendar initialized successfully');
        } catch (error) {
          console.error('❌ Error initializing reference calendar:', error);
        }
      });

      // Calendar functionality
      let currentDate = new Date();

      function generateCalendar(year, month) {
        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        const daysInMonth = lastDay.getDate();
        const startingDayOfWeek = firstDay.getDay();

        const calendarDays = document.getElementById('calendarDays');
        const currentMonth = document.getElementById('currentMonth');

        // Update month display
        const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
          'July', 'August', 'September', 'October', 'November', 'December'];
        currentMonth.textContent = `${monthNames[month]} ${year}`;

        // Clear previous days
        calendarDays.innerHTML = '';

        // Add empty cells for days before the first day of the month
        for (let i = 0; i < startingDayOfWeek; i++) {
          const emptyDay = document.createElement('div');
          emptyDay.className = 'calendar-day other-month';
          const prevMonth = month === 0 ? 11 : month - 1;
          const prevYear = month === 0 ? year - 1 : year;
          const prevMonthLastDay = new Date(prevYear, prevMonth + 1, 0).getDate();
          emptyDay.textContent = prevMonthLastDay - startingDayOfWeek + i + 1;
          calendarDays.appendChild(emptyDay);
        }

        // Add days of the current month
        const today = new Date();
        for (let day = 1; day <= daysInMonth; day++) {
          const dayElement = document.createElement('div');
          dayElement.className = 'calendar-day';
          dayElement.textContent = day;

          // Highlight today
          if (year === today.getFullYear() && month === today.getMonth() && day === today.getDate()) {
            dayElement.classList.add('today');
          }

          calendarDays.appendChild(dayElement);
        }

        // Add days from next month to fill the grid
        const totalCells = calendarDays.children.length;
        const remainingCells = 42 - totalCells; // 6 rows × 7 days
        for (let day = 1; day <= remainingCells && remainingCells < 7; day++) {
          const nextMonthDay = document.createElement('div');
          nextMonthDay.className = 'calendar-day other-month';
          nextMonthDay.textContent = day;
          calendarDays.appendChild(nextMonthDay);
        }
      }

      function changeMonth(direction) {
        currentDate.setMonth(currentDate.getMonth() + direction);
        generateCalendar(currentDate.getFullYear(), currentDate.getMonth());
      }

      // Initialize calendar (commented out - using reference design calendar instead)
      // generateCalendar(currentDate.getFullYear(), currentDate.getMonth());

      // ===== NEW CALENDAR FUNCTIONALITY (Reference Image Design) =====

      // Declare variables at the top to avoid hoisting issues
      let currentCalendarDate = new Date();
      let selectedDate = new Date(); // Today's date instead of fixed date
      let compactCalendarEvents = [];

      // Helper function to get the start of the week (Sunday)
      function getWeekStart(date) {
        const d = new Date(date);
        const day = d.getDay();
        const diff = d.getDate() - day;
        return new Date(d.setDate(diff));
      }

      function generateNewCalendar(year, month) {
        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        const daysInMonth = lastDay.getDate();
        const startingDayOfWeek = firstDay.getDay();

        // Get previous month info
        const prevMonth = new Date(year, month, 0);
        const daysInPrevMonth = prevMonth.getDate();

        const calendarGrid = document.getElementById('calendarDaysGrid');
        const monthTitle = document.getElementById('calendarMonthTitle');

        if (!calendarGrid || !monthTitle) return;

        // Update month title
        const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                           'July', 'August', 'September', 'October', 'November', 'December'];
        monthTitle.textContent = `${monthNames[month]} ${year}`;

        calendarGrid.innerHTML = '';

        // Add days from previous month
        for (let i = startingDayOfWeek - 1; i >= 0; i--) {
          const dayElement = document.createElement('div');
          dayElement.classList.add('calendar-day', 'other-month');
          dayElement.textContent = daysInPrevMonth - i;
          calendarGrid.appendChild(dayElement);
        }

        // Add days of current month
        for (let day = 1; day <= daysInMonth; day++) {
          const dayElement = document.createElement('div');
          dayElement.classList.add('calendar-day');
          dayElement.textContent = day;

          // Check if this is today
          const today = new Date();
          if (year === today.getFullYear() && month === today.getMonth() && day === today.getDate()) {
            dayElement.classList.add('today');
          }

          // Check if this is the selected date (November 15, 2023 from reference image)
          if (year === selectedDate.getFullYear() &&
              month === selectedDate.getMonth() &&
              day === selectedDate.getDate()) {
            dayElement.classList.add('selected');
          }

          // Add click handler
          dayElement.addEventListener('click', function() {
            // Remove previous selection
            document.querySelectorAll('.calendar-day.selected').forEach(el => {
              el.classList.remove('selected');
            });

            // Add selection to clicked day
            if (!dayElement.classList.contains('other-month')) {
              dayElement.classList.add('selected');
              selectedDate = new Date(year, month, day);
              updateScheduleDate();
            }
          });

          calendarGrid.appendChild(dayElement);
        }

        // Add days from next month to fill remaining cells
        const totalCells = calendarGrid.children.length;
        const remainingCells = 42 - totalCells; // 6 rows × 7 days = 42 cells
        for (let day = 1; day <= remainingCells; day++) {
          const dayElement = document.createElement('div');
          dayElement.classList.add('calendar-day', 'other-month');
          dayElement.textContent = day;
          calendarGrid.appendChild(dayElement);
        }
      }

      function changeCalendarMonth(direction) {
        // Update reference design calendar (now weekly navigation)
        currentCalendarDate.setDate(currentCalendarDate.getDate() + (direction * 7));
        generateReferenceCalendar(currentCalendarDate.getFullYear(), currentCalendarDate.getMonth());
        updateScheduleDate();
        updateCompactUpcomingSchedule();
      }

      function updateScheduleDate() {
        const scheduleDate = document.querySelector('.schedule-date-reference');
        if (scheduleDate && selectedDate) {
          const options = {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          };
          scheduleDate.textContent = selectedDate.toLocaleDateString('en-US', options);
        }
      }

      // Initialize reference design calendar
      function initializeReferenceCalendar() {
        // Variables are already initialized at the top
        // Just generate the calendar with current date
        generateReferenceCalendar(currentCalendarDate.getFullYear(), currentCalendarDate.getMonth());
        updateScheduleDate();
        loadCompactCalendarEvents(); // Load events for the compact calendar
      }

      // Load events for compact calendar
      async function loadCompactCalendarEvents() {
        try {
          const response = await fetch('/api/payroll-calendar/events');
          const data = await response.json();

          if (data.success) {
            compactCalendarEvents = data.data;
            generateReferenceCalendar(currentCalendarDate.getFullYear(), currentCalendarDate.getMonth());
            updateCompactUpcomingSchedule();
          }
        } catch (error) {
          console.error('Error loading compact calendar events:', error);
        }
      }

      function generateReferenceCalendar(year, month) {
        // Generate weekly view instead of monthly
        const today = new Date();
        const currentWeekStart = getWeekStart(currentCalendarDate);

        // Update title to show week range
        const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
          'July', 'August', 'September', 'October', 'November', 'December'];
        const monthTitle = document.getElementById('calendarMonthTitleCompact');
        if (monthTitle) {
          const weekEnd = new Date(currentWeekStart);
          weekEnd.setDate(weekEnd.getDate() + 6);

          if (currentWeekStart.getMonth() === weekEnd.getMonth()) {
            monthTitle.textContent = `${monthNames[currentWeekStart.getMonth()]} ${currentWeekStart.getFullYear()}`;
          } else {
            monthTitle.textContent = `${monthNames[currentWeekStart.getMonth()]} - ${monthNames[weekEnd.getMonth()]} ${currentWeekStart.getFullYear()}`;
          }
        }

        // Clear and populate calendar grid for weekly view
        const calendarGrid = document.getElementById('calendarDaysCompact');
        if (!calendarGrid) return;

        calendarGrid.innerHTML = '';

        // Generate 7 days for the current week
        for (let i = 0; i < 7; i++) {
          const dayDate = new Date(currentWeekStart);
          dayDate.setDate(currentWeekStart.getDate() + i);

          const dayElement = document.createElement('div');
          dayElement.className = 'day-reference';

          // Check if this day is in a different month
          if (dayDate.getMonth() !== currentCalendarDate.getMonth()) {
            dayElement.classList.add('other-month');
          }

          // Check if this day has events
          const dayEvents = compactCalendarEvents.filter(event => {
            const eventDate = new Date(event.date);
            return eventDate.getFullYear() === dayDate.getFullYear() &&
                   eventDate.getMonth() === dayDate.getMonth() &&
                   eventDate.getDate() === dayDate.getDate();
          });

          // Add event indicator if there are events
          if (dayEvents.length > 0) {
            dayElement.classList.add('has-events');
            const eventDot = document.createElement('div');
            eventDot.className = 'event-dot';
            dayElement.appendChild(eventDot);
          }

          // Check if this is today
          const today = new Date();
          if (dayDate.getFullYear() === today.getFullYear() &&
              dayDate.getMonth() === today.getMonth() &&
              dayDate.getDate() === today.getDate()) {
            dayElement.classList.add('today');
          }

          // Check if this is the selected date
          if (selectedDate &&
              dayDate.getFullYear() === selectedDate.getFullYear() &&
              dayDate.getMonth() === selectedDate.getMonth() &&
              dayDate.getDate() === selectedDate.getDate()) {
            dayElement.classList.add('selected');
          }

          dayElement.textContent = dayDate.getDate();

          // Add click handler for date selection
          dayElement.addEventListener('click', () => {
            // Remove previous selection
            const prevSelected = calendarGrid.querySelector('.selected');
            if (prevSelected) {
              prevSelected.classList.remove('selected');
            }
            // Add selection to clicked day
            dayElement.classList.add('selected');
            selectedDate = new Date(dayDate.getFullYear(), dayDate.getMonth(), dayDate.getDate());
            updateScheduleDate();

            // Show add event modal for selected date if there are no events
            if (dayEvents.length === 0) {
              showAddEventModal(selectedDate);
            }
          });

          calendarGrid.appendChild(dayElement);
        }
      }



      // Update upcoming schedule section with real data
      function updateCompactUpcomingSchedule() {
        const today = new Date();
        const upcomingEvents = compactCalendarEvents
          .filter(event => new Date(event.date) >= today)
          .sort((a, b) => new Date(a.date) - new Date(b.date))
          .slice(0, 3); // Show only next 3 events

        // Update the schedule date
        const scheduleDateEl = document.querySelector('.schedule-date-reference');
        if (scheduleDateEl && upcomingEvents.length > 0) {
          const nextEventDate = new Date(upcomingEvents[0].date);
          const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
          scheduleDateEl.textContent = nextEventDate.toLocaleDateString('en-US', options);
        }

        // Update schedule items with real events
        const scheduleList = document.querySelector('.schedule-list-reference');
        if (scheduleList && upcomingEvents.length > 0) {
          scheduleList.innerHTML = '';

          upcomingEvents.forEach(event => {
            const eventDate = new Date(event.date);
            const scheduleItem = document.createElement('div');
            scheduleItem.className = 'schedule-item-reference';

            // Determine modern icon based on event type
            let iconClass = 'ph-calendar';
            let iconColorClass = 'payroll-icon-reference';

            switch(event.type) {
              case 'emp201':
              case 'emp501':
              case 'paye':
              case 'cutoff':
              case 'processing':
              case 'payment':
                iconClass = 'ph-money';
                iconColorClass = 'payroll-icon-reference';
                break;
              case 'interview':
                iconClass = 'ph-user-plus';
                iconColorClass = 'interview-icon-reference';
                break;
              case 'meeting':
              case 'compliance':
                iconClass = 'ph-users';
                iconColorClass = 'meeting-icon-reference';
                break;
              case 'irp5':
              case 'it3a':
                iconClass = 'ph-file-text';
                iconColorClass = 'payroll-icon-reference';
                break;
              case 'uif':
              case 'sdl':
                iconClass = 'ph-shield-check';
                iconColorClass = 'meeting-icon-reference';
                break;
              case 'eti':
                iconClass = 'ph-chart-line-up';
                iconColorClass = 'interview-icon-reference';
                break;
              case 'reminder':
                iconClass = 'ph-bell';
                iconColorClass = 'interview-icon-reference';
                break;
              case 'custom':
              default:
                iconClass = 'ph-calendar-check';
                iconColorClass = 'payroll-icon-reference';
                break;
            }

            scheduleItem.innerHTML = `
              <div class="schedule-icon-reference ${iconColorClass}">
                <i class="${iconClass}"></i>
              </div>
              <div class="schedule-details-reference">
                <span class="schedule-title-text-reference">${event.title}</span>
                <span class="schedule-time-reference">${eventDate.toLocaleDateString()}</span>
              </div>
              <button class="schedule-action-btn-reference" onclick="showEventDetails({
                title: '${event.title}',
                date: '${event.date}',
                type: '${event.type}',
                description: '${event.description || ''}',
                priority: '${event.priority || 'medium'}'
              })">
                <i class="ph ph-caret-right"></i>
              </button>
            `;

            scheduleList.appendChild(scheduleItem);
          });
        }
      }

      // Reference design calendar initialization is handled in main DOMContentLoaded event

      // ===== REFERENCE DESIGN CALENDAR IS NOW ACTIVE =====
      // The compact calendar functionality has been replaced with the reference design above

      // Reference design calendar is complete and functional

    </script>

    <!-- Performance Optimization Script -->
    <script>
      // Immediate content visibility and Speed Index optimization
      (function() {
        // Force immediate rendering of critical content
        const style = document.createElement('style');
        style.textContent = `
          .greeting-card, .key-metrics, .three-card-row {
            opacity: 1 !important;
            visibility: visible !important;
            transform: translateZ(0) !important;
          }
          /* Reduce layout shifts for Speed Index */
          .charts-card, .notification-card, .calendar-card-reference {
            opacity: 1 !important;
            visibility: visible !important;
            transform: translateZ(0) !important;
          }
        `;
        document.head.appendChild(style);

        // Reduce critical request chains by batching operations
        const batchOperations = () => {
          // Force layout calculation once
          document.body.offsetHeight;

          // Batch DOM updates
          requestAnimationFrame(() => {
            const elements = document.querySelectorAll('.greeting-card, .key-metrics, .three-card-row');
            elements.forEach(el => {
              if (el) {
                el.style.willChange = 'auto'; // Optimize for Speed Index
              }
            });
          });
        };

        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', batchOperations);
        } else {
          batchOperations();
        }
      })();

      // Speed Index optimization - Break up long tasks
      const optimizeSpeedIndex = () => {
        const tasks = [
          () => {
            // Force early paint of above-the-fold content
            const aboveFold = document.querySelector('.greeting-card');
            if (aboveFold) {
              aboveFold.style.opacity = '1';
              aboveFold.style.transform = 'translateZ(0)';
            }
          },
          () => {
            // Prioritize key metrics rendering
            const metrics = document.querySelector('.key-metrics');
            if (metrics) {
              metrics.style.opacity = '1';
              metrics.style.transform = 'translateZ(0)';
            }
          },
          () => {
            // Optimize three-card-row visibility
            const threeCardRow = document.querySelector('.three-card-row');
            if (threeCardRow) {
              threeCardRow.style.opacity = '1';
              threeCardRow.style.transform = 'translateZ(0)';
            }
          },
          () => {
            // Force repaint for better Speed Index
            document.body.offsetHeight;
          }
        ];

        // Execute tasks with scheduling to avoid blocking main thread
        let taskIndex = 0;
        const executeTasks = () => {
          if (taskIndex < tasks.length) {
            tasks[taskIndex]();
            taskIndex++;

            // Use scheduler API if available, otherwise setTimeout
            if ('scheduler' in window && 'postTask' in scheduler) {
              scheduler.postTask(executeTasks, { priority: 'user-visible' });
            } else {
              setTimeout(executeTasks, 0);
            }
          }
        };

        executeTasks();
      };

      // Early rendering optimization for Speed Index
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', optimizeSpeedIndex);
      } else {
        optimizeSpeedIndex();
      }

      // Font loading detection to prevent layout shift
      if ('fonts' in document) {
        document.fonts.ready.then(() => {
          document.documentElement.classList.add('font-loaded');
        });
      } else {
        // Fallback for browsers without Font Loading API
        setTimeout(() => {
          document.documentElement.classList.add('font-loaded');
        }, 100);
      }

      // Register Service Worker for caching
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js')
            .then(registration => {
              console.log('SW registered: ', registration);
            })
            .catch(registrationError => {
              console.log('SW registration failed: ', registrationError);
            });
        });
      }

      // Performance monitoring
      if ('performance' in window) {
        window.addEventListener('load', () => {
          setTimeout(() => {
            const perfData = performance.getEntriesByType('navigation')[0];
            const metrics = {
              dns: perfData.domainLookupEnd - perfData.domainLookupStart,
              tcp: perfData.connectEnd - perfData.connectStart,
              request: perfData.responseStart - perfData.requestStart,
              response: perfData.responseEnd - perfData.responseStart,
              dom: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
              load: perfData.loadEventEnd - perfData.loadEventStart,
              total: perfData.loadEventEnd - perfData.navigationStart
            };
            console.log('Performance Metrics:', metrics);
          }, 0);
        });
      }

      // Chart libraries are now loaded immediately for better compatibility
      // Removed lazy loading observer as it was causing timing issues

      // Optimize image loading
      if ('loading' in HTMLImageElement.prototype) {
        const images = document.querySelectorAll('img[data-src]');
        images.forEach(img => {
          img.src = img.dataset.src;
        });
      } else {
        // Fallback for browsers that don't support lazy loading
        const script = document.createElement('script');
        script.src = '/js/lazy-loading-polyfill.js';
        document.head.appendChild(script);
      }

      // Preload critical resources for next navigation (only existing files)
      const preloadLinks = [
        '/css/payrollhub.css'
      ];

      preloadLinks.forEach(href => {
        // Check if resource exists before prefetching
        fetch(href, { method: 'HEAD' })
          .then(response => {
            if (response.ok) {
              const link = document.createElement('link');
              link.rel = 'prefetch';
              link.href = href;
              document.head.appendChild(link);
            }
          })
          .catch(() => {
            // Silently ignore missing resources
          });
      });
    </script>

    <!-- Include Payment Restriction Script -->
    <%- include('partials/payment-restriction') %>
  </body>
</html>
