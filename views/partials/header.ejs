<header class="main-header">
  <div class="header-left">
    <!-- Mobile Menu Toggle -->
    <button class="mobile-menu-toggle" id="mobileMenuToggle" aria-label="Toggle Navigation">
      <i class="ph ph-list"></i>
    </button>

    <!-- Navigation Tabs -->
    <div class="nav-tabs">
      <a href="<%= typeof company !== 'undefined' && company ? `/clients/${company.companyCode}/dashboard` : '/clients' %>"
         class="nav-tab <%= (typeof currentPage !== 'undefined' && currentPage === 'dashboard') ? 'active' : '' %>">
        <i class="ph ph-squares-four"></i>
        <span>Dashboard</span>
      </a>

      <a href="<%= typeof company !== 'undefined' && company ? `/clients/${company.companyCode}/employeeManagement` : '/clients' %>"
         class="nav-tab <%= (typeof currentPage !== 'undefined' && currentPage === 'employees') ? 'active' : '' %>">
        <i class="ph ph-users-three"></i>
        <span>Talent Management</span>
      </a>

      <a href="<%= typeof company !== 'undefined' && company ? `/clients/${company.companyCode}/payrollhub` : '/clients' %>"
         class="nav-tab <%= (typeof currentPage !== 'undefined' && currentPage === 'payroll') ? 'active' : '' %>">
        <i class="ph ph-wallet"></i>
        <span>Payroll</span>
      </a>

      <a href="<%= typeof company !== 'undefined' && company ? `/clients/${company.companyCode}/filing` : '/clients' %>"
         class="nav-tab <%= (typeof currentPage !== 'undefined' && currentPage === 'filing') ? 'active' : '' %>">
        <i class="ph ph-folder-open"></i>
        <span>Filing</span>
      </a>

      <a href="<%= typeof company !== 'undefined' && company ? `/clients/${company.companyCode}/reporting` : '/clients' %>"
         class="nav-tab <%= (typeof currentPage !== 'undefined' && currentPage === 'reporting') ? 'active' : '' %>">
        <i class="ph ph-chart-bar"></i>
        <span>Reporting</span>
      </a>

      <a href="/companies"
         class="nav-tab <%= (typeof currentPage !== 'undefined' && currentPage === 'companies') ? 'active' : '' %>">
        <i class="ph ph-buildings"></i>
        <span>Companies</span>
      </a>

      <a href="<%= typeof company !== 'undefined' && company ? `/clients/${company.companyCode}/settings` : '/settings' %>"
         class="nav-tab <%= (typeof currentPage !== 'undefined' && currentPage === 'settings') ? 'active' : '' %>">
        <i class="ph ph-gear"></i>
        <span>Settings</span>
      </a>
    </div>
  </div>

  <div class="header-right">
    <!-- Company Switcher -->
    <div class="logo-company-switcher" id="companySwitcher" role="button" tabindex="0" aria-label="Switch Company">
      <i class="ph ph-office-chair"></i>
      <span id="currentCompanyName">
        <%= user.currentCompany ? user.currentCompany.name : 'Orely Studio' %>
      </span>
      <i class="ph ph-caret-down"></i>
    </div>

    <!-- User Menu -->
    <div class="user-menu">
      <div class="user-pic" id="userPic" aria-label="User Menu">
        <i class="ph ph-user-circle"></i>
      </div>
      <div class="sub-menu-wrap" id="subMenu">
        <div class="sub-menu">
          <div class="user-info">
            <h3><%= user.firstName %> <%= user.lastName %></h3>
          </div>
          <a href="/profile" class="sub-menu-link">
            <i class="ph ph-gear"></i>
            <span>Profile Settings</span>
          </a>
          <a href="/billing/preferences" class="sub-menu-link">
            <i class="ph ph-wallet"></i>
            <span>Billing</span>
          </a>
          <a href="/notifications" class="sub-menu-link">
            <i class="ph ph-bell-ringing"></i>
            <span>Notifications</span>
          </a>
          <a href="/admin/manage-users" class="sub-menu-link">
            <i class="ph ph-users-three"></i>
            <span>Manage Users</span>
          </a>
          <a href="/support" class="sub-menu-link">
            <i class="ph ph-question"></i>
            <span>Help & Support</span>
          </a>
          <a href="/logout" class="sub-menu-link">
            <i class="ph ph-sign-out"></i>
            <span>Sign Out</span>
          </a>
        </div>
      </div>
    </div>
  </div>
</header>

<!-- Mobile Navigation Menu - TEMPORARILY HIDDEN -->
<!--
<div class="mobile-nav-menu" id="mobileNavMenu" style="display: none !important;">
  <div class="mobile-nav-content">
    <div class="mobile-nav-header">
      <h3>Navigation</h3>
      <button class="mobile-nav-close" id="mobileNavClose">
        <i class="ph ph-x"></i>
      </button>
    </div>
    <nav class="mobile-nav-links">
      <a href="<%= typeof company !== 'undefined' && company ? `/clients/${company.companyCode}/dashboard` : '/clients' %>"
         class="mobile-nav-link <%= (typeof currentPage !== 'undefined' && currentPage === 'dashboard') ? 'active' : '' %>">
        <i class="ph ph-squares-four"></i>
        <span>Dashboard</span>
      </a>
      <a href="<%= typeof company !== 'undefined' && company ? `/clients/${company.companyCode}/employeeManagement` : '/clients' %>"
         class="mobile-nav-link <%= (typeof currentPage !== 'undefined' && currentPage === 'employees') ? 'active' : '' %>">
        <i class="ph ph-users-three"></i>
        <span>Talent Management</span>
        <span class="nav-badge">3</span>
      </a>
      <a href="<%= typeof company !== 'undefined' && company ? `/clients/${company.companyCode}/payrollhub` : '/clients' %>"
         class="mobile-nav-link <%= (typeof currentPage !== 'undefined' && currentPage === 'payroll') ? 'active' : '' %>">
        <i class="ph ph-wallet"></i>
        <span>Payroll</span>
      </a>
      <a href="<%= typeof company !== 'undefined' && company ? `/clients/${company.companyCode}/filing` : '/clients' %>"
         class="mobile-nav-link <%= (typeof currentPage !== 'undefined' && currentPage === 'filing') ? 'active' : '' %>">
        <i class="ph ph-folder-open"></i>
        <span>Filing</span>
      </a>
      <a href="<%= typeof company !== 'undefined' && company ? `/clients/${company.companyCode}/reporting` : '/clients' %>"
         class="mobile-nav-link <%= (typeof currentPage !== 'undefined' && currentPage === 'reporting') ? 'active' : '' %>">
        <i class="ph ph-chart-bar"></i>
        <span>Reporting</span>
      </a>
      <a href="/companies"
         class="mobile-nav-link <%= (typeof currentPage !== 'undefined' && currentPage === 'companies') ? 'active' : '' %>">
        <i class="ph ph-buildings"></i>
        <span>Companies</span>
      </a>
      <a href="<%= typeof company !== 'undefined' && company ? `/clients/${company.companyCode}/settings` : '/settings' %>"
         class="mobile-nav-link <%= (typeof currentPage !== 'undefined' && currentPage === 'settings') ? 'active' : '' %>">
        <i class="ph ph-gear"></i>
        <span>Settings</span>
      </a>
    </nav>
  </div>
  <div class="mobile-nav-overlay" id="mobileNavOverlay"></div>
</div>
-->

<style>
  /* ... existing styles ... */

  .company-select-btn {
    display: inline-flex;
    align-items: center;
    background-color: #f3f4f6;
    color: #333;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    text-decoration: none;
    font-size: 0.9rem;
    transition: background-color 0.3s ease;
  }

  .company-select-btn:hover {
    background-color: #e5e7eb;
  }

  .company-select-btn i {
    margin-right: 0.5rem;
  }

  /* Ensure Phosphor icons are properly sized and aligned */
  .ph {
    display: inline-block;
    font-size: 1.2em;
    line-height: 1;
    vertical-align: middle;
  }
  
  /* Add a small margin to icons when they're next to text */
  .sub-menu-link .ph,
  .company-select-btn .ph,
  .logo-company-switcher .ph,
  .search-container .ph {
    margin-right: 0.5em;
  }

  /* Specific size for user icon in the header */
  .user-pic .ph {
    font-size: 1.5em;
  }
</style>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    const userPic = document.getElementById("userPic");
    const subMenu = document.getElementById("subMenu");
    let isMenuOpen = false;

    userPic.addEventListener("click", function (e) {
      e.stopPropagation();
      isMenuOpen = !isMenuOpen;
      subMenu.classList.toggle("open", isMenuOpen);
    });

    // Close menu when clicking outside
    document.addEventListener("click", function (e) {
      if (isMenuOpen && !subMenu.contains(e.target)) {
        isMenuOpen = false;
        subMenu.classList.remove("open");
      }
    });

    // Close menu on scroll
    document.addEventListener("scroll", function () {
      if (isMenuOpen) {
        isMenuOpen = false;
        subMenu.classList.remove("open");
      }
    });

    // Mobile Navigation Menu
    const mobileMenuToggle = document.getElementById("mobileMenuToggle");
    const mobileNavMenu = document.getElementById("mobileNavMenu");
    const mobileNavClose = document.getElementById("mobileNavClose");
    const mobileNavOverlay = document.getElementById("mobileNavOverlay");
    let isMobileMenuOpen = false;

    function toggleMobileMenu() {
      isMobileMenuOpen = !isMobileMenuOpen;
      mobileNavMenu.classList.toggle("open", isMobileMenuOpen);
      document.body.classList.toggle("mobile-nav-open", isMobileMenuOpen);
    }

    function closeMobileMenu() {
      isMobileMenuOpen = false;
      mobileNavMenu.classList.remove("open");
      document.body.classList.remove("mobile-nav-open");
    }

    if (mobileMenuToggle) {
      mobileMenuToggle.addEventListener("click", toggleMobileMenu);
    }

    if (mobileNavClose) {
      mobileNavClose.addEventListener("click", closeMobileMenu);
    }

    if (mobileNavOverlay) {
      mobileNavOverlay.addEventListener("click", closeMobileMenu);
    }

    // Close mobile menu on escape key
    document.addEventListener("keydown", function (e) {
      if (e.key === "Escape" && isMobileMenuOpen) {
        closeMobileMenu();
      }
    });

    // Company Switcher functionality
    const companySwitcher = document.getElementById("companySwitcher");
    if (companySwitcher) {
      companySwitcher.addEventListener("click", function(e) {
        e.preventDefault();
        window.location.href = "/companies";
      });

      // Add keyboard support for accessibility
      companySwitcher.addEventListener("keydown", function(e) {
        if (e.key === "Enter" || e.key === " ") {
          e.preventDefault();
          window.location.href = "/companies";
        }
      });
    }
  });
</script>
