<link rel="stylesheet" href="/css/sidebar.css" />

<!-- Add this in your head section -->
<script src="https://unpkg.com/@phosphor-icons/web"></script>

<nav>
  <aside id="sidebar" class="sidebar">
    <!-- Brand/Logo Area -->
    <div class="sidebar-brand">
      <div class="brand-content">
        <div class="brand-icon">
          <i class="ph ph-panda"></i>
        </div>
        <span class="brand-text">PandaPayroll</span>
      </div>
    </div>

    <!-- Toggle button -->
    <button class="toggle-btn">
      <i class="ph ph-caret-left"></i>
    </button>

    <ul>
      <li class="sidebar-item" data-tooltip="Dashboard">
        <div class="icon-container">
          <i class="ph ph-gauge"></i>
        </div>
        <span class="item-text">Dashboard</span>
        <a
          href="<%= typeof company !== 'undefined' && company ? `/clients/${company.companyCode}/dashboard` : '/clients' %>"
        ></a>
      </li>

      <li class="sidebar-item" data-tooltip="Employees">
        <div class="icon-container">
          <i class="ph ph-users"></i>
        </div>
        <span class="item-text">Employees Management</span>
        <a
          href="<%= typeof company !== 'undefined' && company ? `/clients/${company.companyCode}/employeeManagement` : '/clients' %>"
        ></a>
      </li>

      <li class="sidebar-item" data-tooltip="Payroll Hub">
        <div class="icon-container">
          <i class="ph ph-credit-card"></i>
        </div>
        <span class="item-text">Payroll Hub</span>
        <a
          href="<%= typeof company !== 'undefined' && company ? `/clients/${company.companyCode}/payrollhub` : '/clients' %>"
        ></a>
      </li>

      <li class="sidebar-item" data-tooltip="Filing">
        <div class="icon-container">
          <i class="ph ph-file-text"></i>
        </div>
        <span class="item-text">Filing</span>
        <a
          href="<%= typeof company !== 'undefined' && company ? `/clients/${company.companyCode}/filing` : '/clients' %>"
        ></a>
      </li>

      <li class="sidebar-item" data-tooltip="Reporting">
        <div class="icon-container">
          <i class="ph ph-chart-line"></i>
        </div>
        <span class="item-text">Reporting</span>
        <a
          href="<%= typeof company !== 'undefined' && company ? `/clients/${company.companyCode}/reporting` : '/clients' %>"
        ></a>
      </li>

      <div class="section-header">System</div>

      <li class="sidebar-item" data-tooltip="Manage Companies">
        <div class="icon-container">
          <i class="ph ph-building"></i>
        </div>
        <span class="item-text">Manage Companies</span>
        <a href="/companies"></a>
      </li>

      <li class="sidebar-item" data-tooltip="Settings">
        <div class="icon-container">
          <i class="ph ph-gear"></i>
        </div>
        <span class="item-text">Settings</span>
        <a
          href="<%= typeof company !== 'undefined' && company ? `/clients/${company.companyCode}/settings` : '/clients' %>"
        ></a>
      </li>
    </ul>

    <!-- Move this to the bottom of your sidebar, right before closing </aside> tag -->
    <button class="toggle-btn" aria-label="Toggle Sidebar">
      <i class="ph ph-caret-left"></i>
    </button>
  </aside>
</nav>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    // Get all toggle buttons (since there are two)
    const toggleBtns = document.querySelectorAll(".toggle-btn");
    const sidebar = document.querySelector(".sidebar");
    
    // Add click handler to both toggle buttons
    toggleBtns.forEach(btn => {
      btn.addEventListener("click", () => {
        sidebar.classList.toggle("collapsed");
        // Rotate all toggle button icons
        toggleBtns.forEach(toggleBtn => {
          const icon = toggleBtn.querySelector("i");
          if (sidebar.classList.contains("collapsed")) {
            icon.classList.remove("ph-caret-left");
            icon.classList.add("ph-caret-right");
          } else {
            icon.classList.remove("ph-caret-right");
            icon.classList.add("ph-caret-left");
          }
        });
      });
    });

    // Add active state to current page
    const currentPath = window.location.pathname;
    document.querySelectorAll(".sidebar-item").forEach((item) => {
      const link = item.querySelector("a");
      if (link && link.getAttribute("href") === currentPath) {
        item.classList.add("active");
      }
    });

    // Initialize sidebar state from localStorage if it exists
    const isCollapsed = localStorage.getItem("sidebarCollapsed");
    if (isCollapsed === "true") {
      sidebar.classList.add("collapsed");
      toggleBtns.forEach(btn => {
        const icon = btn.querySelector("i");
        icon.classList.remove("ph-caret-left");
        icon.classList.add("ph-caret-right");
      });
    }

    // Save sidebar state to localStorage when it changes
    const observer = new MutationObserver(() => {
      localStorage.setItem("sidebarCollapsed", sidebar.classList.contains("collapsed"));
    });
    observer.observe(sidebar, { attributes: true });
  });
</script>
