<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Reports | <%= company.name %></title>

    <!-- Modern Font -->
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <!-- Phosphor Icons - Fixed CORS issue -->
    <script src="https://unpkg.com/@phosphor-icons/web@2.1.5/dist/phosphor-icons.js" crossorigin="anonymous"></script>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/styles.css" />
    <link rel="stylesheet" href="/css/onboarding.css" />
    <link rel="stylesheet" href="/css/reporting.css" />
    <link rel="stylesheet" href="/css/header.css" />
    <link rel="stylesheet" href="/css/mobile-header.css" />
    <link rel="stylesheet" href="/css/mobile-nav.css" />
    <link rel="stylesheet" href="/css/mobile-reporting.css" />
    <link rel="stylesheet" href="/css/sidebar.css" />

    <meta name="csrf-token" content="<%= csrfToken %>" />
    <meta name="company-code" content="<%= companyCode %>" />

    <!-- Flatpickr -->
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css"
    />
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/flatpickr/dist/themes/material_blue.css"
    />
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

    <!-- Moment.js for date formatting -->
    <script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>

    <style>
      .report-filters {
        margin-top: 20px;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 8px;
      }

      .report-fields-config {
        margin-top: 20px;
      }

      .report-fields-config h4 {
        margin-bottom: 15px;
        color: #333;
        font-size: 1.1em;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .report-fields-config h4 i {
        color: #666;
        font-size: 1.2em;
      }

      .field-groups-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 15px;
        margin-top: 15px;
      }

      .field-group {
        margin-bottom: 0;
        padding: 15px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        border: 1px solid #eee;
        transition: all 0.2s ease;
      }

      .field-group:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        border-color: #e0e0e0;
      }

      .field-group h5 {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        color: #444;
        font-size: 0.95em;
        padding-bottom: 8px;
        border-bottom: 1px solid #eee;
      }

      .field-group h5 i {
        color: #666;
        margin-right: 6px;
      }

      .checkbox-grid {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .checkbox-wrapper {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 6px 8px;
        border-radius: 4px;
        transition: background-color 0.2s;
        font-size: 0.9em;
        color: #555;
      }

      .checkbox-wrapper:hover {
        background-color: #f5f5f5;
      }

      .checkbox-wrapper input[type="checkbox"] {
        width: 16px;
        height: 16px;
        margin: 0;
        border-radius: 3px;
        border: 2px solid #ddd;
        transition: all 0.2s;
      }

      .checkbox-wrapper input[type="checkbox"]:checked {
        background-color: #4a90e2;
        border-color: #4a90e2;
      }

      .select-all-wrapper {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 0.85em;
        color: #666;
        padding: 4px 8px;
        background: #f8f9fa;
        border-radius: 4px;
        cursor: pointer;
      }

      .select-all-wrapper:hover {
        background: #f0f0f0;
      }

      .employee-selection {
        margin-bottom: 20px;
        padding: 20px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
      }

      .employee-selection-header {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 15px;
      }

      .employee-selection-header i {
        font-size: 1.2em;
        color: #4a90e2;
      }

      .employee-selection-header label {
        font-size: 1.1em;
        font-weight: 500;
        color: #333;
        margin: 0;
      }

      .employee-search {
        position: relative;
        margin-bottom: 15px;
      }

      .employee-search input {
        width: 100%;
        padding: 10px 12px 10px 35px;
        border: 1px solid #ddd;
        border-radius: 6px;
        font-size: 0.95em;
      }

      .employee-search i {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #666;
      }

      .employee-list-wrapper {
        border: 1px solid #eee;
        border-radius: 6px;
        max-height: 250px;
        overflow-y: auto;
      }

      .employee-list {
        list-style: none;
        margin: 0;
        padding: 0;
      }

      .employee-list li {
        padding: 8px 12px;
        border-bottom: 1px solid #eee;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .employee-list li:last-child {
        border-bottom: none;
      }

      .employee-list li:hover {
        background-color: #f8f9fa;
      }

      .employee-list .checkbox-wrapper {
        margin: 0;
        flex: 1;
      }

      .employee-list .employee-info {
        display: flex;
        flex-direction: column;
        gap: 2px;
      }

      .employee-list .employee-name {
        font-weight: 500;
        color: #333;
      }

      .employee-list .employee-number {
        font-size: 0.85em;
        color: #666;
      }

      .employee-selection-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;
        padding-top: 10px;
        border-top: 1px solid #eee;
      }

      .select-all-employees {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 0.9em;
        color: #555;
        padding: 6px 12px;
        background: #f8f9fa;
        border: 1px solid #ddd;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s;
      }

      .select-all-employees:hover {
        background: #f0f0f0;
      }

      .selected-count {
        font-size: 0.9em;
        color: #666;
      }

      /* Quick Select Buttons */
      .quick-select-buttons {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
      }

      .quick-select-btn {
        padding: 6px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background: white;
        color: #555;
        font-size: 0.9em;
        cursor: pointer;
        transition: all 0.2s;
      }

      .quick-select-btn:hover {
        background: #f8f9fa;
        border-color: #ccc;
      }

      .quick-select-btn.active {
        background: #4a90e2;
        color: white;
        border-color: #4a90e2;
      }

      /* Modern Field Selection Styles - PandaPayroll Design System */
      .modern-field-selection {
        background: #ffffff;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 28px;
        margin: 24px 0;
        box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
        width: 100%;
        max-width: 100%;
        overflow: hidden;
        position: relative;
      }

      /* Ensure proper container sizing */
      .report-fields-config {
        width: 100%;
        box-sizing: border-box;
      }

      /* Direct CSS override approach for full width field selection */
      #employeeBasicInfoFilters {
        grid-column: 1 / -1; /* Span all columns in form grid */
        width: 100% !important;
        max-width: none !important;
        margin: 2rem 0 !important;
      }

      /* Override all parent container width constraints */
      #employeeBasicInfoFilters .modern-field-selection {
        width: 100% !important;
        max-width: none !important;
        margin: 0 !important;
        position: relative !important;
        left: 0 !important;
        right: 0 !important;
      }

      /* Ensure the field groups container uses full available width */
      #employeeBasicInfoFilters .modern-field-groups {
        width: 100% !important;
        max-width: none !important;
      }

      /* Make sure parent containers don't constrain width */
      .report-generator {
        overflow: visible !important;
      }

      .generator-content {
        overflow: visible !important;
      }

      .report-form {
        overflow: visible !important;
      }

      .form-grid {
        overflow: visible !important;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
      }

      /* Prevent z-index stacking issues */
      .modern-field-group {
        position: relative;
        z-index: 1;
      }

      .field-group-header {
        z-index: 2;
        position: relative;
      }

      .modern-select-all {
        z-index: 3;
        position: relative;
      }

      /* Ensure proper text rendering */
      .group-title,
      .group-title h5,
      .field-count {
        z-index: 2;
        position: relative;
      }



      .field-selection-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 28px;
        padding-bottom: 24px;
        border-bottom: 1px solid #e2e8f0;
        flex-wrap: wrap;
        gap: 16px;
      }

      .header-content {
        display: flex;
        align-items: center;
        gap: 16px;
        flex: 1;
        min-width: 0; /* Prevents text overflow */
      }

      .header-icon {
        font-size: 28px;
        color: #6366f1;
        flex-shrink: 0;
      }

      .header-text {
        flex: 1;
        min-width: 0; /* Prevents text overflow */
      }

      .header-text h4 {
        font-family: 'Inter', sans-serif;
        font-size: 20px;
        font-weight: 600;
        color: #1e293b;
        margin: 0 0 6px 0;
        line-height: 1.3;
        word-wrap: break-word;
        overflow-wrap: break-word;
      }

      .header-subtitle {
        font-family: 'Inter', sans-serif;
        font-size: 15px;
        color: #64748b;
        margin: 0;
        line-height: 1.4;
        word-wrap: break-word;
        overflow-wrap: break-word;
      }

      .field-counter {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        padding: 12px 20px;
        border-radius: 10px;
        font-family: 'Inter', sans-serif;
        font-size: 15px;
        font-weight: 500;
        color: #475569;
        border: 1px solid #e2e8f0;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        white-space: nowrap;
        flex-shrink: 0;
      }

      .field-counter span {
        color: #6366f1;
        font-weight: 700;
        font-size: 16px;
      }

      .quick-select-section {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 32px;
      }

      .quick-select-label {
        display: flex;
        align-items: center;
        gap: 10px;
        font-family: 'Inter', sans-serif;
        font-size: 16px;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 16px;
      }

      .quick-select-label i {
        color: #6366f1;
        font-size: 20px;
      }

      .modern-field-selection .quick-select-buttons {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 16px;
        width: 100%;
      }

      .modern-field-selection .quick-select-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 8px;
        padding: 20px 16px;
        background: #ffffff;
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        font-family: 'Inter', sans-serif;
        cursor: pointer;
        transition: all 0.3s ease;
        min-height: 100px;
        position: relative;
        overflow: hidden;
      }

      .modern-field-selection .quick-select-btn:hover {
        border-color: #6366f1;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(99, 102, 241, 0.15);
      }

      .modern-field-selection .quick-select-btn.active {
        border-color: #6366f1;
        background: linear-gradient(135deg, #6366f1 0%, #818cf8 100%);
        color: #ffffff;
        transform: translateY(-1px);
        box-shadow: 0 6px 20px rgba(99, 102, 241, 0.25);
      }

      .modern-field-selection .quick-select-btn.active::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
        pointer-events: none;
      }

      .modern-field-selection .quick-select-btn i {
        font-size: 24px;
        color: #6366f1;
        transition: all 0.3s ease;
      }

      .modern-field-selection .quick-select-btn.active i {
        color: #ffffff;
        transform: scale(1.1);
      }

      .modern-field-selection .quick-select-btn span {
        font-size: 15px;
        font-weight: 600;
        color: #1e293b;
        text-align: center;
        transition: all 0.3s ease;
      }

      .modern-field-selection .quick-select-btn.active span {
        color: #ffffff;
      }

      .modern-field-selection .quick-select-btn small {
        font-size: 13px;
        color: #64748b;
        font-weight: 500;
        text-align: center;
        transition: all 0.3s ease;
      }

      .modern-field-selection .quick-select-btn.active small {
        color: rgba(255, 255, 255, 0.9);
      }

      .modern-field-groups {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 20px;
        margin-top: 0;
        width: 100%;
      }

      /* Enhanced responsive grid for optimal space utilization */
      @media (min-width: 1400px) {
        .modern-field-groups {
          grid-template-columns: repeat(4, 1fr);
          gap: 24px;
        }
      }

      @media (min-width: 1200px) and (max-width: 1399px) {
        .modern-field-groups {
          grid-template-columns: repeat(3, 1fr);
          gap: 22px;
        }
      }

      @media (min-width: 768px) and (max-width: 1199px) {
        .modern-field-groups {
          grid-template-columns: repeat(2, 1fr);
          gap: 20px;
        }
      }

      @media (max-width: 767px) {
        .modern-field-groups {
          grid-template-columns: 1fr;
          gap: 16px;
        }
      }

      .modern-field-group {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 20px;
        transition: all 0.3s ease;
        min-height: 220px;
        display: flex;
        flex-direction: column;
        position: relative;
        overflow: hidden;
      }

      .modern-field-group::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #6366f1 0%, #818cf8 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .modern-field-group:hover {
        border-color: #6366f1;
        box-shadow: 0 4px 20px rgba(99, 102, 241, 0.08);
        transform: translateY(-2px);
      }

      .modern-field-group:hover::before {
        opacity: 1;
      }

      .field-group-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 18px;
        padding-bottom: 16px;
        border-bottom: 1px solid #e2e8f0;
        position: relative;
        min-height: 48px; /* Ensure consistent header height */
        gap: 16px; /* Add gap between title and select all */
      }

      .group-title {
        display: flex;
        align-items: center;
        gap: 12px;
        flex: 1;
        min-width: 0;
        overflow: hidden; /* Prevent text overflow */
      }

      .group-icon {
        font-size: 20px;
        color: #6366f1;
        flex-shrink: 0;
      }

      .group-title h5 {
        font-family: 'Inter', sans-serif;
        font-size: 16px;
        font-weight: 600;
        color: #1e293b;
        margin: 0;
        line-height: 1.3;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
        min-width: 0;
      }

      .field-count {
        background: linear-gradient(135deg, #6366f1 0%, #818cf8 100%);
        color: #ffffff;
        font-size: 11px;
        font-weight: 600;
        padding: 6px 12px;
        border-radius: 12px;
        font-family: 'Inter', sans-serif;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-left: 8px;
        flex-shrink: 0;
        white-space: nowrap;
        height: auto;
        line-height: 1;
        display: inline-flex;
        align-items: center;
      }

      .modern-select-all {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        padding: 10px 16px;
        border-radius: 8px;
        transition: all 0.2s ease;
        background: #ffffff;
        border: 1px solid #e2e8f0;
        flex-shrink: 0;
        position: relative;
        z-index: 1;
        height: auto;
        min-height: 36px;
        box-sizing: border-box;
      }

      .modern-select-all:hover {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border-color: #6366f1;
        box-shadow: 0 2px 8px rgba(99, 102, 241, 0.1);
        /* Removed transform to prevent positioning issues */
      }

      .modern-select-all input[type="checkbox"] {
        margin: 0;
        width: 16px;
        height: 16px;
        accent-color: #6366f1;
      }

      .select-all-text {
        font-family: 'Inter', sans-serif;
        font-size: 13px;
        font-weight: 500;
        color: #475569;
        white-space: nowrap;
      }

      .select-all-icon {
        font-size: 14px;
        color: #6366f1;
      }

      .modern-checkbox-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(170px, 1fr));
        gap: 8px;
        flex: 1;
        align-content: start;
      }

      .modern-field-group .checkbox-wrapper {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 12px 14px;
        background: #ffffff;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-family: 'Inter', sans-serif;
        font-size: 14px;
        color: #475569;
        position: relative;
        overflow: hidden;
      }

      .modern-field-group .checkbox-wrapper::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 3px;
        background: #6366f1;
        transform: scaleY(0);
        transition: transform 0.3s ease;
      }

      .modern-field-group .checkbox-wrapper:hover {
        border-color: #6366f1;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(99, 102, 241, 0.12);
      }

      .modern-field-group .checkbox-wrapper:hover::before {
        transform: scaleY(1);
      }

      .modern-field-group .checkbox-wrapper input[type="checkbox"] {
        margin: 0;
        width: 16px;
        height: 16px;
        accent-color: #6366f1;
      }

      .modern-field-group .checkbox-wrapper input[type="checkbox"]:checked + span {
        color: #1e293b;
        font-weight: 600;
      }

      .modern-field-group .checkbox-wrapper input[type="checkbox"]:checked {
        transform: scale(1.1);
      }

      /* Enhanced Responsive Design */
      @media (max-width: 768px) {
        .modern-field-selection {
          padding: 20px;
          margin: 16px 0;
        }



        .field-selection-header {
          flex-direction: column;
          gap: 16px;
          align-items: stretch;
          margin-bottom: 24px;
          padding-bottom: 20px;
        }

        .header-content {
          gap: 12px;
        }

        .header-text h4 {
          font-size: 18px;
        }

        .header-subtitle {
          font-size: 14px;
        }

        .field-counter {
          align-self: flex-start;
          padding: 10px 16px;
          font-size: 14px;
        }

        .quick-select-section {
          padding: 16px;
          margin-bottom: 24px;
        }

        .modern-field-selection .quick-select-buttons {
          grid-template-columns: 1fr;
          gap: 12px;
        }

        .modern-field-selection .quick-select-btn {
          min-height: 80px;
          flex-direction: row;
          justify-content: space-between;
          padding: 16px 20px;
        }

        .modern-field-selection .quick-select-btn i {
          font-size: 20px;
        }

        .modern-checkbox-grid {
          grid-template-columns: 1fr;
          gap: 10px;
        }

        .modern-field-group {
          padding: 16px;
          min-height: auto;
        }
      }

      /* Medium screens - adjust header layout */
      @media (max-width: 1024px) and (min-width: 769px) {
        .field-group-header {
          gap: 12px;
        }

        .group-title h5 {
          font-size: 15px;
        }

        .modern-select-all {
          padding: 8px 12px;
          font-size: 12px;
        }

        .field-count {
          font-size: 10px;
          padding: 4px 8px;
        }
      }

      @media (max-width: 480px) {
        .modern-field-selection {
          padding: 16px;
        }

        .header-text h4 {
          font-size: 16px;
        }

        .quick-select-section {
          padding: 12px;
        }

        .modern-field-group {
          padding: 12px;
        }

        .field-group-header {
          margin-bottom: 12px;
          padding-bottom: 12px;
          flex-direction: column;
          align-items: stretch;
          gap: 12px;
          min-height: auto;
        }

        .group-title {
          justify-content: space-between;
        }

        .group-title h5 {
          white-space: normal;
          text-overflow: initial;
          overflow: visible;
          font-size: 14px;
        }

        .modern-select-all {
          align-self: flex-end;
          padding: 8px 12px;
          min-height: 32px;
        }

        .field-count {
          font-size: 9px;
          padding: 3px 6px;
        }
      }
    </style>
  </head>
  <body>
    <%- include('partials/header', { user: user, company: company, currentPage: 'reporting' }) %>
    <nav><%- include('partials/sidebar') %></nav>

    <main class="main-container">


      <!-- Report Generator Section -->
      <div class="report-generator">
        <div class="generator-header">
          <div class="header-content">
            <h3><i class="ph ph-gear"></i> Report Generator</h3>
          </div>
          <button id="openSettingsBtn" class="action-button secondary">
            <i class="ph ph-sliders"></i>
            Report Preferences
          </button>
        </div>
        <div class="generator-content">
          <form id="reportForm" class="report-form">
            <div class="form-grid">
              <!-- Report Type -->
              <div class="form-group">
                <label for="reportType">Report Type</label>
                <select id="reportType" name="reportType" required>
                  <option value="">Select report type</option>

                  <!-- General Reports -->
                  <optgroup label="General Reports">
                    <option value="employeeBasicInfo">Employee Basic Info</option>
                    <option value="employmentTaxIncentive">Employment Tax Incentive</option>
                    <option value="transactionHistory">Transaction History Report</option>
                    <option value="payrollVariance">Variance Report</option>
                  </optgroup>

                  <!-- Leave Reports -->
                  <optgroup label="Leave Reports">
                    <option value="leaveDaysReport">Leave Days Report</option>
                    <option value="leaveExpiryReport">Leave Expiry Report</option>
                    <option value="leaveReport">Leave Report</option>
                  </optgroup>

                  <!-- Financial Reports -->
                  <optgroup label="Financial Reports">
                    <option value="balanceReport">Balance's - Loans, Savings, Garnishee</option>
                    <option value="leaveLiabilities">Leave Liabilities</option>
                  </optgroup>

                  <!-- Bulk Downloads -->
                  <optgroup label="Bulk Downloads">
                    <option value="payslips">Payslips</option>
                    <option value="bulkDocuments">Termination Certificates, Salary Schedules</option>
                  </optgroup>
                </select>
              </div>

              <!-- Date Range -->
              <div class="form-group">
                <label for="dateRange">Date Range</label>
                <select id="dateRange" name="dateRange" class="form-select">
                  <!-- Payroll Reports Options -->
                  <optgroup label="Standard Ranges" class="payroll-ranges">
                    <option value="currentMonth">Current Month</option>
                    <option value="previousMonth">Previous Month</option>
                    <option value="currentYear">Current Year</option>
                    <option value="previousYear">Previous Year</option>
                    <option value="monthToMonth">
                      Month-to-Month Comparison
                    </option>
                  </optgroup>

                  <!-- Employee Reports Options -->
                  <optgroup
                    label="Employee Report Ranges"
                    class="employee-ranges"
                  >
                    <option value="allTime">All Time</option>
                    <option value="currentYear">Current Year</option>
                    <option value="previousYear">Previous Year</option>
                  </optgroup>

                  <!-- Statutory Reports Options -->
                  <optgroup label="Statutory Ranges" class="statutory-ranges">
                    <option value="currentMonth">Current Month</option>
                    <option value="previousMonth">Previous Month</option>
                    <option value="currentTaxYear">Current Tax Year</option>
                  </optgroup>

                  <!-- Custom Range Option -->
                  <option value="custom">Custom Range</option>
                </select>
              </div>

              <!-- Custom Date Range (hidden by default) -->
              <div class="form-group custom-range" style="display: none">
                <div class="date-inputs">
                  <div class="date-field">
                    <label for="startDate">Start Date</label>
                    <div class="date-input-wrapper">
                      <input
                        type="text"
                        id="startDate"
                        name="startDate"
                        placeholder="Select date"
                      />
                      <i class="ph ph-calendar"></i>
                    </div>
                  </div>
                  <div class="date-field">
                    <label for="endDate">End Date</label>
                    <div class="date-input-wrapper">
                      <input
                        type="text"
                        id="endDate"
                        name="endDate"
                        placeholder="Select date"
                      />
                      <i class="ph ph-calendar"></i>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Format -->
              <div class="form-group">
                <label for="format">Format</label>
                <select id="format" name="format" required>
                  <option value="pdf">PDF</option>
                  <option value="excel">Excel</option>
                  <option value="csv">CSV</option>
                </select>
              </div>

              <!-- Employee Selection for Employee Basic Info Report -->
              <div id="employeeBasicInfoFilters" class="report-filters" style="display: none;">
                <!-- Enhanced Employee Selection -->
                <div class="employee-selection">
                  <div class="employee-selection-header">
                    <i class="ph ph-users"></i>
                    <label for="employeeSearch">Select Employees</label>
                  </div>

                  <!-- Search Box -->
                  <div class="employee-search">
                    <i class="ph ph-magnifying-glass"></i>
                    <input
                      type="text"
                      id="employeeSearch"
                      placeholder="Search employees by name or number..."
                    >
                  </div>

                  <!-- Employee List -->
                  <div class="employee-list-wrapper">
                    <ul class="employee-list">
                      <% if (employees && employees.length > 0) { %>
                        <% employees.forEach(function(employee) { %>
                          <li>
                            <label class="checkbox-wrapper">
                              <input
                                type="checkbox"
                                name="selectedEmployees"
                                value="<%= employee._id %>"
                                class="employee-checkbox"
                              >
                              <div class="employee-info">
                                <span class="employee-name">
                                  <%= employee.firstName %> <%= employee.lastName %>
                                </span>
                                <span class="employee-number">
                                  <%= employee.companyEmployeeNumber %>
                                </span>
                              </div>
                            </label>
                          </li>
                        <% }); %>
                      <% } else { %>
                        <li class="no-employees">No employees found</li>
                      <% } %>
                    </ul>
                  </div>

                  <!-- Selection Actions -->
                  <div class="employee-selection-actions">
                    <button type="button" class="select-all-employees">
                      <i class="ph ph-check-square"></i>
                      <span>Select All</span>
                    </button>
                    <span class="selected-count">0 employees selected</span>
                  </div>
                </div>

                <!-- Report Fields Configuration -->
                <div class="report-fields-config modern-field-selection">
                  <div class="field-selection-header">
                    <div class="header-content">
                      <i class="ph ph-list-checks header-icon"></i>
                      <div class="header-text">
                        <h4>Select Information to Include</h4>
                        <p class="header-subtitle">Choose which employee fields to include in your report</p>
                      </div>
                    </div>
                    <div class="field-counter">
                      <span id="selectedFieldCount">3</span> of <span id="totalFieldCount">71</span> fields selected
                    </div>
                  </div>

                  <!-- Quick Select Buttons -->
                  <div class="quick-select-section">
                    <div class="quick-select-label">
                      <i class="ph ph-lightning"></i>
                      Quick Select
                    </div>
                    <div class="quick-select-buttons">
                      <button type="button" class="quick-select-btn active" data-preset="essential">
                        <i class="ph ph-star"></i>
                        <span>Essential Info</span>
                        <small>7 fields</small>
                      </button>
                      <button type="button" class="quick-select-btn" data-preset="detailed">
                        <i class="ph ph-list-bullets"></i>
                        <span>Detailed Info</span>
                        <small>23 fields</small>
                      </button>
                      <button type="button" class="quick-select-btn" data-preset="complete">
                        <i class="ph ph-squares-four"></i>
                        <span>Complete Info</span>
                        <small>All 71 fields</small>
                      </button>
                    </div>
                  </div>

                  <!-- Field Groups Container -->
                  <div class="field-groups-container modern-field-groups">
                    <!-- Personal Information -->
                    <div class="field-group modern-field-group">
                      <div class="field-group-header">
                        <div class="group-title">
                          <i class="ph ph-user group-icon"></i>
                          <h5>Personal Information</h5>
                          <span class="field-count">6 fields</span>
                        </div>
                        <label class="select-all-wrapper modern-select-all">
                          <input type="checkbox" class="select-all">
                          <span class="select-all-text">Select All</span>
                          <i class="ph ph-check select-all-icon"></i>
                        </label>
                      </div>
                      <div class="checkbox-grid modern-checkbox-grid">
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="firstName" checked>
                          First Names
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="lastName" checked>
                          Last Name
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="dob">
                          Date of Birth
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="email">
                          Email Address
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="phone">
                          Phone Number
                        </label>
                      </div>
                    </div>

                    <!-- Employment Details -->
                    <div class="field-group modern-field-group">
                      <div class="field-group-header">
                        <div class="group-title">
                          <i class="ph ph-briefcase group-icon"></i>
                          <h5>Employment Details</h5>
                          <span class="field-count">7 fields</span>
                        </div>
                        <label class="select-all-wrapper modern-select-all">
                          <input type="checkbox" class="select-all">
                          <span class="select-all-text">Select All</span>
                          <i class="ph ph-check select-all-icon"></i>
                        </label>
                      </div>
                      <div class="checkbox-grid modern-checkbox-grid">
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="companyEmployeeNumber" checked>
                          Employee Number
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="jobTitle">
                          Job Title
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="department">
                          Department
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="costCentre">
                          Cost Centre
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="workingHours">
                          Working Hours
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="payFrequency">
                          Pay Frequency
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="doa">
                          Date of Appointment
                        </label>
                      </div>
                    </div>

                    <!-- Identification -->
                    <div class="field-group modern-field-group">
                      <div class="field-group-header">
                        <div class="group-title">
                          <i class="ph ph-identification-card group-icon"></i>
                          <h5>Identification</h5>
                          <span class="field-count">4 fields</span>
                        </div>
                        <label class="select-all-wrapper modern-select-all">
                          <input type="checkbox" class="select-all">
                          <span class="select-all-text">Select All</span>
                          <i class="ph ph-check select-all-icon"></i>
                        </label>
                      </div>
                      <div class="checkbox-grid modern-checkbox-grid">
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="idType">
                          ID Type
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="idNumber">
                          ID Number
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="passportNumber">
                          Passport Number
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="incomeTaxNumber">
                          Income Tax Number
                        </label>
                      </div>
                    </div>

                    <!-- Banking Details -->
                    <div class="field-group modern-field-group">
                      <div class="field-group-header">
                        <div class="group-title">
                          <i class="ph ph-bank group-icon"></i>
                          <h5>Banking Details</h5>
                          <span class="field-count">7 fields</span>
                        </div>
                        <label class="select-all-wrapper modern-select-all">
                          <input type="checkbox" class="select-all">
                          <span class="select-all-text">Select All</span>
                          <i class="ph ph-check select-all-icon"></i>
                        </label>
                      </div>
                      <div class="checkbox-grid modern-checkbox-grid">
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="paymentMethod">
                          Payment Method
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="bankName">
                          Bank Name
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="accountType">
                          Account Type
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="accountNumber">
                          Account Number
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="branchCode">
                          Branch Code
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="bank">
                          Bank Name (Alt)
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="accountHolder">
                          Account Holder
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="holderRelationship">
                          Holder Relationship
                        </label>
                      </div>
                    </div>

                    <!-- Address Information -->
                    <div class="field-group modern-field-group">
                      <div class="field-group-header">
                        <div class="group-title">
                          <i class="ph ph-map-pin group-icon"></i>
                          <h5>Address Information</h5>
                          <span class="field-count">13 fields</span>
                        </div>
                        <label class="select-all-wrapper modern-select-all">
                          <input type="checkbox" class="select-all">
                          <span class="select-all-text">Select All</span>
                          <i class="ph ph-check select-all-icon"></i>
                        </label>
                      </div>
                      <div class="checkbox-grid modern-checkbox-grid">
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="streetAddress">
                          Street Address
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="suburb">
                          Suburb
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="city">
                          City
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="postalCode">
                          Postal Code
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="province">
                          Province
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="unitNumber">
                          Unit Number
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="complex">
                          Complex
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="streetNumber">
                          Street Number
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="street">
                          Street
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="code">
                          Postal Code (Alt)
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="line1">
                          Address Line 1
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="line2">
                          Address Line 2
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="line3">
                          Address Line 3
                        </label>
                      </div>
                    </div>

                    <!-- Employment Tax Incentive (ETI) -->
                    <div class="field-group modern-field-group">
                      <div class="field-group-header">
                        <div class="group-title">
                          <i class="ph ph-chart-line-up group-icon"></i>
                          <h5>Employment Tax Incentive</h5>
                          <span class="field-count">6 fields</span>
                        </div>
                        <label class="select-all-wrapper modern-select-all">
                          <input type="checkbox" class="select-all">
                          <span class="select-all-text">Select All</span>
                          <i class="ph ph-check select-all-icon"></i>
                        </label>
                      </div>
                      <div class="checkbox-grid modern-checkbox-grid">
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="etiStatus">
                          ETI Status
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="etiEffectiveFrom">
                          ETI Effective From
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="isETIEligible">
                          ETI Eligible
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="monthlyRemuneration">
                          Monthly Remuneration
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="ageAtEmployment">
                          Age at Employment
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="employmentStartDate">
                          Employment Start Date
                        </label>
                      </div>
                    </div>

                    <!-- Occupation Information -->
                    <div class="field-group modern-field-group">
                      <div class="field-group-header">
                        <div class="group-title">
                          <i class="ph ph-briefcase-metal group-icon"></i>
                          <h5>Occupation Information</h5>
                          <span class="field-count">3 fields</span>
                        </div>
                        <label class="select-all-wrapper modern-select-all">
                          <input type="checkbox" class="select-all">
                          <span class="select-all-text">Select All</span>
                          <i class="ph ph-check select-all-icon"></i>
                        </label>
                      </div>
                      <div class="checkbox-grid modern-checkbox-grid">
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="occupationLevel">
                          Occupation Level
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="occupationCategory">
                          Occupation Category
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="jobValue">
                          Job Value
                        </label>
                      </div>
                    </div>

                    <!-- UIF Information -->
                    <div class="field-group modern-field-group">
                      <div class="field-group-header">
                        <div class="group-title">
                          <i class="ph ph-shield-check group-icon"></i>
                          <h5>UIF Information</h5>
                          <span class="field-count">3 fields</span>
                        </div>
                        <label class="select-all-wrapper modern-select-all">
                          <input type="checkbox" class="select-all">
                          <span class="select-all-text">Select All</span>
                          <i class="ph ph-check select-all-icon"></i>
                        </label>
                      </div>
                      <div class="checkbox-grid modern-checkbox-grid">
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="isUifExempt">
                          UIF Exempt
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="uifExemptReason">
                          UIF Exempt Reason
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="uifStatusCode">
                          UIF Status Code
                        </label>
                      </div>
                    </div>

                    <!-- Additional Employment Details -->
                    <div class="field-group modern-field-group">
                      <div class="field-group-header">
                        <div class="group-title">
                          <i class="ph ph-clock group-icon"></i>
                          <h5>Additional Employment Details</h5>
                          <span class="field-count">7 fields</span>
                        </div>
                        <label class="select-all-wrapper modern-select-all">
                          <input type="checkbox" class="select-all">
                          <span class="select-all-text">Select All</span>
                          <i class="ph ph-check select-all-icon"></i>
                        </label>
                      </div>
                      <div class="checkbox-grid modern-checkbox-grid">
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="globalEmployeeId">
                          Global Employee ID
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="status">
                          Employment Status
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="employmentStatus">
                          Active Status
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="isDirector">
                          Is Director
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="typeOfDirector">
                          Director Type
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="isContractor">
                          Is Contractor
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="hoursPerDay">
                          Hours Per Day
                        </label>
                      </div>
                    </div>

                    <!-- Personal Demographics -->
                    <div class="field-group modern-field-group">
                      <div class="field-group-header">
                        <div class="group-title">
                          <i class="ph ph-users-three group-icon"></i>
                          <h5>Personal Demographics</h5>
                          <span class="field-count">6 fields</span>
                        </div>
                        <label class="select-all-wrapper modern-select-all">
                          <input type="checkbox" class="select-all">
                          <span class="select-all-text">Select All</span>
                          <i class="ph ph-check select-all-icon"></i>
                        </label>
                      </div>
                      <div class="checkbox-grid modern-checkbox-grid">
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="gender">
                          Gender
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="race">
                          Race
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="disabled">
                          Disabled
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="foreignNational">
                          Foreign National
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="notRSACitizen">
                          Not RSA Citizen
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="mobileNumber">
                          Mobile Number
                        </label>
                      </div>
                    </div>

                    <!-- Termination Information -->
                    <div class="field-group modern-field-group">
                      <div class="field-group-header">
                        <div class="group-title">
                          <i class="ph ph-sign-out group-icon"></i>
                          <h5>Termination Information</h5>
                          <span class="field-count">5 fields</span>
                        </div>
                        <label class="select-all-wrapper modern-select-all">
                          <input type="checkbox" class="select-all">
                          <span class="select-all-text">Select All</span>
                          <i class="ph ph-check select-all-icon"></i>
                        </label>
                      </div>
                      <div class="checkbox-grid modern-checkbox-grid">
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="terminationNoticeDate">
                          Termination Notice Date
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="lastDayOfService">
                          Last Day of Service
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="rehireEligibility">
                          Rehire Eligible
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="exitInterviewDate">
                          Exit Interview Date
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="lastPayrollDate">
                          Last Payroll Date
                        </label>
                      </div>
                    </div>

                    <!-- OID Information -->
                    <div class="field-group modern-field-group">
                      <div class="field-group-header">
                        <div class="group-title">
                          <i class="ph ph-first-aid-kit group-icon"></i>
                          <h5>OID Information</h5>
                          <span class="field-count">2 fields</span>
                        </div>
                        <label class="select-all-wrapper modern-select-all">
                          <input type="checkbox" class="select-all">
                          <span class="select-all-text">Select All</span>
                          <i class="ph ph-check select-all-icon"></i>
                        </label>
                      </div>
                      <div class="checkbox-grid modern-checkbox-grid">
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="oidEligible">
                          OID Eligible
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="oidExemptReason">
                          OID Exempt Reason
                        </label>
                      </div>
                    </div>

                    <!-- Self Service -->
                    <div class="field-group modern-field-group">
                      <div class="field-group-header">
                        <div class="group-title">
                          <i class="ph ph-user-gear group-icon"></i>
                          <h5>Self Service</h5>
                          <span class="field-count">3 fields</span>
                        </div>
                        <label class="select-all-wrapper modern-select-all">
                          <input type="checkbox" class="select-all">
                          <span class="select-all-text">Select All</span>
                          <i class="ph ph-check select-all-icon"></i>
                        </label>
                      </div>
                      <div class="checkbox-grid modern-checkbox-grid">
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="selfServiceEnabled">
                          Self Service Enabled
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="lastActivity">
                          Last Activity
                        </label>
                        <label class="checkbox-wrapper">
                          <input type="checkbox" name="fields" value="essEmailSent">
                          ESS Email Sent
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="form-actions">
              <button type="submit" class="action-button primary">
                <i class="ph ph-file-arrow-down"></i>
                Generate Report
              </button>
            </div>
          </form>
        </div>
      </div>



      <!-- Recent Reports Section -->
      <div class="recent-reports">
        <div class="section-header">
          <h3><i class="ph ph-clock-counter-clockwise"></i> Recent Reports</h3>
        </div>
        <div class="reports-list">
          <% if (recentReports && recentReports.length > 0) { %>
          <div class="reports-grid">
            <% recentReports.forEach(report => { %>
            <div class="report-card">
              <div class="report-icon">
                <% if (report.format === 'pdf') { %>
                <i class="ph ph-file-pdf"></i>
                <% } else if (report.format === 'excel') { %>
                <i class="ph ph-file-xls"></i>
                <% } else { %>
                <i class="ph ph-file-csv"></i>
                <% } %>
              </div>
              <div class="report-info">
                <h4><%= report.type.replace(/([A-Z])/g, ' $1').trim() %></h4>
                <p class="report-meta">
                  <span class="format"><%= report.format.toUpperCase() %></span>
                  <span class="date"
                    ><%= moment(report.createdAt).fromNow() %></span
                  >
                </p>
                <p class="generated-by">
                  Generated by <%= report.generatedBy.firstName %> <%=
                  report.generatedBy.lastName %>
                </p>
              </div>
              <div class="report-actions">
                <button
                  onclick="downloadReport('<%= report._id %>')"
                  class="action-button secondary small"
                >
                  <i class="ph ph-download"></i>
                  Download
                </button>
              </div>
            </div>
            <% }); %>
          </div>
          <% } else { %>
          <div class="empty-state">
            <i class="ph ph-file-text"></i>
            <p>No recent reports</p>
          </div>
          <% } %>
        </div>
      </div>
     
      <script>
      document.addEventListener('DOMContentLoaded', () => {
        const enableCheckbox = document.getElementById('enableEmployeeReportAutomation');
        const automationDetails = document.getElementById('employeeReportAutomationDetails');
        const saveButton = document.getElementById('saveEmployeeReportSettings');

        // Toggle visibility of automation details - with null checks
        if (enableCheckbox && automationDetails) {
          enableCheckbox.addEventListener('change', () => {
            automationDetails.style.display =
              enableCheckbox.checked ? 'block' : 'none';
          });
        }

        // Save settings - with null check
        if (saveButton) {
          saveButton.addEventListener('click', async () => {
          const settings = {
            enabled: enableCheckbox.checked,
            frequency: document.getElementById('employeeReportFrequency').value,
            reportTypes: Array.from(
              document.querySelectorAll('input[name="employeeReportTypes"]:checked')
            ).map(el => el.value),
            preferredEmail: document.getElementById('preferredEmail').value
          };
      
          try {
            const response = await fetch('/api/employee-report-automation', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
              },
              body: JSON.stringify(settings)
            });
      
            if (response.ok) {
              alert('Employee Report Automation Settings Saved');
            } else {
              throw new Error('Failed to save settings');
            }
          } catch (error) {
            console.error(error);
            alert('Failed to save settings');
          }
          });
        }
      });
      </script>
    </main>

    <!-- Floating Action Button for Mobile -->
    <button class="fab-reporting" onclick="document.getElementById('reportForm').scrollIntoView({behavior: 'smooth'})" aria-label="Generate Report">
      <i class="ph ph-file-plus"></i>
    </button>

    <%- include('partials/mobile-bottom-nav', { req: { path: `/clients/${company.companyCode}/reporting` }, company: company }) %>

    <!-- Report Settings Modal -->
    <div id="reportSettingsModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h2><i class="ph ph-gear"></i> Report Settings</h2>
          <button class="close"><i class="ph ph-x"></i></button>
        </div>

        <div class="modal-body">
          <form id="reportSettingsForm">
            <input type="hidden" name="_csrf" value="<%= csrfToken %>" />

            <!-- Default Settings -->
            <div class="settings-section">
              <h3>Default Settings</h3>
              <div class="form-group">
                <label for="defaultFormat">Default Format</label>
                <select id="defaultFormat" name="defaultFormat">
                  <option value="pdf">PDF</option>
                  <option value="excel">Excel</option>
                  <option value="csv">CSV</option>
                </select>
              </div>
            </div>

            <!-- Payroll Reports Settings -->
            <div class="settings-section">
              <h3>Payroll Reports</h3>

              <!-- Payslips Settings -->
              <div class="settings-subsection">
                <h4>Payslips</h4>
                <div class="checkbox-group">
                  <input
                    type="checkbox"
                    id="maskSensitiveData"
                    name="payslips.maskSensitiveData"
                  />
                  <label for="maskSensitiveData">Mask Sensitive Data</label>
                </div>
                <div class="checkbox-group">
                  <input
                    type="checkbox"
                    id="includeYTDTotals"
                    name="payslips.includeYTDTotals"
                    checked
                  />
                  <label for="includeYTDTotals">Include YTD Totals</label>
                </div>
                <div class="checkbox-group">
                  <input
                    type="checkbox"
                    id="showBankDetails"
                    name="payslips.showBankDetails"
                    checked
                  />
                  <label for="showBankDetails">Show Bank Details</label>
                </div>
              </div>

              <!-- Banking Report Settings -->
              <div class="settings-subsection">
                <h4>Banking Report</h4>
                <div class="checkbox-group">
                  <input
                    type="checkbox"
                    id="maskAccountNumbers"
                    name="bankingReport.maskAccountNumbers"
                  />
                  <label for="maskAccountNumbers">Mask Account Numbers</label>
                </div>
                <div class="checkbox-group">
                  <input
                    type="checkbox"
                    id="groupByBank"
                    name="bankingReport.groupByBank"
                    checked
                  />
                  <label for="groupByBank">Group by Bank</label>
                </div>
              </div>
            </div>

            <!-- Employee Reports Settings -->
            <div class="settings-section">
              <h3>Employee Reports</h3>
              <div class="checkbox-group">
                <input
                  type="checkbox"
                  id="includePersonalInfo"
                  name="employeeReports.includePersonalInfo"
                  checked
                />
                <label for="includePersonalInfo"
                  >Include Personal Information</label
                >
              </div>
              <div class="checkbox-group">
                <input
                  type="checkbox"
                  id="includeBankDetails"
                  name="employeeReports.includeBankDetails"
                />
                <label for="includeBankDetails">Include Bank Details</label>
              </div>
            </div>

            <!-- Email Settings -->
            <div class="settings-section">
              <h3>Email Settings</h3>
              <p class="section-description">Configure email automation for different report types</p>

              <!-- Email Settings Tabs -->
              <div class="email-settings-tabs">
                <div class="tab-nav">
                  <button type="button" class="tab-button active" data-tab="global">
                    <i class="ph ph-globe"></i>
                    Global Settings
                  </button>
                  <button type="button" class="tab-button" data-tab="payslips">
                    <i class="ph ph-receipt"></i>
                    Payslips
                  </button>
                  <button type="button" class="tab-button" data-tab="banking">
                    <i class="ph ph-bank"></i>
                    Banking Reports
                  </button>
                  <button type="button" class="tab-button" data-tab="employee">
                    <i class="ph ph-users"></i>
                    Employee Reports
                  </button>
                </div>

                <!-- Global Email Settings Tab -->
                <div class="tab-content active" id="global-tab">
                  <h4>Global Email Settings</h4>
                  <small class="form-help">Default settings for all reports (used when specific settings are not configured)</small>

                  <div class="checkbox-group">
                    <input
                      type="checkbox"
                      id="autoEmailReports"
                      name="emailSettings.autoEmailReports"
                    />
                    <label for="autoEmailReports">Auto-email Reports</label>
                  </div>
                  <div class="form-group">
                    <label for="emailRecipients">Email Recipients</label>
                    <input
                      type="text"
                      id="emailRecipients"
                      name="emailSettings.emailRecipients"
                      placeholder="Enter email addresses (comma-separated)"
                    />
                    <small class="form-help">Enter multiple email addresses separated by commas</small>
                  </div>
                </div>

                <!-- Payslips Email Settings Tab -->
                <div class="tab-content" id="payslips-tab">
                  <h4>Payslips & Payroll Reports</h4>
                  <small class="form-help">Email settings for payslips, payroll summaries, and statutory reports</small>

                  <div class="checkbox-group">
                    <input
                      type="checkbox"
                      id="payslipsAutoEmail"
                      name="payslips.emailSettings.autoEmailReports"
                    />
                    <label for="payslipsAutoEmail">Auto-email Payslip Reports</label>
                  </div>
                  <div class="form-group">
                    <label for="payslipsEmailRecipients">Email Recipients</label>
                    <input
                      type="text"
                      id="payslipsEmailRecipients"
                      name="payslips.emailSettings.emailRecipients"
                      placeholder="Enter email addresses (comma-separated)"
                    />
                    <small class="form-help">Leave empty to use global settings</small>
                  </div>
                </div>

                <!-- Banking Reports Email Settings Tab -->
                <div class="tab-content" id="banking-tab">
                  <h4>Banking Reports</h4>
                  <small class="form-help">Email settings for banking and financial reports</small>

                  <div class="checkbox-group">
                    <input
                      type="checkbox"
                      id="bankingAutoEmail"
                      name="bankingReport.emailSettings.autoEmailReports"
                    />
                    <label for="bankingAutoEmail">Auto-email Banking Reports</label>
                  </div>
                  <div class="form-group">
                    <label for="bankingEmailRecipients">Email Recipients</label>
                    <input
                      type="text"
                      id="bankingEmailRecipients"
                      name="bankingReport.emailSettings.emailRecipients"
                      placeholder="Enter email addresses (comma-separated)"
                    />
                    <small class="form-help">Leave empty to use global settings</small>
                  </div>
                </div>

                <!-- Employee Reports Email Settings Tab -->
                <div class="tab-content" id="employee-tab">
                  <h4>Employee Reports</h4>
                  <small class="form-help">Email settings for employee lists, leave reports, and HR reports</small>

                  <div class="checkbox-group">
                    <input
                      type="checkbox"
                      id="employeeAutoEmail"
                      name="employeeReports.emailSettings.autoEmailReports"
                    />
                    <label for="employeeAutoEmail">Auto-email Employee Reports</label>
                  </div>
                  <div class="form-group">
                    <label for="employeeEmailRecipients">Email Recipients</label>
                    <input
                      type="text"
                      id="employeeEmailRecipients"
                      name="employeeReports.emailSettings.emailRecipients"
                      placeholder="Enter email addresses (comma-separated)"
                    />
                    <small class="form-help">Leave empty to use global settings</small>
                  </div>
                </div>
              </div>

              <!-- Test Email Section -->
              <div class="form-group">
                <label for="testEmail">Test Email Configuration</label>
                <div style="display: flex; gap: 0.5rem; align-items: center;">
                  <input
                    type="email"
                    id="testEmail"
                    placeholder="Enter test email address"
                    style="flex: 1;"
                  />
                  <button
                    type="button"
                    id="sendTestEmail"
                    class="action-button secondary"
                    style="min-width: auto; padding: 0.75rem 1rem;"
                  >
                    <i class="ph ph-paper-plane-tilt"></i>
                    Send Test
                  </button>
                </div>
                <small class="form-help">Send a test email to verify your email configuration</small>
              </div>
            </div>

            <!-- Date Range Presets Settings -->
            <div class="settings-section">
              <h3>Date Range Presets</h3>
              <p class="section-description">Configure default date ranges for different report types</p>

              <!-- Payroll Reports Date Range -->
              <div class="settings-subsection">
                <h4>Payroll Reports Default</h4>
                <div class="form-group">
                  <label for="payrollReportsDefault">Default Date Range</label>
                  <select id="payrollReportsDefault" name="dateRangePresets.payrollReports.default">
                    <option value="currentMonth">Current Month</option>
                    <option value="previousMonth">Previous Month</option>
                    <option value="currentYear">Current Year</option>
                    <option value="custom">Custom Range</option>
                  </select>
                  <small class="form-help">Default date range when generating payroll reports</small>
                </div>
              </div>

              <!-- Employee Reports Date Range -->
              <div class="settings-subsection">
                <h4>Employee Reports Default</h4>
                <div class="form-group">
                  <label for="employeeReportsDefault">Default Date Range</label>
                  <select id="employeeReportsDefault" name="dateRangePresets.employeeReports.default">
                    <option value="allTime">All Time</option>
                    <option value="currentYear">Current Year</option>
                    <option value="custom">Custom Range</option>
                  </select>
                  <small class="form-help">Default date range when generating employee reports</small>
                </div>
              </div>
            </div>

            <!-- Custom Date Range Container -->
            <div id="customDateRange" class="custom-date-range">
              <div class="date-inputs">
                <div class="form-group">
                  <label for="startDate">Start Date</label>
                  <input
                    type="date"
                    id="startDate"
                    name="startDate"
                    class="form-control"
                  />
                </div>
                <div class="form-group">
                  <label for="endDate">End Date</label>
                  <input
                    type="date"
                    id="endDate"
                    name="endDate"
                    class="form-control"
                  />
                </div>
              </div>
            </div>

            <div class="modal-actions">
              <button type="button" class="action-button secondary close-modal">
                Cancel
              </button>
              <button type="submit" class="action-button primary">
                <i class="ph ph-check"></i> Save Settings
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Toast Notification System -->
    <script src="/js/toast-notifications.js"></script>
    <script src="/js/reporting.js"></script>
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        const reportTypeSelect = document.getElementById('reportType');
        const employeeBasicInfoFilters = document.getElementById('employeeBasicInfoFilters');
        const quickSelectButtons = document.querySelectorAll('.quick-select-btn');
        const allCheckboxes = document.querySelectorAll('input[name="fields"]');

        // Handle report type change - with null check
        if (reportTypeSelect && employeeBasicInfoFilters) {
          reportTypeSelect.addEventListener('change', function() {
            if (this.value === 'employeeBasicInfo') {
              employeeBasicInfoFilters.style.display = 'block';
            } else {
              employeeBasicInfoFilters.style.display = 'none';
            }
          });
        }

        // Quick Select Button Presets - Updated for comprehensive field coverage
        const presets = {
          essential: [
            'firstName', 'lastName', 'companyEmployeeNumber',
            'jobTitle', 'department', 'email', 'phone'
          ],
          detailed: [
            'firstName', 'lastName', 'companyEmployeeNumber',
            'jobTitle', 'department', 'email', 'phone',
            'dob', 'idType', 'idNumber', 'incomeTaxNumber',
            'workingHours', 'payFrequency', 'doa', 'gender', 'race',
            'paymentMethod', 'bankName', 'accountNumber', 'streetAddress',
            'city', 'postalCode', 'province'
          ],
          complete: 'all'
        };

        // Handle Select All functionality for each field group
        document.querySelectorAll('.field-group').forEach(group => {
          const selectAllCheckbox = group.querySelector('.select-all');
          const fieldCheckboxes = group.querySelectorAll('input[name="fields"]');

          if (selectAllCheckbox) {
            // Handle select all checkbox change
            selectAllCheckbox.addEventListener('change', function() {
              fieldCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
              });
            });

            // Handle individual checkbox changes
            fieldCheckboxes.forEach(checkbox => {
              checkbox.addEventListener('change', function() {
                const allChecked = Array.from(fieldCheckboxes).every(cb => cb.checked);
                selectAllCheckbox.checked = allChecked;
                selectAllCheckbox.indeterminate = !allChecked && 
                  Array.from(fieldCheckboxes).some(cb => cb.checked);
              });
            });
          }
        });

        // Handle Quick Select Buttons - with null check
        if (quickSelectButtons && quickSelectButtons.length > 0) {
          quickSelectButtons.forEach(button => {
            button.addEventListener('click', function() {
              // Update active state
              quickSelectButtons.forEach(btn => btn.classList.remove('active'));
              this.classList.add('active');

              const preset = this.dataset.preset;
            
            // Update checkboxes based on preset
            allCheckboxes.forEach(checkbox => {
              if (presets[preset] === 'all') {
                checkbox.checked = true;
              } else {
                checkbox.checked = presets[preset].includes(checkbox.value);
              }
            });

            // Update "Select All" checkboxes state
            updateAllSelectAllCheckboxes();
            });
          });
        }

        // Function to update a single group's select all checkbox
        function updateSelectAllCheckbox(group) {
          const checkboxes = group.querySelectorAll('input[name="fields"]');
          const selectAllCheckbox = group.querySelector('.select-all');
          const allChecked = Array.from(checkboxes).every(cb => cb.checked);
          const someChecked = Array.from(checkboxes).some(cb => cb.checked);
          
          selectAllCheckbox.checked = allChecked;
          selectAllCheckbox.indeterminate = someChecked && !allChecked;
        }

        // Function to update all select all checkboxes
        function updateAllSelectAllCheckboxes() {
          document.querySelectorAll('.field-group').forEach(group => {
            updateSelectAllCheckbox(group);
          });
        }

        // Function to update field counter
        function updateFieldCounter() {
          const totalFields = document.querySelectorAll('input[name="fields"]').length;
          const selectedFields = document.querySelectorAll('input[name="fields"]:checked').length;

          const selectedCountElement = document.getElementById('selectedFieldCount');
          const totalCountElement = document.getElementById('totalFieldCount');

          if (selectedCountElement) selectedCountElement.textContent = selectedFields;
          if (totalCountElement) totalCountElement.textContent = totalFields;
        }

        // Add event listeners to all field checkboxes for counter updates
        document.querySelectorAll('input[name="fields"]').forEach(checkbox => {
          checkbox.addEventListener('change', updateFieldCounter);
        });

        // Initial counter update
        updateFieldCounter();

        // DEBUGGING: Log initial field state
        console.log('=== INITIAL FIELD STATE DEBUG ===');
        console.log('Total field checkboxes on page load:', document.querySelectorAll('input[name="fields"]').length);
        console.log('Initially checked field checkboxes:', document.querySelectorAll('input[name="fields"]:checked').length);

        // Log all field values
        const allFieldCheckboxes = document.querySelectorAll('input[name="fields"]');
        console.log('All available field values:');
        allFieldCheckboxes.forEach((checkbox, index) => {
          console.log(`${index + 1}. ${checkbox.value} (checked: ${checkbox.checked})`);
        });
        console.log('=== END INITIAL DEBUG ===');
      });
    </script>
  </body>
</html>
