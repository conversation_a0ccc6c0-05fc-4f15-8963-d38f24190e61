<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="csrf-token" content="<%= csrfToken %>" />
    <title>EFT Settings | <%= company.name %></title>

    <!-- Modern Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />

    <!-- Phosphor Icons -->
    <script src="https://unpkg.com/@phosphor-icons/web"></script>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/styles.css" />
    <link rel="stylesheet" href="/css/onboarding.css" />
    <link rel="stylesheet" href="/css/employeeManagement.css" />
    <link rel="stylesheet" href="/css/header.css" />
    <link rel="stylesheet" href="/css/eft-settings.css" />
    <link rel="stylesheet" href="/css/settings.css" />
    <link rel="stylesheet" href="/css/mobile-header.css" />
    <link rel="stylesheet" href="/css/mobile-employee-profile.css" />
    <link rel="stylesheet" href="/css/mobile-nav.css" />
    <link rel="stylesheet" href="/css/notifications.css" />
    <link rel="stylesheet" href="/css/dark-mode.css" />
  </head>

  <body>
    <div class="layout-wrapper">
      <%- include('../partials/sidebar', { user: user, company: company }) %>
      <div class="content-wrapper">
        <%- include('../partials/header', { user: user, company: company, currentPage: 'settings' }) %>

        <main class="main-container">
          <!-- Tabs Section -->
          <div id="tabs-section" style="margin-top: 6.5rem;">
  <div class="tab-row main-tabs">
    <a
      href="/clients/<%= company.companyCode %>/settings/accounting"
      class="tab-button <%= activeTab === 'accounting' ? 'active' : '' %>"
    >
      <i class="ph ph-calculator"></i>
      Accounting
    </a>
    <a
      href="/clients/<%= company.companyCode %>/settings/employee"
      class="tab-button"
    >
      <i class="ph ph-users"></i>
      Employee
    </a>
    <a
      href="/clients/<%= company.companyCode %>/settings/payroll"
      class="tab-button"
    >
      <i class="ph ph-money"></i>
      Payroll
    </a>
    <a
      href="/clients/<%= company.companyCode %>/settings/other"
      class="tab-button"
    >
      <i class="ph ph-gear"></i>
      Other
    </a>
  </div>
</div>

          <!-- Settings Grid -->
          <div class="settings-grid">
            <!-- Primary Bank Account Section -->
            <div class="settings-card">
              <div class="card-header">
                <h3>
                  <i class="ph ph-bank"></i>
                  Primary Bank Account
                </h3>
              </div>

              <div class="step-indicator">
                <div class="step-number active" data-step="1">1</div>
                <div class="step-number" data-step="2">2</div>
                <div class="step-number" data-step="3">3</div>
                <div class="step-number" data-step="4">4</div>
              </div>

              <form id="primaryBankAccountForm">
                <div class="form-step active" data-step="1">
                  <div class="form-group">
                    <label for="eftFormat">EFT Format</label>
                    <select id="eftFormat" name="eftFormat" required>
                      <option value="">Select EFT Format</option>
                      <optgroup label="ABSA">
                        <option value="ABSA Cash Focus" <%= eftDetails.eftFormat === 'ABSA Cash Focus' ? 'selected' : '' %>>ABSA Cash Focus</option>
                        <option value="ABSA Business Integrator (.txt)" <%= eftDetails.eftFormat === 'ABSA Business Integrator (.txt)' ? 'selected' : '' %>>ABSA Business Integrator (.txt)</option>
                        <option value="ABSA Business Integrator (.csv)" <%= eftDetails.eftFormat === 'ABSA Business Integrator (.csv)' ? 'selected' : '' %>>ABSA Business Integrator (.csv)</option>
                        <option value="ABSA Business Integrator SAP (beta)" <%= eftDetails.eftFormat === 'ABSA Business Integrator SAP (beta)' ? 'selected' : '' %>>ABSA Business Integrator SAP (beta)</option>
                        <option value="ABSA Business Integrator Online (.csv)" <%= eftDetails.eftFormat === 'ABSA Business Integrator Online (.csv)' ? 'selected' : '' %>>ABSA Business Integrator Online (.csv)</option>
                      </optgroup>
                      <optgroup label="FNB">
                        <option value="FNB Bankit" <%= eftDetails.eftFormat === 'FNB Bankit' ? 'selected' : '' %>>FNB Bankit</option>
                        <option value="FNB CAMS" <%= eftDetails.eftFormat === 'FNB CAMS' ? 'selected' : '' %>>FNB CAMS</option>
                        <option value="FNB Enterprise CSV" <%= eftDetails.eftFormat === 'FNB Enterprise CSV' ? 'selected' : '' %>>FNB Enterprise CSV</option>
                        <option value="FNB Online Banking (ACB)" <%= eftDetails.eftFormat === 'FNB Online Banking (ACB)' ? 'selected' : '' %>>FNB Online Banking (ACB)</option>
                        <option value="FNB PACS" <%= eftDetails.eftFormat === 'FNB PACS' ? 'selected' : '' %>>FNB PACS</option>
                      </optgroup>
                      <optgroup label="Standard Bank">
                        <option value="Standard Bank CATS" <%= eftDetails.eftFormat === 'Standard Bank CATS' ? 'selected' : '' %>>Standard Bank CATS</option>
                        <option value="Standard Bank Value Dated / EFTS" <%= eftDetails.eftFormat === 'Standard Bank Value Dated / EFTS' ? 'selected' : '' %>>Standard Bank Value Dated / EFTS</option>
                        <option value="Standard Bank SSVS" <%= eftDetails.eftFormat === 'Standard Bank SSVS' ? 'selected' : '' %>>Standard Bank SSVS</option>
                      </optgroup>
                      <optgroup label="Nedbank">
                        <option value="NedBank Business Banking (CSV)" <%= eftDetails.eftFormat === 'NedBank Business Banking (CSV)' ? 'selected' : '' %>>NedBank Business Banking (CSV)</option>
                        <option value="NedInform" <%= eftDetails.eftFormat === 'NedInform' ? 'selected' : '' %>>NedInform</option>
                      </optgroup>
                      <optgroup label="Other Banks">
                        <option value="Albaraka" <%= eftDetails.eftFormat === 'Albaraka' ? 'selected' : '' %>>Albaraka</option>
                        <option value="Capitec ACB" <%= eftDetails.eftFormat === 'Capitec ACB' ? 'selected' : '' %>>Capitec ACB</option>
                        <option value="Investec CSV" <%= eftDetails.eftFormat === 'Investec CSV' ? 'selected' : '' %>>Investec CSV</option>
                        <option value="Netcash" <%= eftDetails.eftFormat === 'Netcash' ? 'selected' : '' %>>Netcash</option>
                        <option value="Sage Pay" <%= eftDetails.eftFormat === 'Sage Pay' ? 'selected' : '' %>>Sage Pay</option>
                      </optgroup>
                    </select>
                  </div>
                  <div class="button-group">
                    <button type="button" class="btn btn-primary" onclick="nextStep(1)">Next <i class="ph ph-arrow-right"></i></button>
                  </div>
                </div>

                <div class="form-step" data-step="2">
                  <div class="form-group">
                    <label for="bank">Bank</label>
                    <select id="bank" name="bank" required>
                      <option value="">Select Bank</option>
                      <optgroup label="Most Common">
                        <option value="ABSA Bank" <%= eftDetails.bank === 'ABSA Bank' ? 'selected' : '' %>>ABSA Bank</option>
                        <option value="Capitec Bank" <%= eftDetails.bank === 'Capitec Bank' ? 'selected' : '' %>>Capitec Bank</option>
                        <option value="First National Bank" <%= eftDetails.bank === 'First National Bank' ? 'selected' : '' %>>First National Bank</option>
                        <option value="Nedbank" <%= eftDetails.bank === 'Nedbank' ? 'selected' : '' %>>Nedbank</option>
                        <option value="Standard Bank" <%= eftDetails.bank === 'Standard Bank' ? 'selected' : '' %>>Standard Bank</option>
                      </optgroup>
                    </select>
                  </div>
                  <div class="button-group">
                    <button type="button" class="btn btn-secondary" onclick="prevStep(2)"><i class="ph ph-arrow-left"></i> Previous</button>
                    <button type="button" class="btn btn-primary" onclick="nextStep(2)">Next <i class="ph ph-arrow-right"></i></button>
                  </div>
                </div>

                <div class="form-step" data-step="3">
                  <div class="form-group">
                    <label for="accountNumber">Account Number</label>
                    <input type="text" id="accountNumber" name="accountNumber" value="<%= eftDetails.accountNumber %>" required />
                  </div>
                  <div class="form-group">
                    <label for="branchCode">Branch Code</label>
                    <input type="text" id="branchCode" name="branchCode" value="<%= eftDetails.branchCode %>" required />
                  </div>
                  <div class="button-group">
                    <button type="button" class="btn btn-secondary" onclick="prevStep(3)"><i class="ph ph-arrow-left"></i> Previous</button>
                    <button type="button" class="btn btn-primary" onclick="nextStep(3)">Next <i class="ph ph-arrow-right"></i></button>
                  </div>
                </div>

                <div class="form-step" data-step="4">
                  <div class="form-group">
                    <label for="accountType">Account Type</label>
                    <select id="accountType" name="accountType" required>
                      <option value="">Select Account Type</option>
                      <option value="Current" <%= eftDetails.accountType === 'Current' ? 'selected' : '' %>>Current</option>
                      <option value="Savings" <%= eftDetails.accountType === 'Savings' ? 'selected' : '' %>>Savings</option>
                      <option value="Transmission" <%= eftDetails.accountType === 'Transmission' ? 'selected' : '' %>>Transmission</option>
                    </select>
                  </div>
                  <div class="button-group">
                    <button type="button" class="btn btn-secondary" onclick="prevStep(4)"><i class="ph ph-arrow-left"></i> Previous</button>
                    <button type="submit" class="btn btn-primary">Save Changes <i class="ph ph-check"></i></button>
                  </div>
                </div>
              </form>
            </div>

            <!-- EFT Summary Section -->
            <div class="settings-card">
              <div class="card-header">
                <h3>
                  <i class="ph ph-info"></i>
                  EFT Summary
                </h3>
              </div>
              <div class="eft-summary">
                <div class="summary-item">
                  <span class="summary-label">EFT Format</span>
                  <span class="summary-value"><%= eftDetails.eftFormat || 'Not set' %></span>
                </div>
                <div class="summary-item">
                  <span class="summary-label">Bank</span>
                  <span class="summary-value"><%= eftDetails.bank || 'Not set' %></span>
                </div>
                <div class="summary-item">
                  <span class="summary-label">Account Type</span>
                  <span class="summary-value"><%= eftDetails.accountType || 'Not set' %></span>
                </div>
                <div class="summary-item">
                  <span class="summary-label">Account Number</span>
                  <span class="summary-value"><%= eftDetails.accountNumber ? '****' + eftDetails.accountNumber.slice(-4) : 'Not set' %></span>
                </div>
                <div class="summary-item">
                  <span class="summary-label">Branch Code</span>
                  <span class="summary-value"><%= eftDetails.branchCode || 'Not set' %></span>
                </div>
              </div>
            </div>

            <!-- Additional Bank Accounts Section -->
            <div class="settings-card additional-accounts-card">
              <div class="card-header">
                <h3>
                  <i class="ph ph-plus-circle"></i>
                  Additional Bank Accounts
                </h3>
                <button type="button" class="btn btn-primary btn-sm" onclick="showAddAccountModal()">
                  <i class="ph ph-plus"></i> Add Account
                </button>
              </div>

              <div class="additional-accounts-list" id="additionalAccountsList">
                <% if (eftDetails.additionalBankAccounts && eftDetails.additionalBankAccounts.length > 0) { %>
                  <% eftDetails.additionalBankAccounts.forEach((account, index) => { %>
                    <div class="account-item" data-index="<%= index %>">
                      <div class="account-info">
                        <div class="account-header">
                          <h4><%= account.accountDescription %></h4>
                          <div class="account-actions">
                            <button type="button" class="btn-icon" onclick="editAccount(<%= index %>)" title="Edit Account">
                              <i class="ph ph-pencil"></i>
                            </button>
                            <button type="button" class="btn-icon btn-danger" onclick="removeAccount(<%= index %>)" title="Remove Account">
                              <i class="ph ph-trash"></i>
                            </button>
                          </div>
                        </div>
                        <div class="account-details">
                          <div class="detail-item">
                            <span class="detail-label">Bank:</span>
                            <span class="detail-value"><%= account.bankName %></span>
                          </div>
                          <div class="detail-item">
                            <span class="detail-label">Account Type:</span>
                            <span class="detail-value"><%= account.accountType %></span>
                          </div>
                          <div class="detail-item">
                            <span class="detail-label">Account Number:</span>
                            <span class="detail-value">****<%= account.accountNumber.slice(-4) %></span>
                          </div>
                          <div class="detail-item">
                            <span class="detail-label">Branch Code:</span>
                            <span class="detail-value"><%= account.branchCode %></span>
                          </div>
                        </div>
                      </div>
                    </div>
                  <% }); %>
                <% } else { %>
                  <div class="empty-state">
                    <i class="ph ph-bank"></i>
                    <p>No additional bank accounts configured</p>
                    <p class="empty-description">Add additional bank accounts to manage multiple payment sources for your payroll.</p>
                  </div>
                <% } %>
              </div>
            </div>
          </div>

          <!-- Add/Edit Bank Account Modal -->
          <div id="bankAccountModal" class="xpay-modal">
            <div class="xpay-modal-content">
              <div class="xpay-modal-header">
                <h3 id="modalTitle">Add Bank Account</h3>
                <button class="close-button" onclick="closeBankAccountModal()">×</button>
              </div>
              <div class="xpay-modal-body">
                <form id="bankAccountForm">
                  <input type="hidden" id="editIndex" value="-1">

                  <div class="form-group">
                    <label for="modalAccountDescription">Account Description/Label *</label>
                    <input type="text" id="modalAccountDescription" name="accountDescription" required
                           placeholder="e.g., Payroll Account, Executive Salaries, etc.">
                  </div>

                  <div class="form-group">
                    <label for="modalBankName">Bank Name *</label>
                    <select id="modalBankName" name="bankName" required>
                      <option value="">Select Bank</option>
                      <optgroup label="Most Common">
                        <option value="ABSA Bank">ABSA Bank</option>
                        <option value="Capitec Bank">Capitec Bank</option>
                        <option value="First National Bank">First National Bank</option>
                        <option value="Nedbank">Nedbank</option>
                        <option value="Standard Bank">Standard Bank</option>
                      </optgroup>
                      <optgroup label="Other Banks">
                        <option value="African Bank">African Bank</option>
                        <option value="Bidvest Bank">Bidvest Bank</option>
                        <option value="Discovery Bank">Discovery Bank</option>
                        <option value="Investec">Investec</option>
                        <option value="Sasfin Bank">Sasfin Bank</option>
                        <option value="TymeBank">TymeBank</option>
                      </optgroup>
                    </select>
                  </div>

                  <div class="form-row">
                    <div class="form-group">
                      <label for="modalAccountNumber">Account Number *</label>
                      <input type="text" id="modalAccountNumber" name="accountNumber" required
                             placeholder="Enter account number">
                    </div>
                    <div class="form-group">
                      <label for="modalBranchCode">Branch Code *</label>
                      <input type="text" id="modalBranchCode" name="branchCode" required
                             placeholder="6-digit code">
                    </div>
                  </div>

                  <div class="form-group">
                    <label for="modalAccountType">Account Type *</label>
                    <select id="modalAccountType" name="accountType" required>
                      <option value="">Select Account Type</option>
                      <option value="Current">Current</option>
                      <option value="Savings">Savings</option>
                      <option value="Transmission">Transmission</option>
                    </select>
                  </div>
                </form>
              </div>
              <div class="xpay-modal-actions">
                <button type="button" class="btn btn-secondary" onclick="closeBankAccountModal()">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveBankAccount()">Save Account</button>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
    <%- include('../partials/mobile-bottom-nav', { req: req }) %>


    <script>
      // Toast notification function following PandaPayroll patterns
      function showToast(options) {
        if (typeof options === 'string') {
          options = { message: options, type: 'info' };
        }

        const { type = 'info', message = '', duration = 4000 } = options;

        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;

        // Add icon based on type
        const iconClass = type === 'success' ? 'ph-check-circle' :
                         type === 'error' ? 'ph-x-circle' :
                         type === 'warning' ? 'ph-warning-circle' : 'ph-info';

        toast.innerHTML = `
          <i class="ph ${iconClass}"></i>
          <span>${message}</span>
        `;

        document.body.appendChild(toast);

        // Trigger animation
        setTimeout(() => {
          toast.classList.add('show');
        }, 10);

        // Auto-remove after duration
        setTimeout(() => {
          toast.classList.remove('show');
          setTimeout(() => {
            if (toast.parentNode) {
              toast.parentNode.removeChild(toast);
            }
          }, 300);
        }, duration);
      }

      // Form validation functions
      function validateStep(stepNumber) {
        const currentStep = document.querySelector(`.form-step[data-step="${stepNumber}"]`);
        const inputs = currentStep.querySelectorAll('input[required], select[required]');
        let isValid = true;

        inputs.forEach(input => {
          if (!input.value.trim()) {
            input.classList.add('error');
            isValid = false;
          } else {
            input.classList.remove('error');
          }
        });

        return isValid;
      }

      function validateAccountNumber(accountNumber) {
        // Remove any non-digit characters
        const cleanNumber = accountNumber.replace(/\D/g, '');
        // Check if it's between 8-12 digits (typical SA bank account range)
        return cleanNumber.length >= 8 && cleanNumber.length <= 12;
      }

      function validateBranchCode(branchCode) {
        // Remove any non-digit characters
        const cleanCode = branchCode.replace(/\D/g, '');
        // Check if it's 6 digits (SA branch code standard)
        return cleanCode.length === 6;
      }

      function nextStep(currentStep) {
        // Validate current step before proceeding
        if (!validateStep(currentStep)) {
          showToast({
            type: 'warning',
            message: 'Please fill in all required fields before proceeding.'
          });
          return;
        }

        // Additional validation for specific steps
        if (currentStep === 3) {
          const accountNumber = document.getElementById('accountNumber').value;
          const branchCode = document.getElementById('branchCode').value;

          if (!validateAccountNumber(accountNumber)) {
            showToast({
              type: 'error',
              message: 'Please enter a valid account number (8-12 digits).'
            });
            document.getElementById('accountNumber').focus();
            return;
          }

          if (!validateBranchCode(branchCode)) {
            showToast({
              type: 'error',
              message: 'Please enter a valid branch code (6 digits).'
            });
            document.getElementById('branchCode').focus();
            return;
          }
        }

        document.querySelector(`.form-step[data-step="${currentStep}"]`).classList.remove('active');
        document.querySelector(`.form-step[data-step="${currentStep + 1}"]`).classList.add('active');
        document.querySelector(`.step-number[data-step="${currentStep}"]`).classList.add('completed');
        document.querySelector(`.step-number[data-step="${currentStep + 1}"]`).classList.add('active');
      }

      function prevStep(currentStep) {
        document.querySelector(`.form-step[data-step="${currentStep}"]`).classList.remove('active');
        document.querySelector(`.form-step[data-step="${currentStep - 1}"]`).classList.add('active');
        document.querySelector(`.step-number[data-step="${currentStep}"]`).classList.remove('active');
        document.querySelector(`.step-number[data-step="${currentStep - 1}"]`).classList.remove('completed');
        document.querySelector(`.step-number[data-step="${currentStep - 1}"]`).classList.add('active');
      }

      // Additional Bank Accounts Management
      let additionalAccounts = <%= JSON.stringify(eftDetails.additionalBankAccounts || []) %>;

      function showAddAccountModal() {
        console.log('showAddAccountModal called');

        const modal = document.getElementById('bankAccountModal');
        const modalTitle = document.getElementById('modalTitle');
        const editIndex = document.getElementById('editIndex');
        const form = document.getElementById('bankAccountForm');

        console.log('Modal element:', modal);
        console.log('Modal title element:', modalTitle);
        console.log('Edit index element:', editIndex);
        console.log('Form element:', form);

        if (!modal) {
          console.error('Modal element not found!');
          return;
        }

        modalTitle.textContent = 'Add Bank Account';
        editIndex.value = '-1';
        form.reset();

        console.log('Adding show class to modal');
        modal.classList.add('show');

        console.log('Modal classes after adding show:', modal.classList.toString());

        // Check modal and content visibility
        const modalContent = modal.querySelector('.xpay-modal-content');
        console.log('Modal content element:', modalContent);

        setTimeout(() => {
          const modalStyles = window.getComputedStyle(modal);
          const contentStyles = modalContent ? window.getComputedStyle(modalContent) : null;

          console.log('Modal display:', modalStyles.display);
          console.log('Modal visibility:', modalStyles.visibility);
          console.log('Modal opacity:', modalStyles.opacity);
          console.log('Modal z-index:', modalStyles.zIndex);

          if (contentStyles) {
            console.log('Content display:', contentStyles.display);
            console.log('Content visibility:', contentStyles.visibility);
            console.log('Content opacity:', contentStyles.opacity);
            console.log('Content transform:', contentStyles.transform);
            console.log('Content position:', contentStyles.position);
            console.log('Content top:', contentStyles.top);
            console.log('Content left:', contentStyles.left);
            console.log('Content width:', contentStyles.width);
            console.log('Content height:', contentStyles.height);
          }
        }, 100);
      }

      function editAccount(index) {
        const account = additionalAccounts[index];
        document.getElementById('modalTitle').textContent = 'Edit Bank Account';
        document.getElementById('editIndex').value = index;

        // Populate form with account data
        document.getElementById('modalAccountDescription').value = account.accountDescription;
        document.getElementById('modalBankName').value = account.bankName;
        document.getElementById('modalAccountNumber').value = account.accountNumber;
        document.getElementById('modalBranchCode').value = account.branchCode;
        document.getElementById('modalAccountType').value = account.accountType;

        document.getElementById('bankAccountModal').classList.add('show');
      }

      function closeBankAccountModal() {
        document.getElementById('bankAccountModal').classList.remove('show');
      }

      function validateBankAccountForm() {
        const form = document.getElementById('bankAccountForm');
        const formData = new FormData(form);
        const accountData = {
          accountDescription: formData.get('accountDescription'),
          bankName: formData.get('bankName'),
          accountNumber: formData.get('accountNumber'),
          branchCode: formData.get('branchCode'),
          accountType: formData.get('accountType')
        };

        // Validate required fields
        const requiredFields = ['accountDescription', 'bankName', 'accountNumber', 'branchCode', 'accountType'];
        const missingFields = requiredFields.filter(field => !accountData[field]);

        if (missingFields.length > 0) {
          showToast({
            type: 'warning',
            message: `Please fill in all required fields: ${missingFields.join(', ')}`
          });
          return null;
        }

        // Validate account number
        if (!validateAccountNumber(accountData.accountNumber)) {
          showToast({
            type: 'error',
            message: 'Please enter a valid account number (8-12 digits).'
          });
          return null;
        }

        // Validate branch code
        if (!validateBranchCode(accountData.branchCode)) {
          showToast({
            type: 'error',
            message: 'Please enter a valid branch code (6 digits).'
          });
          return null;
        }

        // Check for duplicate account descriptions (excluding current edit)
        const editIndex = parseInt(document.getElementById('editIndex').value);
        const duplicateIndex = additionalAccounts.findIndex((account, index) =>
          account.accountDescription.toLowerCase() === accountData.accountDescription.toLowerCase() &&
          index !== editIndex
        );

        if (duplicateIndex >= 0) {
          showToast({
            type: 'error',
            message: 'An account with this description already exists. Please use a unique description.'
          });
          return null;
        }

        return accountData;
      }

      function saveBankAccount() {
        const accountData = validateBankAccountForm();
        if (!accountData) return;

        const editIndex = parseInt(document.getElementById('editIndex').value);

        if (editIndex >= 0) {
          // Edit existing account
          additionalAccounts[editIndex] = accountData;
          showToast({
            type: 'success',
            message: 'Bank account updated successfully'
          });
        } else {
          // Add new account
          additionalAccounts.push(accountData);
          showToast({
            type: 'success',
            message: 'Bank account added successfully'
          });
        }

        updateAdditionalAccountsDisplay();
        closeBankAccountModal();
      }

      function removeAccount(index) {
        const account = additionalAccounts[index];
        const confirmMessage = `Are you sure you want to remove "${account.accountDescription}"?\n\nThis action cannot be undone.`;

        if (confirm(confirmMessage)) {
          additionalAccounts.splice(index, 1);
          updateAdditionalAccountsDisplay();
          showToast({
            type: 'success',
            message: 'Bank account removed successfully'
          });
        }
      }

      function updateAdditionalAccountsDisplay() {
        const container = document.getElementById('additionalAccountsList');

        if (additionalAccounts.length === 0) {
          container.innerHTML = `
            <div class="empty-state">
              <i class="ph ph-bank"></i>
              <p>No additional bank accounts configured</p>
              <p class="empty-description">Add additional bank accounts to manage multiple payment sources for your payroll.</p>
            </div>
          `;
        } else {
          container.innerHTML = additionalAccounts.map((account, index) => `
            <div class="account-item" data-index="${index}">
              <div class="account-info">
                <div class="account-header">
                  <h4>${account.accountDescription}</h4>
                  <div class="account-actions">
                    <button type="button" class="btn-icon" onclick="editAccount(${index})" title="Edit Account">
                      <i class="ph ph-pencil"></i>
                    </button>
                    <button type="button" class="btn-icon btn-danger" onclick="removeAccount(${index})" title="Remove Account">
                      <i class="ph ph-trash"></i>
                    </button>
                  </div>
                </div>
                <div class="account-details">
                  <div class="detail-item">
                    <span class="detail-label">Bank:</span>
                    <span class="detail-value">${account.bankName}</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Account Type:</span>
                    <span class="detail-value">${account.accountType}</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Account Number:</span>
                    <span class="detail-value">****${account.accountNumber.slice(-4)}</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Branch Code:</span>
                    <span class="detail-value">${account.branchCode}</span>
                  </div>
                </div>
              </div>
            </div>
          `).join('');
        }
      }

      // Test modal visibility function
      function testModal() {
        console.log('Testing modal...');
        const modal = document.getElementById('bankAccountModal');
        if (modal) {
          console.log('Modal found, testing visibility...');
          modal.style.display = 'flex';
          modal.style.position = 'fixed';
          modal.style.top = '0';
          modal.style.left = '0';
          modal.style.width = '100%';
          modal.style.height = '100%';
          modal.style.backgroundColor = 'rgba(0,0,0,0.5)';
          modal.style.zIndex = '10000';
          modal.style.justifyContent = 'center';
          modal.style.alignItems = 'center';

          const content = modal.querySelector('.xpay-modal-content');
          if (content) {
            content.style.backgroundColor = 'white';
            content.style.padding = '20px';
            content.style.borderRadius = '12px';
            content.style.border = '3px solid red';
            content.style.minHeight = '200px';
            content.style.minWidth = '300px';
          }
        }
      }

      // Add input event listeners for real-time validation
      document.addEventListener('DOMContentLoaded', function() {
        // Format account number input
        const accountNumberInput = document.getElementById('accountNumber');
        if (accountNumberInput) {
          accountNumberInput.addEventListener('input', function(e) {
            // Remove non-digits and limit length
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 12) value = value.substring(0, 12);
            e.target.value = value;
          });
        }

        // Format branch code input
        const branchCodeInput = document.getElementById('branchCode');
        if (branchCodeInput) {
          branchCodeInput.addEventListener('input', function(e) {
            // Remove non-digits and limit to 6 characters
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 6) value = value.substring(0, 6);
            e.target.value = value;
          });
        }

        // Remove error styling on input
        const allInputs = document.querySelectorAll('input, select');
        allInputs.forEach(input => {
          input.addEventListener('input', function() {
            this.classList.remove('error');
          });
        });

        // Modal input formatting for additional accounts
        const modalAccountNumber = document.getElementById('modalAccountNumber');
        if (modalAccountNumber) {
          modalAccountNumber.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 12) value = value.substring(0, 12);
            e.target.value = value;
          });
        }

        const modalBranchCode = document.getElementById('modalBranchCode');
        if (modalBranchCode) {
          modalBranchCode.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 6) value = value.substring(0, 6);
            e.target.value = value;
          });
        }

        // Close modal on outside click
        const modal = document.getElementById('bankAccountModal');
        if (modal) {
          modal.addEventListener('click', function(e) {
            if (e.target === modal) {
              closeBankAccountModal();
            }
          });
        }
      });

      document.getElementById('primaryBankAccountForm').addEventListener('submit', async function(e) {
        e.preventDefault();

        // Get form data
        const formData = new FormData(this);
        const eftData = {
          eftFormat: formData.get('eftFormat'),
          bank: formData.get('bank'),
          accountNumber: formData.get('accountNumber'),
          branchCode: formData.get('branchCode'),
          accountType: formData.get('accountType'),
          additionalBankAccounts: JSON.stringify(additionalAccounts)
        };

        // Validate required fields
        const requiredFields = ['eftFormat', 'bank', 'accountNumber', 'branchCode', 'accountType'];
        const missingFields = requiredFields.filter(field => !eftData[field]);

        if (missingFields.length > 0) {
          showToast({
            type: 'warning',
            message: `Please fill in all required fields: ${missingFields.join(', ')}`
          });
          return;
        }

        try {
          // Show loading state
          const submitButton = this.querySelector('button[type="submit"]');
          const originalText = submitButton.innerHTML;
          submitButton.innerHTML = '<i class="ph ph-spinner ph-spin"></i> Saving...';
          submitButton.disabled = true;

          // Submit to the existing API endpoint
          const response = await fetch(`/clients/<%= company.companyCode %>/settings/accounting/eft`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(eftData)
          });

          if (response.ok) {
            // Success - show success toast and reload
            showToast({
              type: 'success',
              message: 'EFT settings saved successfully'
            });

            // Delay reload to show toast
            setTimeout(() => {
              window.location.reload();
            }, 1500);
          } else {
            const errorData = await response.json();
            throw new Error(errorData.message || 'Failed to save EFT settings');
          }
        } catch (error) {
          console.error('Error saving EFT settings:', error);
          showToast({
            type: 'error',
            message: 'Error saving EFT settings: ' + error.message
          });

          // Reset button state
          const submitButton = this.querySelector('button[type="submit"]');
          submitButton.innerHTML = originalText;
          submitButton.disabled = false;
        }
      });
    </script>
  </body>
</html>
