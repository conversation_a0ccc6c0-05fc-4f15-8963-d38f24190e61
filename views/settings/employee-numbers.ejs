<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Settings - <%= company.name %></title>
    
    <!-- Modern Font -->
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <!-- Phosphor Icons -->
    <script src="https://unpkg.com/@phosphor-icons/web"></script>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/styles.css" />
    <link rel="stylesheet" href="/css/onboarding.css" />
    <link rel="stylesheet" href="/css/employeeManagement.css" />
    <link rel="stylesheet" href="/css/header.css" />
    <link rel="stylesheet" href="/css/sidebar.css" />
    <link rel="stylesheet" href="/css/settings.css" />
    <link rel="stylesheet" href="/css/employee-numbers.css" />
    <link rel="stylesheet" href="/css/mobile-header.css" />
    <link rel="stylesheet" href="/css/mobile-nav.css" />
    <link rel="stylesheet" href="/css/dark-mode.css" />

    <meta name="company-code" content="<%= company.companyCode %>" />
  </head>

  <body>
    <div class="layout-wrapper">
      <%- include('../partials/sidebar', { user: user, company: company }) %>
      <div class="content-wrapper">
        <%- include('../partials/header', { user: user, company: company, currentPage: 'settings' }) %>

        <main class="main-container">
          <!-- Tabs Section -->
          <div id="tabs-section" style="margin-top: 6.5rem;">
        <div class="tab-row">
          <a
            href="/clients/<%= company.companyCode %>/settings/accounting"
            class="tab-button"
          >
            <i class="ph ph-calculator"></i>
            <span>Accounting</span>
          </a>
          <a
            href="/clients/<%= company.companyCode %>/settings/employee"
            class="tab-button <%= activeTab === 'employee' ? 'active' : '' %>"
          >
            <i class="ph ph-users"></i>
            <span>Employee</span>
          </a>
          <a
            href="/clients/<%= company.companyCode %>/settings/payroll"
            class="tab-button"
          >
            <i class="ph ph-money"></i>
            <span>Payroll</span>
          </a>
          <a
            href="/clients/<%= company.companyCode %>/settings/other"
            class="tab-button"
          >
            <i class="ph ph-gear"></i>
            <span>Other</span>
          </a>
        </div>
      </div>

      <!-- Alert Messages -->
      <% if (messages.success && messages.success.length > 0) { %>
        <div class="alert alert-success">
          <i class="ph ph-check-circle"></i>
          <%= messages.success %>
        </div>
      <% } %>

      <% if (messages.error && messages.error.length > 0) { %>
        <div class="alert alert-danger">
          <i class="ph ph-x-circle"></i>
          <%= messages.error %>
        </div>
      <% } %>

      <!-- Settings Grid -->
      <div class="settings-grid">
        <!-- Employee Number Settings Card -->
        <div class="settings-card">
          <div class="card-header">
            <h3>
              <i class="ph ph-identification-card"></i>
              Employee Number Settings
            </h3>
          </div>

          <form id="employeeNumberSettingsForm" action="/clients/<%= company.companyCode %>/settings/employee/employee-numbers" method="POST">
            <input type="hidden" name="_csrf" value="<%= csrfToken %>">
            
            <div class="form-group">
              <label for="employeeNumberMode">Employee number mode</label>
              <select id="employeeNumberMode" name="employeeNumberMode" class="form-select">
                <option value="automatic" <%= employeeNumberSettings.mode === 'automatic' ? 'selected' : '' %>>Automatic</option>
                <option value="manual" <%= employeeNumberSettings.mode === 'manual' ? 'selected' : '' %>>Manual</option>
              </select>
            </div>

            <div class="form-group">
              <label for="firstCompanyEmployeeNumber">First Company Employee Number</label>
              <input
                type="text"
                id="firstCompanyEmployeeNumber"
                name="firstCompanyEmployeeNumber"
                class="form-input"
                value="<%= employeeNumberSettings.firstCompanyEmployeeNumber || '0001' %>"
                data-current-value="<%= employeeNumberSettings.firstCompanyEmployeeNumber || '0001' %>"
                pattern="^[A-Za-z-]*\d+[A-Za-z-]*$"
                title="Must contain at least one number. Can include letters and hyphens (e.g., PSS0001, EMP-001)"
              />
              <small class="help-text">
                This will set the pattern for all employee numbers. Example formats: 0001, PSS0001, EMP-001
              </small>
            </div>

            <div class="checkbox-group" id="updateExistingWrapper">
              <label class="checkbox-label">
                <input
                  type="checkbox"
                  id="updateExistingEmployees"
                  name="updateExistingEmployees"
                />
                <span>Update existing employees when switching to auto mode</span>
              </label>
              <small class="help-text">
                Warning: This will update ALL existing employee numbers to match the new format
              </small>
            </div>

            <div class="form-actions">
              <button type="submit" class="btn btn-primary">Save Changes</button>
            </div>
          </form>
        </div>
      </div>
        </main>
      </div>
    </div>

    <script src="/js/employee-numbers.js"></script>
    <script src="/main.js"></script>
    <script src="/tab.js"></script>
  </body>
</html>
