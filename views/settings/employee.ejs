<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Settings - <%= company.name %></title>
    
    <!-- Modern Font -->
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <!-- Phosphor Icons -->
    <script src="https://unpkg.com/@phosphor-icons/web"></script>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/styles.css" />
    <link rel="stylesheet" href="/css/onboarding.css" />
    <link rel="stylesheet" href="/css/employeeManagement.css" />
    <link rel="stylesheet" href="/css/header.css" />
    <link rel="stylesheet" href="/css/sidebar.css" />
    <link rel="stylesheet" href="/css/settings.css" />
    <link rel="stylesheet" href="/css/accounting-settings.css" />
    <link rel="stylesheet" href="/css/mobile-nav.css" />
    <link rel="stylesheet" href="/css/feature-status.css" />
    <link rel="stylesheet" href="/css/dark-mode.css" />
  </head>

  <body>
    <div class="layout-wrapper">
      <%- include('../partials/sidebar', { user: user, company: company }) %>
      <div class="content-wrapper">
        <%- include('../partials/header', { user: user, company: company, currentPage: 'settings' }) %>

        <main class="main-container">
          <!-- Tabs Section -->
          <div id="tabs-section" style="margin-top: 6.5rem;">
        <div class="tab-row main-tabs">
          <a
            href="/clients/<%= company.companyCode %>/settings/accounting"
            class="tab-button"
          >
            <i class="ph ph-calculator"></i>
            Accounting
          </a>
          <a
            href="/clients/<%= company.companyCode %>/settings/employee"
            class="tab-button <%= activeTab === 'employee' ? 'active' : '' %>"
          >
            <i class="ph ph-users"></i>
            Employee
          </a>
          <a
            href="/clients/<%= company.companyCode %>/settings/payroll"
            class="tab-button"
          >
            <i class="ph ph-money"></i>
            Payroll
          </a>
          <a
            href="/clients/<%= company.companyCode %>/settings/other"
            class="tab-button"
          >
            <i class="ph ph-gear"></i>
            Other
          </a>
        </div>
      </div>

      <!-- Settings Grid -->
      <div class="settings-grid">
        <!-- Employee Numbers (Active) -->
        <a
          href="/clients/<%= company.companyCode %>/settings/employee/employee-numbers"
          class="settings-card"
        >
          <div class="card-header">
            <i class="ph ph-user-gear"></i>
            <h3>Employee Numbers</h3>
          </div>
          <p>Configure employee numbering system</p>
        </a>

        <!-- Employer Details (Active) -->
        <a
          href="/clients/<%= company.companyCode %>/settings/employee/employer-details"
          class="settings-card"
        >
          <div class="card-header">
            <i class="ph ph-tree-structure"></i>
            <h3>Employer Details</h3>
          </div>
          <p>Manage employer information and details</p>
        </a>

        <!-- Employer Filing Details (Active) -->
        <a
          href="/clients/<%= company.companyCode %>/settings/employee/employer-filing-details"
          class="settings-card"
        >
          <div class="card-header">
            <i class="ph ph-user-plus"></i>
            <h3>Employer Filing Details</h3>
          </div>
          <p>Configure filing and documentation settings</p>
        </a>

        <!-- Job Grades (Locked) -->
        <% if (isFeatureAvailable('JOB_GRADES_SETTINGS')) { %>
          <a
            href="/clients/<%= company.companyCode %>/settings/employee/job-grades"
            class="settings-card"
          >
            <div class="card-header">
              <i class="ph ph-files"></i>
              <h3>Job Grades</h3>
            </div>
            <p>Manage job grades and positions</p>
          </a>
        <% } else { %>
          <div class="settings-card disabled" title="Feature not available">
            <div class="card-header">
              <i class="ph ph-files"></i>
              <h3>Job Grades</h3>
            </div>
            <p>Manage job grades and positions</p>
            <div class="feature-overlay">
              <i class="ph ph-lock"></i>
            </div>
            <%- include('../partials/feature-status', { featureKey: 'JOB_GRADES_SETTINGS' }) %>
          </div>
        <% } %>
      </div>
        </main>
      </div>
    </div>

    <%- include('../partials/mobile-bottom-nav', { req: { path: `/clients/${company.companyCode}/settings/employee` }, company: company }) %>

    <script src="/include.js"></script>
    <script src="/script.js"></script>
    <script src="/main.js"></script>
    <script src="/tab.js"></script>
  </body>
</html>
