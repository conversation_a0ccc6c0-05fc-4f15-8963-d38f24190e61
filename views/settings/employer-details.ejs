<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <title>
      <%= newCompany === 'true' ? 'Add New Company' : 'Settings - ' + (company
      && company.name ? company.name : 'Company') %>
    </title>

    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://unpkg.com/@phosphor-icons/web/src/regular/style.css">
    <link rel="stylesheet" href="/css/styles.css" />
    <link rel="stylesheet" href="/css/onboarding.css" />
    <link rel="stylesheet" href="/css/employeeManagement.css" />
    <link rel="stylesheet" href="/css/header.css" />
    <link rel="stylesheet" href="/css/sidebar.css" />
    <link rel="stylesheet" href="/css/settings.css" />
    <link rel="stylesheet" href="/css/employee-numbers.css" />   
    <link rel="stylesheet" href="/css/mobile-header.css" />
    <link rel="stylesheet" href="/css/mobile-employee-profile.css" />
    <link rel="stylesheet" href="/css/mobile-nav.css" />
    <link rel="stylesheet" href="/css/notifications.css" />

 
    <style>
      /* Page Specific Styles */
      .employer-container {
        padding: 2rem;
        max-width: 1200px;
        margin: 0 auto;
      }

      .title-section {
        margin-bottom: 2rem;
      }

      .banner-content {
        display: flex;
        align-items: center;
        gap: 1rem;
      }

      .banner-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        background: rgba(139, 92, 246, 0.08);
        border-radius: 8px;
        color: #8B5CF6;
      }

      .banner-text h1 {
        font-size: 1.5rem;
        font-weight: 600;
        color: #1e293b;
        margin: 0;
      }

      .banner-subtitle {
        color: #64748b;
        margin: 0.25rem 0 0 0;
      }

      .employer-sections-grid {
        display: grid;
        gap: 2rem;
      }

      .employer-section {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        border: 1px solid #e2e8f0;
      }

      .section-header {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1.5rem;
        color: #1e293b;
        font-weight: 600;
      }

      .section-header i {
        color: #6366f1;
        font-size: 1.25rem;
      }

      .form-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
      }

      .form-group {
        margin-bottom: 1.5rem;
      }

      .form-group.full-width {
        grid-column: 1 / -1;
      }

      .form-group label {
        display: block;
        margin-bottom: 0.5rem;
        color: #1e293b;
        font-weight: 500;
        font-size: 0.875rem;
      }

      .form-group input,
      .form-group select,
      .form-group textarea {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        font-size: 0.875rem;
        font-family: 'Inter', sans-serif;
        color: #1e293b;
        transition: all 0.2s ease;
      }

      .form-group input:focus,
      .form-group select:focus,
      .form-group textarea:focus {
        border-color: #6366f1;
        box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
        outline: none;
      }

      .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 1rem;
        margin-top: 2rem;
      }

      /* File input styling */
      .file-input {
        padding: 0.5rem !important;
      }

      .file-input::-webkit-file-upload-button {
        padding: 0.5rem 1rem;
        margin-right: 1rem;
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 6px;
        color: #1e293b;
        font-family: 'Inter', sans-serif;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .file-input::-webkit-file-upload-button:hover {
        background: #f1f5f9;
      }

      .current-logo {
        margin-top: 1rem;
        padding: 1rem;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        background: #f8fafc;
      }

      .current-logo img {
        max-width: 200px;
        height: auto;
        display: block;
      }

      .mt-4 {
        margin-top: 2rem;
      }

      .message {
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
      }

      .message.success {
        background-color: #f0fdf4;
        color: #166534;
        border: 1px solid #bbf7d0;
      }

      .message.error {
        background-color: #fef2f2;
        color: #991b1b;
        border: 1px solid #fecaca;
      }

      /* Loading and Toast Styles */
      .form-loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
      }

      .form-loading-overlay.show {
        opacity: 1;
        visibility: visible;
      }

      .loading-content {
        background: white;
        padding: 3rem;
        border-radius: 16px;
        text-align: center;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        max-width: 400px;
        width: 90%;
      }

      .loading-spinner {
        width: 48px;
        height: 48px;
        border: 4px solid #e5e7eb;
        border-top: 4px solid #6366f1;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 1.5rem;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .loading-text {
        font-size: 1.25rem;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 0.5rem;
      }

      .loading-subtext {
        color: #64748b;
        font-size: 0.875rem;
      }

      .toast-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10001;
        display: flex;
        flex-direction: column;
        gap: 10px;
      }

      .toast {
        background: white;
        border-radius: 8px;
        padding: 1rem 1.5rem;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        gap: 0.75rem;
        min-width: 300px;
        transform: translateX(100%);
        opacity: 0;
        transition: all 0.3s ease;
        border-left: 4px solid;
      }

      .toast.show {
        transform: translateX(0);
        opacity: 1;
      }

      .toast-success {
        border-left-color: #10b981;
        color: #065f46;
      }

      .toast-success i {
        color: #10b981;
        font-size: 1.25rem;
      }

      .toast-error {
        border-left-color: #ef4444;
        color: #991b1b;
      }

      .toast-error i {
        color: #ef4444;
        font-size: 1.25rem;
      }

      .btn.loading {
        position: relative;
        color: transparent !important;
      }

      .btn.loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 20px;
        height: 20px;
        border: 2px solid transparent;
        border-top: 2px solid currentColor;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        color: white;
      }

      .file-upload-status {
        margin-top: 0.5rem;
        padding: 0.5rem;
        border-radius: 6px;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .file-upload-status.uploading {
        background: #f0f9ff;
        color: #0369a1;
        border: 1px solid #bae6fd;
      }

      .file-upload-status.success {
        background: #f0fdf4;
        color: #166534;
        border: 1px solid #bbf7d0;
      }

      .file-upload-status.error {
        background: #fef2f2;
        color: #991b1b;
        border: 1px solid #fecaca;
      }

      @media (max-width: 768px) {
        .form-grid {
          grid-template-columns: 1fr;
        }

        .employer-container {
          padding: 1rem;
        }

        .toast-container {
          top: 10px;
          right: 10px;
          left: 10px;
        }

        .toast {
          min-width: auto;
        }

        .loading-content {
          padding: 2rem;
        }
      }
    </style>
  </head>

  <body>
    <%- include('../partials/mobile-bottom-nav', { req: req }) %>

    <div class="layout-wrapper">
      <%- include('../partials/sidebar', { user: user, company: company }) %>
      <div class="content-wrapper">
        <%- include('../partials/header', { user: user, company: company, currentPage: 'settings' }) %>

        <main>
          <div class="employer-container">
            <!-- Navigation -->
            <div id="tabs-section" style="margin-top: 6.5rem;">
          <div class="tab-row">
            <a href="/clients/<%= company.companyCode %>/settings/accounting" class="tab-button">
              <i class="ph ph-calculator"></i>
              <span>Accounting</span>
            </a>
            <a href="/clients/<%= company.companyCode %>/settings/employee" class="tab-button <%= activeTab === 'employee' ? 'active' : '' %>">
              <i class="ph ph-users"></i>
              <span>Employee</span>
            </a>
            <a href="/clients/<%= company.companyCode %>/settings/payroll" class="tab-button">
              <i class="ph ph-money"></i>
              <span>Payroll</span>
            </a>
            <a href="/clients/<%= company.companyCode %>/settings/other" class="tab-button">
              <i class="ph ph-gear"></i>
              <span>Other</span>
            </a>
          </div>
        </div>

        <div id="successMessage" class="message success" style="display: none">
          Settings updated successfully!
        </div>

        <div id="errorMessage" class="message error" style="display: none">
          An error occurred. Please try again.
        </div>

        <div class="employer-sections-grid">
          <div class="employer-section">
            <form
              id="employerDetailsForm"
              action="/clients/<%= company.companyCode %>/settings/employee/employer-details"
              method="POST"
            >
              <input type="hidden" name="_csrf" value="<%= csrfToken %>" />
              <input type="hidden" name="companyId" value="<%= company._id %>" />

              <div class="section-header">
                <i class="ph ph-buildings"></i>
                <span>Company Information</span>
              </div>

              <div class="form-grid">
                <div class="form-group">
                  <label>Trading Name</label>
                  <input
                    type="text"
                    name="tradingName"
                    value="<%= employerDetails?.tradingName || '' %>"
                    required
                  />
                </div>

                <div class="form-group">
                  <label>Company Logo</label>
                  <input
                    type="file"
                    id="logoFile"
                    name="logo"
                    accept="image/jpeg,image/png,image/gif,image/webp"
                    class="file-input"
                  />
                  <input type="hidden" id="logoPath" name="logoPath" value="" />
                  <small class="form-text text-muted">
                    Accepted formats: JPG, PNG, GIF, WebP. Maximum size: 5MB
                  </small>
                  <div id="fileUploadStatus" class="file-upload-status" style="display: none;"></div>
                  <% if (employerDetails?.logo) { %>
                    <div class="current-logo">
                      <img src="<%= employerDetails.logo %>" alt="Company Logo" style="max-width: 200px; margin-top: 10px;"/>
                    </div>
                  <% } %>
                </div>
              </div>

              <div class="section-header mt-4">
                <i class="ph ph-map-pin"></i>
                <span>Physical Address</span>
              </div>

              <div class="form-grid">
                <div class="form-group">
                  <label>Unit Number</label>
                  <input
                    type="text"
                    name="physicalAddress[unitNumber]"
                    value="<%= employerDetails?.physicalAddress?.unitNumber || '' %>"
                  />
                </div>

                <div class="form-group">
                  <label>Complex</label>
                  <input
                    type="text"
                    name="physicalAddress[complex]"
                    value="<%= employerDetails?.physicalAddress?.complex || '' %>"
                  />
                </div>

                <div class="form-group">
                  <label>Street Number</label>
                  <input
                    type="text"
                    name="physicalAddress[streetNumber]"
                    value="<%= employerDetails?.physicalAddress?.streetNumber || '' %>"
                  />
                </div>

                <div class="form-group">
                  <label>Street</label>
                  <input
                    type="text"
                    name="physicalAddress[street]"
                    value="<%= employerDetails?.physicalAddress?.street || '' %>"
                  />
                </div>

                <div class="form-group">
                  <label>Suburb/District</label>
                  <input
                    type="text"
                    name="physicalAddress[suburbDistrict]"
                    value="<%= employerDetails?.physicalAddress?.suburbDistrict || '' %>"
                  />
                </div>

                <div class="form-group">
                  <label>City/Town</label>
                  <input
                    type="text"
                    name="physicalAddress[cityTown]"
                    value="<%= employerDetails?.physicalAddress?.cityTown || '' %>"
                  />
                </div>

                <div class="form-group">
                  <label>Postal Code</label>
                  <input
                    type="text"
                    name="physicalAddress[code]"
                    value="<%= employerDetails?.physicalAddress?.code || '' %>"
                  />
                </div>
              </div>

              <div class="section-header mt-4">
                <i class="ph ph-envelope"></i>
                <span>Postal Address</span>
              </div>

              <div class="form-grid">
                <div class="form-group">
                  <label>Address Line 1</label>
                  <input
                    type="text"
                    name="postalAddress[line1]"
                    value="<%= employerDetails?.postalAddress?.line1 || '' %>"
                  />
                </div>

                <div class="form-group">
                  <label>Address Line 2</label>
                  <input
                    type="text"
                    name="postalAddress[line2]"
                    value="<%= employerDetails?.postalAddress?.line2 || '' %>"
                  />
                </div>

                <div class="form-group">
                  <label>Address Line 3</label>
                  <input
                    type="text"
                    name="postalAddress[line3]"
                    value="<%= employerDetails?.postalAddress?.line3 || '' %>"
                  />
                </div>

                <div class="form-group">
                  <label>Postal Code</label>
                  <input
                    type="text"
                    name="postalAddress[code]"
                    value="<%= employerDetails?.postalAddress?.code || '' %>"
                  />
                </div>
              </div>

              <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                  Save Changes
                </button>
              </div>
            </form>
          </div>
        </div>
          </div>
        </main>
      </div>
    </div>


    <script>
      class EmployerDetailsForm {
        constructor() {
          this.form = document.getElementById("employerDetailsForm");
          this.logoFileInput = document.getElementById("logoFile");
          this.logoPathInput = document.getElementById("logoPath");
          this.fileUploadStatus = document.getElementById("fileUploadStatus");
          this.csrfToken = document.querySelector('input[name="_csrf"]')?.value;
          this.isSubmitting = false;
          this.uploadedFilePath = null;

          this.initializeEventListeners();
        }

        initializeEventListeners() {
          // Handle form submission
          this.form?.addEventListener("submit", async (e) => {
            e.preventDefault();
            await this.handleSubmit(e);
          });

          // Handle file upload
          this.logoFileInput?.addEventListener("change", async (e) => {
            if (e.target.files.length > 0) {
              await this.handleFileUpload(e.target.files[0]);
            }
          });
        }

        async handleFileUpload(file) {
          try {
            this.showFileUploadStatus("uploading", "Uploading logo...");

            const formData = new FormData();
            formData.append("logo", file);

            const companyCode = window.location.pathname.split('/')[2];
            const uploadUrl = `/api/upload/upload-logo/${companyCode}`;

            const response = await fetch(uploadUrl, {
              method: "POST",
              body: formData,
            });

            const result = await response.json();

            if (!response.ok || !result.success) {
              throw new Error(result.message || "File upload failed");
            }

            // Store the uploaded file path
            this.uploadedFilePath = result.file.path;
            this.logoPathInput.value = result.file.path;

            this.showFileUploadStatus("success", `✓ ${result.file.originalname} uploaded successfully`);
            this.showToast("Logo uploaded successfully", "success");

          } catch (error) {
            console.error("File upload error:", error);
            this.showFileUploadStatus("error", `✗ Upload failed: ${error.message}`);
            this.showToast(error.message || "File upload failed", "error");

            // Clear the file input and path
            this.logoFileInput.value = "";
            this.logoPathInput.value = "";
            this.uploadedFilePath = null;
          }
        }

        showFileUploadStatus(type, message) {
          this.fileUploadStatus.className = `file-upload-status ${type}`;
          this.fileUploadStatus.textContent = message;
          this.fileUploadStatus.style.display = "flex";
        }

        async handleSubmit(e) {
          e.preventDefault();

          if (this.isSubmitting) {
            return;
          }

          this.isSubmitting = true;

          // Debug form state before collection
          console.log("Form element:", this.form);
          console.log("Form inputs count:", this.form.querySelectorAll('input, select, textarea').length);

          // Check specific required fields
          const tradingNameInput = this.form.querySelector('input[name="tradingName"]');
          console.log("Trading name input:", tradingNameInput ? tradingNameInput.value : 'Not found');

          // Collect form data BEFORE showing loading state (which disables inputs)
          const formData = new FormData(this.form);
          console.log("Raw FormData entries:");
          for (let [key, value] of formData.entries()) {
            console.log(`  ${key}: ${value}`);
          }

          this.showLoadingState();

          try {

            // Convert FormData to regular object for JSON submission
            const requestData = {};
            for (let [key, value] of formData.entries()) {
              // Skip file inputs but include all other fields (even empty ones for debugging)
              if (key !== 'logo') {
                requestData[key] = value;
                console.log(`Processing field: ${key} = "${value}" (${typeof value})`);
              }
            }

            // Include uploaded file path if available
            if (this.uploadedFilePath) {
              requestData.logoPath = this.uploadedFilePath;
            }

            console.log("Form submission data:", requestData);
            console.log("Form fields count:", Object.keys(requestData).length);
            console.log("Required fields check:", {
              tradingName: requestData.tradingName ? 'Present' : 'Missing',
              physicalAddress: Object.keys(requestData).filter(k => k.startsWith('physicalAddress')).length,
              postalAddress: Object.keys(requestData).filter(k => k.startsWith('postalAddress')).length
            });

            const response = await fetch(this.form.action, {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                "CSRF-Token": this.csrfToken,
              },
              body: JSON.stringify(requestData),
            });

            const result = await response.json();

            if (!response.ok || !result.success) {
              throw new Error(result.message || "Failed to save employer details");
            }

            this.showSuccessState();
            this.showToast(result.message || "Employer details saved successfully", "success");

            // Hide loading after success message
            setTimeout(() => {
              this.hideLoadingState();
            }, 2000);

          } catch (error) {
            console.error("Submission error:", error);
            this.showToast(error.message || "Failed to save employer details", "error");
            this.hideLoadingState();
          }
        }

        showLoadingState() {
          this.createLoadingOverlay();

          const submitButton = this.form.querySelector('button[type="submit"]');
          if (submitButton) {
            submitButton.disabled = true;
            submitButton.classList.add('loading');

            if (!submitButton.dataset.originalContent) {
              submitButton.dataset.originalContent = submitButton.innerHTML;
            }
          }

          // Disable form inputs
          const inputs = this.form.querySelectorAll('input, select, textarea');
          inputs.forEach(input => {
            if (!input.disabled) {
              input.dataset.wasEnabled = 'true';
              input.disabled = true;
            }
          });
        }

        showSuccessState() {
          const loadingOverlay = document.querySelector('.form-loading-overlay');
          if (loadingOverlay) {
            const loadingContent = loadingOverlay.querySelector('.loading-content');
            if (loadingContent) {
              loadingContent.innerHTML = `
                <div style="color: #10b981; font-size: 48px; margin-bottom: 1rem;">
                  <i class="ph ph-check-circle"></i>
                </div>
                <div class="loading-text" style="color: #10b981;">Success!</div>
                <div class="loading-subtext">Employer details saved successfully</div>
              `;
            }
          }

          const submitButton = this.form.querySelector('button[type="submit"]');
          if (submitButton) {
            submitButton.classList.remove('loading');
            submitButton.innerHTML = '<i class="ph ph-check"></i> Saved Successfully';
            submitButton.style.backgroundColor = '#10b981';
          }
        }

        hideLoadingState() {
          const loadingOverlay = document.querySelector('.form-loading-overlay');
          if (loadingOverlay) {
            loadingOverlay.classList.remove('show');
            setTimeout(() => {
              loadingOverlay.remove();
            }, 300);
          }

          const submitButton = this.form.querySelector('button[type="submit"]');
          if (submitButton) {
            submitButton.disabled = false;
            submitButton.classList.remove('loading');
            submitButton.style.backgroundColor = '';

            if (submitButton.dataset.originalContent) {
              submitButton.innerHTML = submitButton.dataset.originalContent;
              delete submitButton.dataset.originalContent;
            }
          }

          // Re-enable form inputs
          const inputs = this.form.querySelectorAll('input, select, textarea');
          inputs.forEach(input => {
            if (input.dataset.wasEnabled === 'true') {
              input.disabled = false;
              delete input.dataset.wasEnabled;
            }
          });

          this.isSubmitting = false;
        }

        createLoadingOverlay() {
          const existingOverlay = document.querySelector('.form-loading-overlay');
          if (existingOverlay) {
            existingOverlay.remove();
          }

          const overlay = document.createElement('div');
          overlay.className = 'form-loading-overlay';
          overlay.innerHTML = `
            <div class="loading-content">
              <div class="loading-spinner"></div>
              <div class="loading-text">Saving Changes...</div>
              <div class="loading-subtext">Please don't refresh or navigate away</div>
            </div>
          `;

          document.body.appendChild(overlay);

          setTimeout(() => {
            overlay.classList.add('show');
          }, 10);
        }

        showToast(message, type = 'info') {
          let toastContainer = document.querySelector('.toast-container');
          if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container';
            document.body.appendChild(toastContainer);
          }

          const icons = {
            success: 'ph-check-circle',
            error: 'ph-x-circle',
            info: 'ph-info',
            warning: 'ph-warning'
          };

          const toast = document.createElement('div');
          toast.className = `toast toast-${type}`;
          toast.innerHTML = `
            <i class="ph ${icons[type] || icons.info}"></i>
            <span>${message}</span>
          `;

          toastContainer.appendChild(toast);

          setTimeout(() => {
            toast.classList.add('show');
          }, 10);

          setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
              toast.remove();
              if (toastContainer.children.length === 0) {
                toastContainer.remove();
              }
            }, 300);
          }, 4000);
        }
      }

      document.addEventListener("DOMContentLoaded", function () {
        new EmployerDetailsForm();
      });
    </script>
  </body>
</html>
