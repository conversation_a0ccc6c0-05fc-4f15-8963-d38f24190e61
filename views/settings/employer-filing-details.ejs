<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Settings - <%= company.name %></title>

    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://unpkg.com/@phosphor-icons/web/src/regular/style.css">
    <link rel="stylesheet" href="/css/styles.css" />
    <link rel="stylesheet" href="/css/onboarding.css" />
    <link rel="stylesheet" href="/css/employeeManagement.css" />
    <link rel="stylesheet" href="/css/header.css" />
    <link rel="stylesheet" href="/css/sidebar.css" />
    <link rel="stylesheet" href="/css/settings.css" />
    <link rel="stylesheet" href="/css/employee-numbers.css" />    
    
    <meta name="company-code" content="<%= company.companyCode %>" />
    
    <style>
      /* Page Specific Styles */
      .filing-container {
        padding: 2rem;
        max-width: 1200px;
        margin: 0 auto;
      }

      .filing-section {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 1px solid #e2e8f0;
      }

      .section-header {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1.5rem;
        color: #1e293b;
        font-weight: 600;
      }

      .section-header i {
        color: #8B5CF6;
        font-size: 1.25rem;
      }

      .form-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
      }

      .form-group {
        margin-bottom: 1.5rem;
      }

      .form-group label {
        display: block;
        margin-bottom: 0.5rem;
        color: #1e293b;
        font-weight: 500;
        font-size: 0.875rem;
      }

      .form-group input,
      .form-group select {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        font-size: 0.875rem;
        font-family: 'Inter', sans-serif;
        color: #1e293b;
        transition: all 0.2s ease;
      }

      .form-group input:focus,
      .form-group select:focus {
        border-color: #6366f1;
        box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
        outline: none;
      }

      .form-group select:disabled {
        background-color: #f8fafc;
        cursor: not-allowed;
      }

      .form-group small {
        display: block;
        margin-top: 0.25rem;
        color: #64748b;
        font-size: 0.75rem;
      }

      .checkbox-group {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
      }

      .checkbox-group input[type="checkbox"] {
        width: 1rem;
        height: 1rem;
        border: 1px solid #e2e8f0;
        border-radius: 4px;
        cursor: pointer;
      }

      .checkbox-group label {
        color: #1e293b;
        font-size: 0.875rem;
      }

      .alert {
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
      }

      .alert-success {
        background-color: #f0fdf4;
        color: #166534;
        border: 1px solid #bbf7d0;
      }

      .alert-danger {
        background-color: #fef2f2;
        color: #991b1b;
        border: 1px solid #fecaca;
      }

      .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 1rem;
        margin-top: 2rem;
      }

      @media (max-width: 768px) {
        .form-grid {
          grid-template-columns: 1fr;
        }

        .filing-container {
          padding: 1rem;
        }
      }
    </style>
  </head>

  <body>
    <div class="layout-wrapper">
      <%- include('../partials/sidebar', { user: user, company: company }) %>
      <div class="content-wrapper">
        <%- include('../partials/header', { user: user, company: company, currentPage: 'settings' }) %>

        <main class="main-container">
          <div class="filing-container">
            <!-- Navigation -->
            <div id="tabs-section" style="margin-top: 6.5rem;">
          <div class="tab-row">
            <a href="/clients/<%= company.companyCode %>/settings/accounting" class="tab-button">
              <i class="ph ph-calculator"></i>
              <span>Accounting</span>
            </a>
            <a href="/clients/<%= company.companyCode %>/settings/employee" class="tab-button <%= activeTab === 'employee' ? 'active' : '' %>">
              <i class="ph ph-users"></i>
              <span>Employee</span>
            </a>
            <a href="/clients/<%= company.companyCode %>/settings/payroll" class="tab-button">
              <i class="ph ph-money"></i>
              <span>Payroll</span>
            </a>
            <a href="/clients/<%= company.companyCode %>/settings/other" class="tab-button">
              <i class="ph ph-gear"></i>
              <span>Other</span>
            </a>
          </div>
        </div>

        <% if (messages && messages.success) { %>
        <div class="alert alert-success">
          <i class="ph ph-check-circle"></i>
          <%= messages.success %>
        </div>
        <% } %> 
        
        <% if (messages && messages.error) { %>
        <div class="alert alert-danger">
          <i class="ph ph-x-circle"></i>
          <%= messages.error %>
        </div>
        <% } %>

        <form
          id="employerFilingDetailsForm"
          action="/clients/<%= company.companyCode %>/settings/employee/employer-filing-details"
          method="POST"
        >
          <input type="hidden" name="_csrf" value="<%= csrfToken %>" />

          <!-- PAYE Section -->
          <div class="filing-section">
            <h3 class="section-header">
              <i class="ph ph-file-text"></i>
              PAYE Information
            </h3>
            <div class="form-group">
              <label for="payeNumber">PAYE number</label>
              <input
                type="text"
                id="payeNumber"
                name="payeNumber"
                required
                value="<%= employerFilingDetails.payeNumber || '' %>"
              />
            </div>

            <div class="checkbox-group">
              <input
                type="checkbox"
                id="diplomaticIndemnity"
                name="diplomaticIndemnity"
                <%= employerFilingDetails.diplomaticIndemnity ? 'checked' : '' %>
              />
              <label for="diplomaticIndemnity">Diplomatic indemnity indicator</label>
            </div>
          </div>

          <!-- SIC Codes Section -->
          <div class="filing-section">
            <h3 class="section-header">
              <i class="ph ph-git-fork"></i>
              SIC Classification
            </h3>
            <div class="form-grid">
              <div class="form-group">
                <label for="sicMainGroup">SIC Main Group</label>
                <select id="sicMainGroup" name="sicMainGroup">
                  <option value="">Select SIC Main Group</option>
                </select>
              </div>

              <div class="form-group">
                <label for="sicLevel2">SIC Level 2</label>
                <select id="sicLevel2" name="sicLevel2" disabled>
                  <option value="">Select SIC Level 2</option>
                </select>
              </div>

              <div class="form-group">
                <label for="sicLevel3">SIC Level 3</label>
                <select id="sicLevel3" name="sicLevel3" disabled>
                  <option value="">Select SIC Level 3</option>
                </select>
              </div>

              <div class="form-group">
                <label for="sicLevel4">SIC Level 4</label>
                <select id="sicLevel4" name="sicLevel4" disabled>
                  <option value="">Select SIC Level 4</option>
                </select>
              </div>

              <div class="form-group">
                <label for="sicCode">SIC Code</label>
                <select id="sicCode" name="sicCode" disabled>
                  <option value="">Select SIC Code</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Contact Information Section -->
          <div class="filing-section">
            <h3 class="section-header">
              <i class="ph ph-user"></i>
              Contact Information
            </h3>
            <div class="form-grid">
              <div class="form-group">
                <label for="telephoneNumber">Telephone Number</label>
                <input
                  type="tel"
                  id="telephoneNumber"
                  name="telephoneNumber"
                  value="<%= employerFilingDetails.telephoneNumber || '' %>"
                />
                <small>Will be submitted as business tel. no. for all employees</small>
              </div>

              <div class="form-group">
                <label for="sarsContactName">SARS Contact Name</label>
                <input
                  type="text"
                  id="sarsContactName"
                  name="sarsContactName"
                  value="<%= employerFilingDetails.sarsContactName || '' %>"
                />
                <small>Name of contact person in case SARS has any queries</small>
              </div>

              <div class="form-group">
                <label for="sarsContactSurname">SARS Contact Surname</label>
                <input
                  type="text"
                  id="sarsContactSurname"
                  name="sarsContactSurname"
                  value="<%= employerFilingDetails.sarsContactSurname || '' %>"
                />
              </div>

              <div class="form-group">
                <label for="sarsContactPosition">SARS Contact Position</label>
                <input
                  type="text"
                  id="sarsContactPosition"
                  name="sarsContactPosition"
                  value="<%= employerFilingDetails.sarsContactPosition || '' %>"
                />
              </div>
            </div>
          </div>

          <div class="form-actions">
            <button type="submit" class="primary-button">
              <i class="ph ph-floppy-disk"></i>
              Save Changes
            </button>
          </div>
        </form>
          </div>
        </main>
      </div>
    </div>

    <script src="/js/sic-codes.js"></script>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const sicMainGroup = document.getElementById("sicMainGroup");
        const sicLevel2 = document.getElementById("sicLevel2");
        const sicLevel3 = document.getElementById("sicLevel3");
        const sicLevel4 = document.getElementById("sicLevel4");
        const sicCode = document.getElementById("sicCode");

        // Populate SIC Main Group
        for (const [key, value] of Object.entries(sicCodes)) {
          const option = document.createElement("option");
          option.value = key;
          option.textContent = `${key} - ${value.name}`;
          sicMainGroup.appendChild(option);
        }

        sicMainGroup.addEventListener("change", function () {
          populateNextLevel(this.value, sicLevel2, "level2");
          resetLowerLevels(sicLevel2);
        });

        sicLevel2.addEventListener("change", function () {
          populateNextLevel(
            sicMainGroup.value + "-" + this.value,
            sicLevel3,
            "level3"
          );
          resetLowerLevels(sicLevel3);
        });

        sicLevel3.addEventListener("change", function () {
          populateNextLevel(
            sicMainGroup.value + "-" + sicLevel2.value + "-" + this.value,
            sicLevel4,
            "level4"
          );
          resetLowerLevels(sicLevel4);
        });

        sicLevel4.addEventListener("change", function () {
          populateNextLevel(
            sicMainGroup.value +
              "-" +
              sicLevel2.value +
              "-" +
              sicLevel3.value +
              "-" +
              this.value,
            sicCode,
            "code"
          );
        });

        function populateNextLevel(selectedValue, nextLevelElement, level) {
          nextLevelElement.innerHTML =
            '<option value="">Select ' + level.toUpperCase() + "</option>";

          if (selectedValue) {
            let options = getOptions(selectedValue, level);
            for (const [key, value] of Object.entries(options)) {
              const option = document.createElement("option");
              option.value = key;
              option.textContent = `${key} - ${value.name || value}`;
              nextLevelElement.appendChild(option);
            }
            nextLevelElement.disabled = false;
          } else {
            nextLevelElement.disabled = true;
          }
        }

        function resetLowerLevels(startElement) {
          let current = startElement.nextElementSibling;
          while (current && current.tagName === "SELECT") {
            current.innerHTML =
              '<option value="">Select ' +
              current.id.replace("sic", "").toUpperCase() +
              "</option>";
            current.disabled = true;
            current = current.nextElementSibling;
          }
        }

        function getOptions(selectedValue, level) {
          let options = sicCodes;
          const levels = ["mainGroup", "level2", "level3", "level4"];
          const selectedLevel = levels.indexOf(level);
          const parts = selectedValue.split("-");

          for (let i = 0; i < selectedLevel; i++) {
            if (options[parts[i]]) {
              options = options[parts[i]][levels[i + 1]] || {};
            } else {
              return {};
            }
          }

          return options;
        }

        // Set the saved values for SIC codes
        const savedSicMainGroup =
          "<%= employerFilingDetails.sicMainGroup || '' %>";
        const savedSicLevel2 = "<%= employerFilingDetails.sicLevel2 || '' %>";
        const savedSicLevel3 = "<%= employerFilingDetails.sicLevel3 || '' %>";
        const savedSicLevel4 = "<%= employerFilingDetails.sicLevel4 || '' %>";
        const savedSicCode = "<%= employerFilingDetails.sicCode || '' %>";

        if (savedSicMainGroup) {
          sicMainGroup.value = savedSicMainGroup;
          sicMainGroup.dispatchEvent(new Event("change"));
        }

        if (savedSicLevel2) {
          setTimeout(() => {
            sicLevel2.value = savedSicLevel2;
            sicLevel2.dispatchEvent(new Event("change"));
          }, 100);
        }

        if (savedSicLevel3) {
          setTimeout(() => {
            sicLevel3.value = savedSicLevel3;
            sicLevel3.dispatchEvent(new Event("change"));
          }, 200);
        }

        if (savedSicLevel4) {
          setTimeout(() => {
            sicLevel4.value = savedSicLevel4;
            sicLevel4.dispatchEvent(new Event("change"));
          }, 300);
        }

        if (savedSicCode) {
          setTimeout(() => {
            sicCode.value = savedSicCode;
          }, 400);
        }
      });
    </script>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const form = document.getElementById("employerFilingDetailsForm");

        if (form) {
          form.addEventListener("submit", async function (e) {
            e.preventDefault();

            try {
              const formData = new FormData(this);
              const formObject = {};
              formData.forEach((value, key) => {
                formObject[key] = value;
              });

              // Add special handling for checkbox fields
              formObject.diplomaticIndemnity = document.getElementById(
                "diplomaticIndemnity"
              ).checked;

              // Structure the postal address
              // Removed postal address fields as they were not present in the updated code

              const response = await fetch(this.action, {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                  "X-Requested-With": "XMLHttpRequest",
                },
                body: JSON.stringify(formObject),
              });

              if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
              }

              const result = await response.json();

              if (result.success) {
                showAlert(
                  "success",
                  result.message || "Settings saved successfully"
                );
                setTimeout(() => {
                  window.location.reload();
                }, 1500);
              } else {
                showAlert("error", result.message || "Failed to save settings");
              }
            } catch (error) {
              console.error("Error:", error);

              // Show error message
              const errorMessage = document.getElementById("errorMessage");
              const errorText = document.getElementById("errorText");
              errorText.textContent = error.message;
              errorMessage.style.display = "block";

              // Hide error message after 5 seconds
              setTimeout(() => {
                errorMessage.style.display = "none";
              }, 5000);
            }
          });
        }

        // Add event listener for "Same as Residential" checkbox
        // Removed as the checkbox was not present in the updated code
      });

      function showAlert(type, message) {
        const alertElement = document.getElementById(`${type}Message`);
        const textElement = document.getElementById(`${type}Text`);

        if (alertElement && textElement) {
          textElement.textContent = message;
          alertElement.style.display = "block";

          // Auto hide after 3 seconds
          setTimeout(() => {
            alertElement.classList.add("fade-out");
            setTimeout(() => {
              alertElement.style.display = "none";
              alertElement.classList.remove("fade-out");
            }, 500);
          }, 3000);
        }
      }
    </script>
  </body>
</html>
