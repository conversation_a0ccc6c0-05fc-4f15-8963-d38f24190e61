<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Settings | <%= company.name %></title>

    <!-- Prevent iOS text size adjust -->
    <meta name="format-detection" content="telephone=no">
    
    <!-- Ensure proper rendering on all devices -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <!-- Modern Font -->
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <!-- Phosphor Icons -->
    <script src="https://unpkg.com/@phosphor-icons/web"></script>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/styles.css" />
    <link rel="stylesheet" href="/css/onboarding.css" />
    <link rel="stylesheet" href="/css/settings.css" />
    <link rel="stylesheet" href="/css/header.css" />
    <link rel="stylesheet" href="/css/mobile-nav.css" />
    <link rel="stylesheet" href="/css/sidebar.css" />
    <link rel="stylesheet" href="/css/index.css" />
    <link rel="stylesheet" href="/css/dark-mode.css" />

    <meta name="company-code" content="<%= company.companyCode %>" />
  </head>

  <body>
    <div class="layout-wrapper">
      <%- include('../partials/sidebar', { user: user, company: company }) %>
      <div class="content-wrapper">
        <%- include('../partials/header', { user: user, company: company, currentPage: 'settings' }) %>

        <main class="main-container">
          <!-- Tabs Section -->
          <div id="tabs-section" style="margin-top: 6.5rem;">
        <div class="tab-row main-tabs">
          <a
            href="/clients/<%= company.companyCode %>/settings/accounting"
            class="tab-button"
          >
            <i class="ph ph-calculator"></i>
            Accounting
          </a>
          <a
            href="/clients/<%= company.companyCode %>/settings/employee"
            class="tab-button"
          >
            <i class="ph ph-users"></i>
            Employee
          </a>
          <a
            href="/clients/<%= company.companyCode %>/settings/payroll"
            class="tab-button"
          >
            <i class="ph ph-money"></i>
            Payroll
          </a>
          <a
            href="/clients/<%= company.companyCode %>/settings/other"
            class="tab-button"
          >
            <i class="ph ph-gear"></i>
            Other
          </a>
        </div>

        <!-- Sub Tabs -->
        <div id="accounting" class="tab-content">
          <div class="tab-row sub-tabs">
            <a
              href="/clients/<%= company.companyCode %>/settings/accounting/accounting-integrations"
              class="tab-button"
            >
              <i class="ph ph-plug"></i>
              Accounting Integrations
            </a>
            <a
              href="/clients/<%= company.companyCode %>/settings/accounting/accounting-splits"
              class="tab-button"
            >
              <i class="ph ph-chart-pie"></i>
              Accounting Splits
            </a>
            <a
              href="/clients/<%= company.companyCode %>/settings/accounting/beneficiaries"
              class="tab-button"
            >
              <i class="ph ph-users-three"></i>
              Beneficiaries
            </a>
            <a
              href="/clients/<%= company.companyCode %>/settings/accounting/custom-items"
              class="tab-button"
            >
              <i class="ph ph-cube"></i>
              Custom Items
            </a>
            <a
              href="/clients/<%= company.companyCode %>/settings/accounting/eft"
              class="tab-button"
            >
              <i class="ph ph-bank"></i>
              EFT
            </a>
          </div>
        </div>

        <div id="employee" class="tab-content" style="display: none">
          <div class="tab-row sub-tabs">
            <a
              href="/clients/<%= company.companyCode %>/settings/employee-numbers"
              class="tab-button"
            >
              <i class="ph ph-hash"></i>
              Employee Numbers
            </a>
            <a
              href="/clients/<%= company.companyCode %>/settings/employer-details"
              class="tab-button"
            >
              <i class="ph ph-buildings"></i>
              Employer Details
            </a>
            <a
              href="/clients/<%= company.companyCode %>/settings/employer-filing-details"
              class="tab-button"
            >
              <i class="ph ph-file-text"></i>
              Employer Filing Details
            </a>
            <a
              href="/clients/<%= company.companyCode %>/settings/job-grades"
              class="tab-button"
            >
              <i class="ph ph-chart-line-up"></i>
              Job Grades
            </a>
          </div>
        </div>

        <div id="payroll" class="tab-content" style="display: none">
          <div class="tab-row sub-tabs">
            <a
              href="/clients/<%= company.companyCode %>/settings/leave"
              class="tab-button"
            >
              <i class="ph ph-calendar"></i>
              Leave
            </a>
            <a
              href="/clients/<%= company.companyCode %>/settings/pay-points"
              class="tab-button"
            >
              <i class="ph ph-map-pin"></i>
              Pay Points
            </a>
            <a
              href="/clients/<%= company.companyCode %>/settings/pay-frequencies"
              class="tab-button"
            >
              <i class="ph ph-clock"></i>
              Pay Frequencies
            </a>
            <a
              href="/clients/<%= company.companyCode %>/settings/payroll-calculations"
              class="tab-button"
            >
              <i class="ph ph-calculator"></i>
              Payroll Calculations
            </a>
            <a
              href="/clients/<%= company.companyCode %>/settings/payslip"
              class="tab-button"
            >
              <i class="ph ph-receipt"></i>
              Payslip
            </a>
          </div>
        </div>

        <div id="other" class="tab-content" style="display: none">
          <div class="tab-row sub-tabs">
            <a
              href="/clients/<%= company.companyCode %>/settings/templates"
              class="tab-button"
            >
              <i class="ph ph-file-text"></i>
              Templates
            </a>
            <a
              href="/clients/<%= company.companyCode %>/settings/advanced"
              class="tab-button"
            >
              <i class="ph ph-gear"></i>
              Advanced
            </a>
          </div>
        </div>
      </div>

      <div id="tab-content">
        <!-- Tab content will be loaded here dynamically -->
      </div>
        </main>
      </div>
    </div>

    <%- include('../partials/mobile-bottom-nav', { req: { path: `/clients/${company.companyCode}/settings` }, company: company }) %>

    <script>
      // Update this function to highlight the active tab
      function setActiveTab() {
        const currentPath = window.location.pathname;
        const mainTabs = document.querySelectorAll(".main-tab");
        mainTabs.forEach((tab) => {
          if (currentPath.includes(tab.getAttribute("href"))) {
            tab.classList.add("active");
          } else {
            tab.classList.remove("active");
          }
        });
      }

      // Call setActiveTab when the page loads
      document.addEventListener("DOMContentLoaded", setActiveTab);
    </script>
  </body>
</html>
