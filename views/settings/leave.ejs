<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Leave Management - <%= company.name %></title>

    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://unpkg.com/@phosphor-icons/web/src/regular/style.css"
    />
    <link rel="stylesheet" href="/css/styles.css" />
    <link rel="stylesheet" href="/css/onboarding.css" />
    <link rel="stylesheet" href="/css/employeeManagement.css" />
    <link rel="stylesheet" href="/css/header.css" />
    <link rel="stylesheet" href="/css/sidebar.css" />
    <link rel="stylesheet" href="/css/settings.css" />
    <link rel="stylesheet" href="/css/toast.css" />

    <meta name="company-code" content="<%= company.companyCode %>" />

    <style>
      /* Page Specific Styles */
      .leave-container {
        padding: 2rem;
        max-width: 1200px;
        margin: 0 auto;
      }

      .settings-card {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 1px solid #e2e8f0;
      }

      .button-container {
        display: flex;
        gap: 1rem;
        margin-bottom: 2rem;
      }

      .leave-types-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1.5rem;
      }

      .leave-type-card {
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        padding: 1.5rem;
        transition: all 0.2s;
      }

      .leave-type-card:hover {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
      }

      .leave-type-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
      }

      .leave-type-info h3 {
        margin: 0;
        color: #1e293b;
        font-size: 1.1rem;
        font-weight: 500;
      }

      .leave-type-description {
        margin: 0.5rem 0;
        color: #64748b;
        font-size: 0.875rem;
      }

      .leave-type-stats {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        margin: 1rem 0;
      }

      .stat-item {
        background: #f8fafc;
        padding: 0.75rem;
        border-radius: 6px;
      }

      .stat-label {
        font-size: 0.75rem;
        color: #64748b;
        margin-bottom: 0.25rem;
      }

      .stat-value {
        font-size: 1rem;
        color: #1e293b;
        font-weight: 500;
      }

      .leave-type-actions {
        display: flex;
        gap: 0.5rem;
        margin-top: 1rem;
      }

      .action-button {
        padding: 0.5rem;
        border: 1px solid #e2e8f0;
        border-radius: 6px;
        background: transparent;
        color: #64748b;
        cursor: pointer;
        transition: all 0.2s;
      }

      .action-button:hover {
        background: #f1f5f9;
        color: #4f46e5;
      }

      /* Modal Styles */
      .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1000;
      }

      .modal-content {
        position: relative;
        background: white;
        margin: 2rem auto;
        padding: 2rem;
        max-width: 600px;
        max-height: 80vh;
        overflow-y: auto;
        border-radius: 12px;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
      }

      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
      }

      .modal-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #1e293b;
      }

      .close-modal {
        background: none;
        border: none;
        font-size: 1.5rem;
        color: #64748b;
        cursor: pointer;
      }

      .form-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
      }

      .form-field {
        margin-bottom: 1rem;
      }

      .form-field label {
        display: block;
        margin-bottom: 0.5rem;
        color: #475569;
        font-size: 0.875rem;
      }

      .form-field input,
      .form-field select,
      .form-field textarea {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid #e2e8f0;
        border-radius: 6px;
        font-size: 0.875rem;
      }

      .form-field.full-width {
        grid-column: span 2;
      }

      .checkbox-field {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
      }

      .checkbox-field input[type="checkbox"] {
        width: auto;
      }

      .modal-footer {
        display: flex;
        justify-content: flex-end;
        gap: 1rem;
        margin-top: 2rem;
        padding-top: 1rem;
        border-top: 1px solid #e2e8f0;
      }

      /* Custom leave type field styling */
      #customNameField {
        transition: all 0.3s ease;
      }

      #customNameField input {
        border: 2px solid #e2e8f0;
        transition: border-color 0.2s ease;
      }

      #customNameField input:focus {
        border-color: #6366f1;
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        outline: none;
      }

      #customNameField input:invalid {
        border-color: #ef4444;
      }

      /* Animation for new cards */
      .leave-type-card.new-card {
        animation: slideInUp 0.3s ease-out;
      }

      @keyframes slideInUp {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* Status badge animations */
      .status-badge {
        transition: all 0.3s ease;
      }

      .status-badge.active {
        background-color: #dcfce7;
        color: #166534;
        border: 1px solid #bbf7d0;
      }

      .status-badge.inactive {
        background-color: #fef2f2;
        color: #991b1b;
        border: 1px solid #fecaca;
      }

      /* Enhanced form validation styling */
      .form-field input:invalid,
      .form-field select:invalid {
        border-color: #ef4444;
        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
      }

      .form-field input:valid,
      .form-field select:valid {
        border-color: #10b981;
      }

      /* Toast notification styles */
      .toast {
        position: fixed;
        top: 20px;
        right: 20px;
        background: white;
        border-radius: 8px;
        padding: 1rem 1.5rem;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        z-index: 1000;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        min-width: 300px;
        max-width: 500px;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
        border-left: 4px solid #6366f1;
      }

      .toast.show {
        opacity: 1;
        transform: translateX(0);
      }

      .toast.success {
        border-left-color: #10b981;
        background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
        color: #166534;
      }

      .toast.error {
        border-left-color: #ef4444;
        background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
        color: #991b1b;
      }

      .toast.warning {
        border-left-color: #f59e0b;
        background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
        color: #92400e;
      }

      .toast i {
        font-size: 1.25rem;
        flex-shrink: 0;
      }

      .toast.success i {
        color: #10b981;
      }

      .toast.error i {
        color: #ef4444;
      }

      .toast.warning i {
        color: #f59e0b;
      }

      @media (max-width: 768px) {
        .leave-container {
          padding: 1rem;
        }

        .form-grid {
          grid-template-columns: 1fr;
        }

        .form-field.full-width {
          grid-column: auto;
        }

        .modal-content {
          margin: 1rem;
          padding: 1rem;
        }
      }
    </style>
  </head>

  <body>
    <div class="layout-wrapper">
      <%- include('../partials/sidebar', { user: user, company: company }) %>
      <div class="content-wrapper">
        <%- include('../partials/header', { user: user, company: company, currentPage: 'settings' }) %>

        <main class="main-container" style="margin-top: 6.5rem;">

      <div class="leave-container">
        <div class="settings-card">
          <div class="button-container">
            <button class="primary-button" id="addLeaveType">
              <i class="ph ph-plus"></i>
              Add Leave Type
            </button>
          </div>

          <div class="leave-types-grid">
            <% leaveTypes.forEach(function(leaveType) { %>
            <div class="leave-type-card" data-id="<%= leaveType._id %>">
              <div class="leave-type-header">
                <div class="leave-type-info">
                  <h3><%= leaveType.name %></h3>
                  <p class="leave-type-description">
                    <%= leaveType.description || 'No description provided' %>
                  </p>
                </div>
                <div
                  class="status-badge <%= leaveType.active ? 'active' : 'inactive' %>"
                >
                  <%= leaveType.active ? 'Active' : 'Inactive' %>
                </div>
              </div>

              <div class="leave-type-stats">
                <div class="stat-item">
                  <div class="stat-label">Days Per Year</div>
                  <div class="stat-value"><%= leaveType.daysPerYear %></div>
                </div>
                <div class="stat-item">
                  <div class="stat-label">Accrual Rate</div>
                  <div class="stat-value"><%= leaveType.accrualRate %></div>
                </div>
              </div>

              <div class="leave-type-actions">
                <button class="action-button edit-leave-type" title="Edit">
                  <i class="ph ph-pencil"></i>
                </button>
                <button
                  class="action-button toggle-status"
                  title="Toggle Status"
                >
                  <i class="ph ph-power"></i>
                </button>
                <button class="action-button view-details" title="View Details">
                  <i class="ph ph-eye"></i>
                </button>
              </div>
            </div>
            <% }); %>
          </div>
        </div>
      </div>
        </main>
      </div>
    </div>

    <!-- Leave Type Modal -->
    <div id="leaveTypeModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h2 class="modal-title">Add Leave Type</h2>
          <button class="close-modal">&times;</button>
        <!-- Add error message container -->
        <div id="leaveTypeErrorMessage" class="error-message" style="display: none; color: #e53e3e; margin-bottom: 1rem; padding: 0.5rem; background-color: #fff5f5; border-radius: 0.25rem;"></div>
        </div>

        <form id="leaveTypeForm">
          <input type="hidden" name="_csrf" value="<%= csrfToken %>" />
          <div class="form-grid">
            <div class="form-field">
              <label for="category">Leave Type Category *</label>
              <select id="category" name="category" required>
                <option value="annual">Annual Leave</option>
                <option value="sick">Sick Leave</option>
                <option value="family">Family Responsibility Leave</option>
                <option value="unpaid">Unpaid Leave</option>
                <option value="custom">Custom Leave</option>
              </select>
            </div>

            <div class="form-field" id="customNameField" style="display: none;">
              <label for="customName">Custom Leave Type Name *</label>
              <input
                type="text"
                id="customName"
                name="customName"
                placeholder="e.g., Study Leave, Paternity Leave, Compassionate Leave"
                maxlength="50"
              />
              <small style="color: #64748b; font-size: 0.75rem; margin-top: 0.25rem; display: block;">
                Enter a descriptive name for your custom leave type. This will be displayed to employees.
              </small>
            </div>

            <div class="form-field">
              <label for="daysPerYear">Default Days Per Year *</label>
              <input
                type="number"
                id="daysPerYear"
                name="daysPerYear"
                min="0"
                step="0.5"
                required
              />
            </div>

            <div class="form-field full-width">
              <label for="description">Description</label>
              <textarea id="description" name="description" rows="3"></textarea>
            </div>

            <div class="form-field">
              <label for="accrualRate">Accrual Rate</label>
              <select id="accrualRate" name="accrualRate">
                <option value="yearly">Yearly (Full Upfront)</option>
                <option value="monthly">Monthly Accrual</option>
                <option value="none">No Accrual</option>
              </select>
            </div>

            <div id="accrualSettings" style="display: none">
              <div class="form-field">
                <label for="waitingPeriodMonths">Waiting Period (Months)</label>
                <input
                  type="number"
                  id="waitingPeriodMonths"
                  name="accrualSettings.waitingPeriodMonths"
                  min="0"
                  value="0"
                />
              </div>

              <div class="checkbox-field">
                <input
                  type="checkbox"
                  id="allowAdvance"
                  name="accrualSettings.allowAdvance"
                  checked
                />
                <label for="allowAdvance">Allow Leave Advances</label>
              </div>

              <div class="form-field">
                <label for="maxAdvanceDays">Maximum Advance Days</label>
                <input
                  type="number"
                  id="maxAdvanceDays"
                  name="accrualSettings.maxAdvanceDays"
                  min="0"
                />
              </div>

              <div class="checkbox-field">
                <input
                  type="checkbox"
                  id="proRateFirstYear"
                  name="accrualSettings.proRateFirstYear"
                  checked
                />
                <label for="proRateFirstYear">Pro-rate First Year</label>
              </div>
            </div>

            <div class="form-field">
              <label for="carryOverLimit">Carry Over Limit</label>
              <input
                type="number"
                id="carryOverLimit"
                name="carryOverLimit"
                min="0"
                value="0"
              />
            </div>

            <div class="form-field">
              <label for="gender">Applicable Gender</label>
              <select id="gender" name="gender">
                <option value="all">All</option>
                <option value="male">Male</option>
                <option value="female">Female</option>
              </select>
            </div>

            <div class="form-field">
              <label for="minDaysNotice">Minimum Days Notice</label>
              <input
                type="number"
                id="minDaysNotice"
                name="minDaysNotice"
                min="0"
                value="0"
              />
            </div>

            <div class="checkbox-field">
              <input type="checkbox" id="paidLeave" name="paidLeave" checked />
              <label for="paidLeave">Paid Leave</label>
            </div>

            <div class="checkbox-field">
              <input
                type="checkbox"
                id="requiresApproval"
                name="requiresApproval"
                checked
              />
              <label for="requiresApproval">Requires Approval</label>
            </div>

            <div class="checkbox-field">
              <input
                type="checkbox"
                id="requiresDocument"
                name="requiresDocument"
              />
              <label for="requiresDocument">Requires Supporting Document</label>
            </div>

            <div
              class="form-field"
              id="documentDaysField"
              style="display: none"
            >
              <label for="documentRequiredAfterDays"
                >Document Required After Days</label
              >
              <input
                type="number"
                id="documentRequiredAfterDays"
                name="documentRequiredAfterDays"
                min="0"
                value="2"
              />
            </div>
          </div>

          <div class="modal-footer">
            <button type="button" class="secondary-button" id="cancelLeaveType">
              Cancel
            </button>
            <button type="submit" class="primary-button">
              Save Leave Type
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Employee Allocation Modal -->
    <div id="employeeAllocationModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h2 class="modal-title">Employee-Specific Leave Allocation</h2>
          <button class="close-modal">&times;</button>
        </div>

        <form id="employeeAllocationForm">
          <div class="form-grid">
            <div class="form-field">
              <label for="employeeId">Employee *</label>
              <select id="employeeId" name="employeeId" required>
                <% employees.forEach(function(employee) { %>
                <option value="<%= employee._id %>">
                  <%= employee.firstName %> <%= employee.lastName %>
                </option>
                <% }); %>
              </select>
            </div>

            <div class="form-field">
              <label for="employeeDaysPerYear">Days Per Year *</label>
              <input
                type="number"
                id="employeeDaysPerYear"
                name="employeeDaysPerYear"
                min="0"
                step="0.5"
                required
              />
            </div>

            <div class="form-field">
              <label for="startDate">Start Date *</label>
              <input type="date" id="startDate" name="startDate" required />
            </div>

            <div class="form-field">
              <label for="endDate">End Date</label>
              <input type="date" id="endDate" name="endDate" />
            </div>

            <div class="form-field full-width">
              <label for="allocationReason">Reason</label>
              <textarea
                id="allocationReason"
                name="allocationReason"
                rows="3"
              ></textarea>
            </div>
          </div>

          <div class="modal-footer">
            <button
              type="button"
              class="secondary-button"
              id="cancelAllocation"
            >
              Cancel
            </button>
            <button type="submit" class="primary-button">
              Save Allocation
            </button>
          </div>
        </form>
      </div>
    </div>

    <script>
      // Toast notification function
      function showToast(message, type = 'info') {
        console.log('Showing toast:', { message, type }); // Debug log

        // Remove any existing toasts
        const existingToasts = document.querySelectorAll('.toast');
        existingToasts.forEach(toast => toast.remove());

        // Create new toast
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;

        // Add icon based on type
        const icon = document.createElement('i');
        icon.className = type === 'success' ? 'ph ph-check-circle' :
                        type === 'error' ? 'ph ph-x-circle' :
                        type === 'warning' ? 'ph ph-warning' :
                        'ph ph-info';

        // Add message
        const messageSpan = document.createElement('span');
        messageSpan.textContent = message;

        toast.appendChild(icon);
        toast.appendChild(messageSpan);
        document.body.appendChild(toast);

        // Show toast with animation
        setTimeout(() => {
          toast.classList.add('show');
        }, 100);

        // Hide toast after 4 seconds
        setTimeout(() => {
          toast.classList.remove('show');
          setTimeout(() => {
            if (toast.parentNode) {
              toast.remove();
            }
          }, 300);
        }, 4000);
      }

      // Modal functionality
      const modal = document.getElementById("leaveTypeModal");
      const addButton = document.getElementById("addLeaveType");
      const closeButton = document.querySelector(".close-modal");
      const cancelButton = document.getElementById("cancelLeaveType");
      const form = document.getElementById("leaveTypeForm");
      const requiresDocumentCheckbox =
        document.getElementById("requiresDocument");
      const documentDaysField = document.getElementById("documentDaysField");

      addButton.onclick = () => {
        modal.style.display = "block";
        form.reset();
        clearErrorMessages();

        // Reset custom name field visibility
        document.getElementById("customNameField").style.display = "none";
        document.getElementById("customName").required = false;

        // Reset modal title
        document.querySelector(".modal-title").textContent = "Add Leave Type";
        form.removeAttribute('data-id');
      };

      closeButton.onclick = () => {
        modal.style.display = "none";
        clearErrorMessages();
      };

      cancelButton.onclick = () => {
        modal.style.display = "none";
        clearErrorMessages();
      };

      window.onclick = (event) => {
        if (event.target === modal) {
          modal.style.display = "none";
        }
      };

      requiresDocumentCheckbox.onchange = () => {
        documentDaysField.style.display = requiresDocumentCheckbox.checked
          ? "block"
          : "none";
      };
        // Clear previous error messages
        clearErrorMessages();

      // Form submission
      form.onsubmit = async (e) => {
        e.preventDefault();
        const formData = new FormData(form);
        const data = {};

        // Convert form data to proper types
        formData.forEach((value, key) => {
          if (key.includes(".")) {
            // Handle nested objects (accrualSettings)
            const [parent, child] = key.split(".");
            if (!data[parent]) data[parent] = {};
            if (child === "allowAdvance" || child === "proRateFirstYear") {
              data[parent][child] = true;
            } else if (
              child === "waitingPeriodMonths" ||
              child === "maxAdvanceDays"
            ) {
              data[parent][child] = Number(value);
            } else {
              data[parent][child] = value;
            }
          } else if (
            key === "daysPerYear" ||
            key === "carryOverLimit" ||
            key === "minDaysNotice" ||
            key === "documentRequiredAfterDays"
          ) {
            data[key] = Number(value);
          } else if (
            key === "paidLeave" ||
            key === "requiresApproval" ||
            key === "requiresDocument" ||
            key === "allowEmployeeSpecificAllocation"
          ) {
            data[key] = true;
          } else {
            data[key] = value;
          }
        });

        // Handle custom leave type naming
        if (data.category === "custom" && data.customName) {
          data.name = data.customName.trim();
          delete data.customName; // Remove from data as it's not needed in the backend
        }

        // Validate custom name for uniqueness and meaningfulness
        if (data.category === "custom") {
          if (!data.name || data.name.trim().length < 3) {
            const errorMessageContainer = document.getElementById("leaveTypeErrorMessage");
            errorMessageContainer.textContent = "Custom leave type name must be at least 3 characters long.";
            errorMessageContainer.style.display = "block";
            return;
          }

          // Check if name contains meaningful content (not just spaces or special characters)
          if (!/[a-zA-Z]/.test(data.name)) {
            const errorMessageContainer = document.getElementById("leaveTypeErrorMessage");
            errorMessageContainer.textContent = "Custom leave type name must contain letters.";
            errorMessageContainer.style.display = "block";
            return;
          }
        }

        // Add missing checkbox values as false
        [
          "paidLeave",
          "requiresApproval",
          "requiresDocument",
          "allowEmployeeSpecificAllocation",
        ].forEach((key) => {
          if (!(key in data)) data[key] = false;
        });

        // Add missing accrual settings checkbox values as false
        if (data.accrualSettings) {
          ["allowAdvance", "proRateFirstYear"].forEach((key) => {
            if (!(key in data.accrualSettings))
              data.accrualSettings[key] = false;
          });
        }

        // Get company code from meta tag
        const companyCode = document.querySelector(
          'meta[name="company-code"]'
        ).content;

        try {
          // Determine if this is a create or update operation
          const isEdit = form.dataset.id;
          const url = isEdit
            ? `/api/leave/types/${form.dataset.id}`
            : `/api/leave/types`;

          const response = await fetch(url, {
            method: isEdit ? "PUT" : "POST",
            headers: {
              "Content-Type": "application/json",
              "CSRF-Token": document.querySelector('input[name="_csrf"]').value,
            },
            body: JSON.stringify({
              ...data,
              companyCode, // Add company code to request
            }),
          });

          if (response.ok) {
            const savedLeaveType = await response.json();
            console.log('Leave type saved successfully:', savedLeaveType); // Debug log

            modal.style.display = "none";

            // Clear form for next use
            form.reset();
            form.removeAttribute('data-id');
            document.querySelector(".modal-title").textContent = "Add Leave Type";

            // Show success message
            const successMessage = isEdit ? "Leave type updated successfully!" : "Leave type created successfully!";
            console.log('Showing success toast:', successMessage); // Debug log
            showToast(successMessage, "success");

            // Update UI dynamically instead of page reload
            try {
              if (isEdit) {
                console.log('Updating existing card for leave type:', savedLeaveType._id); // Debug log
                updateLeaveTypeCardInPlace(savedLeaveType);
              } else {
                console.log('Adding new card for leave type:', savedLeaveType._id); // Debug log
                addNewLeaveTypeCard(savedLeaveType);
              }
            } catch (error) {
              console.error('Error updating UI:', error);
              // Fallback to page reload if dynamic update fails
              console.log('Falling back to page reload');
              window.location.reload();
            }
          } else {
            const error = await response.json();
            // Display error message in the container
            const errorMessageContainer = document.getElementById("leaveTypeErrorMessage");
            errorMessageContainer.textContent = error.message || "Failed to save leave type";
            errorMessageContainer.style.display = "block";
            // Also show toast notification for error
            showToast(error.message || "Failed to save leave type", "error");
          }
        } catch (error) {
          console.error("Error:", error);
          // Display generic error message
          const errorMessageContainer = document.getElementById("leaveTypeErrorMessage");
          errorMessageContainer.textContent = "Failed to save leave type";
          errorMessageContainer.style.display = "block";
          // Also show toast notification for error
          showToast("Failed to save leave type", "error");
        }
      };

      // Edit leave type
      document.querySelectorAll(".edit-leave-type").forEach((button) => {
        button.onclick = async (e) => {
          const card = e.target.closest(".leave-type-card");
          const leaveTypeId = card.dataset.id;

          try {
            const response = await fetch(`/api/leave/types/${leaveTypeId}`, {
              headers: {
                "CSRF-Token": document.querySelector('input[name="_csrf"]')
                  .value,
              },
            });
            const leaveType = await response.json();

            // Populate form with leave type data
            Object.entries(leaveType).forEach(([key, value]) => {
              const input = form.elements[key];
              if (input) {
                if (input.type === "checkbox") {
                  input.checked = value;
                } else {
                  input.value = value;
                }
              }
            });

            // Update form action and modal title
            form.dataset.id = leaveTypeId;
            document.querySelector(".modal-title").textContent =
              "Edit Leave Type";
            modal.style.display = "block";
          } catch (error) {
            console.error("Error:", error);
            showToast("Failed to load leave type details", "error");
          }
        };
      });

      // Toggle leave type status
      document.querySelectorAll(".toggle-status").forEach((button) => {
        button.onclick = async (e) => {
          const card = e.target.closest(".leave-type-card");
          const leaveTypeId = card.dataset.id;

          if (
            confirm("Are you sure you want to toggle this leave type's status?")
          ) {
            try {
              const response = await fetch(
                `/api/leave/types/${leaveTypeId}/toggle`,
                {
                  method: "PUT",
                  headers: {
                    "CSRF-Token": document.querySelector('input[name="_csrf"]')
                      .value,
                  },
                }
              );

              if (response.ok) {
                const updatedLeaveType = await response.json();
                showToast("Leave type status updated successfully!", "success");

                // Update the status badge dynamically
                updateLeaveTypeStatus(card, updatedLeaveType.active);
              } else {
                showToast("Failed to toggle leave type status", "error");
              }
            } catch (error) {
              console.error("Error:", error);
              showToast("Failed to toggle leave type status", "error");
            }
          }
        };
      });

      // Employee allocation modal functionality
      const employeeAllocationModal = document.getElementById(
        "employeeAllocationModal"
      );
      const employeeAllocationForm = document.getElementById(
        "employeeAllocationForm"
      );
      const cancelAllocationButton =
        document.getElementById("cancelAllocation");

      // Show employee allocation modal when clicking "Manage Allocations" button
      document.querySelectorAll(".manage-allocations").forEach((button) => {
        button.onclick = async (e) => {
          const card = e.target.closest(".leave-type-card");
          const leaveTypeId = card.dataset.id;
          employeeAllocationForm.dataset.leaveTypeId = leaveTypeId;
          employeeAllocationModal.style.display = "block";
        };
      });

      // Close employee allocation modal
      cancelAllocationButton.onclick = () => {
        employeeAllocationModal.style.display = "none";
      };

      // Handle employee allocation form submission
      employeeAllocationForm.onsubmit = async (e) => {
        e.preventDefault();
        const formData = new FormData(employeeAllocationForm);
        const leaveTypeId = employeeAllocationForm.dataset.leaveTypeId;
        const data = Object.fromEntries(formData);

        try {
          const response = await fetch(
            `/api/leave/types/${leaveTypeId}/allocations`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                "CSRF-Token": document.querySelector('input[name="_csrf"]')
                  .value,
              },
              body: JSON.stringify(data),
            }
          );

          if (response.ok) {
            employeeAllocationModal.style.display = "none";
            showToast("Employee allocation saved successfully!", "success");
            window.location.reload();
          } else {
            const error = await response.json();
            showToast(error.message || "Failed to create employee allocation", "error");
          }
        } catch (error) {
          console.error("Error:", error);
          showToast("Failed to create employee allocation", "error");
        }
      };

      // Update leave type card to show employee allocations
      function updateLeaveTypeCard(leaveType) {
        const card = document.querySelector(`[data-id="${leaveType._id}"]`);
        if (!card) return;

        // Add employee allocations section if allowed
        if (leaveType.allowEmployeeSpecificAllocation) {
          const allocationsSection = document.createElement("div");
          allocationsSection.className = "leave-type-allocations";
          allocationsSection.innerHTML = `
            <h4>Employee Allocations</h4>
            <button class="action-button manage-allocations" title="Manage Allocations">
              <i class="ph ph-users"></i>
            </button>
          `;
          card
            .querySelector(".leave-type-actions")
            .appendChild(allocationsSection);
        }
      }

      // Update form based on category selection
      document.getElementById("category").onchange = (e) => {
        const category = e.target.value;
        const customNameField = document.getElementById("customNameField");
        const customNameInput = document.getElementById("customName");
        const paidLeaveCheckbox = document.getElementById("paidLeave");
        const requiresApprovalCheckbox =
          document.getElementById("requiresApproval");
        const requiresDocumentCheckbox =
          document.getElementById("requiresDocument");
        const daysPerYearInput = document.getElementById("daysPerYear");
        const accrualRateSelect = document.getElementById("accrualRate");

        // Show/hide custom name field
        if (category === "custom") {
          customNameField.style.display = "block";
          customNameInput.required = true;
          customNameInput.focus();
        } else {
          customNameField.style.display = "none";
          customNameInput.required = false;
          customNameInput.value = "";
        }

        switch (category) {
          case "annual":
            paidLeaveCheckbox.checked = true;
            requiresApprovalCheckbox.checked = true;
            requiresDocumentCheckbox.checked = false;
            daysPerYearInput.value = "15";
            accrualRateSelect.value = "yearly";
            break;
          case "sick":
            paidLeaveCheckbox.checked = true;
            requiresApprovalCheckbox.checked = true;
            requiresDocumentCheckbox.checked = true;
            daysPerYearInput.value = "30";
            accrualRateSelect.value = "yearly";
            break;
          case "family":
            paidLeaveCheckbox.checked = true;
            requiresApprovalCheckbox.checked = true;
            requiresDocumentCheckbox.checked = true;
            daysPerYearInput.value = "3";
            accrualRateSelect.value = "yearly";
            break;
          case "unpaid":
            paidLeaveCheckbox.checked = false;
            requiresApprovalCheckbox.checked = true;
            requiresDocumentCheckbox.checked = false;
            daysPerYearInput.value = "0";
            accrualRateSelect.value = "none";
            break;
          case "custom":
            // Set reasonable defaults for custom leave
            paidLeaveCheckbox.checked = true;
            requiresApprovalCheckbox.checked = true;
            requiresDocumentCheckbox.checked = false;
            daysPerYearInput.value = "5";
            accrualRateSelect.value = "yearly";
            break;
        }
      };

      // Add this to your existing script section
      document
        .getElementById("accrualRate")
        .addEventListener("change", function () {
          const accrualSettings = document.getElementById("accrualSettings");
          const maxAdvanceDays = document.getElementById("maxAdvanceDays");
          const daysPerYear = document.getElementById("daysPerYear");

          if (this.value === "monthly") {
            accrualSettings.style.display = "block";
            maxAdvanceDays.value = daysPerYear.value;
          } else {
            accrualSettings.style.display = "none";
          }
        });

      // Update maxAdvanceDays when daysPerYear changes
      document
        .getElementById("daysPerYear")
        .addEventListener("change", function () {
          if (document.getElementById("accrualRate").value === "monthly") {
            document.getElementById("maxAdvanceDays").value = this.value;
          }
        });

      // Dynamic UI update functions
      function addNewLeaveTypeCard(leaveType) {
        console.log('addNewLeaveTypeCard called with:', leaveType); // Debug log

        const grid = document.querySelector(".leave-types-grid");
        if (!grid) {
          console.error('Leave types grid not found');
          return;
        }

        console.log('Grid found, creating card HTML'); // Debug log
        const cardHtml = createLeaveTypeCardHtml(leaveType);
        console.log('Card HTML created:', cardHtml.substring(0, 100) + '...'); // Debug log

        // Create a temporary container to parse the HTML
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = cardHtml;
        const newCard = tempDiv.firstElementChild;

        if (!newCard) {
          console.error('Failed to create new card element');
          return;
        }

        console.log('Adding card to grid'); // Debug log
        // Add the new card to the grid
        grid.appendChild(newCard);

        console.log('Attaching event listeners'); // Debug log
        // Attach event listeners to the new card
        attachCardEventListeners(newCard);

        console.log('Adding animation'); // Debug log
        // Add a subtle animation
        newCard.style.opacity = '0';
        newCard.style.transform = 'translateY(20px)';
        setTimeout(() => {
          newCard.style.transition = 'all 0.3s ease';
          newCard.style.opacity = '1';
          newCard.style.transform = 'translateY(0)';
        }, 100);

        console.log('New card added successfully'); // Debug log
      }

      function updateLeaveTypeCardInPlace(leaveType) {
        const existingCard = document.querySelector(`[data-id="${leaveType._id}"]`);
        if (!existingCard) return;

        // Update the card content
        const cardHtml = createLeaveTypeCardHtml(leaveType);
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = cardHtml;
        const newCard = tempDiv.firstElementChild;

        // Replace the existing card
        existingCard.parentNode.replaceChild(newCard, existingCard);

        // Attach event listeners to the updated card
        attachCardEventListeners(newCard);

        // Add a subtle flash animation to indicate update
        newCard.style.backgroundColor = '#f0f9ff';
        setTimeout(() => {
          newCard.style.transition = 'background-color 0.5s ease';
          newCard.style.backgroundColor = '';
        }, 100);
      }

      function updateLeaveTypeStatus(card, isActive) {
        const statusBadge = card.querySelector('.status-badge');
        statusBadge.textContent = isActive ? 'Active' : 'Inactive';
        statusBadge.className = `status-badge ${isActive ? 'active' : 'inactive'}`;

        // Add a subtle flash animation
        statusBadge.style.backgroundColor = isActive ? '#dcfce7' : '#fef2f2';
        setTimeout(() => {
          statusBadge.style.transition = 'background-color 0.5s ease';
          statusBadge.style.backgroundColor = '';
        }, 100);
      }

      function createLeaveTypeCardHtml(leaveType) {
        console.log('Creating card HTML for leave type:', leaveType); // Debug log

        // Ensure we have all required properties with defaults
        const name = leaveType.name || 'Unnamed Leave Type';
        const description = leaveType.description || 'No description provided';
        const daysPerYear = leaveType.daysPerYear || 0;
        const accrualRate = leaveType.accrualRate || 'yearly';
        const active = leaveType.active !== false; // Default to true if not specified
        const id = leaveType._id || leaveType.id;

        if (!id) {
          console.error('Leave type missing ID:', leaveType);
          return '';
        }

        return `
          <div class="leave-type-card" data-id="${id}">
            <div class="leave-type-header">
              <div class="leave-type-info">
                <h3>${name}</h3>
                <p class="leave-type-description">
                  ${description}
                </p>
              </div>
              <div class="status-badge ${active ? 'active' : 'inactive'}">
                ${active ? 'Active' : 'Inactive'}
              </div>
            </div>

            <div class="leave-type-stats">
              <div class="stat-item">
                <div class="stat-label">Days Per Year</div>
                <div class="stat-value">${daysPerYear}</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">Accrual Rate</div>
                <div class="stat-value">${accrualRate}</div>
              </div>
            </div>

            <div class="leave-type-actions">
              <button class="action-button edit-leave-type" title="Edit">
                <i class="ph ph-pencil"></i>
              </button>
              <button class="action-button toggle-status" title="Toggle Status">
                <i class="ph ph-power"></i>
              </button>
              <button class="action-button view-details" title="View Details">
                <i class="ph ph-eye"></i>
              </button>
            </div>
          </div>
        `;
      }

      function attachCardEventListeners(card) {
        console.log('Attaching event listeners to card:', card.dataset.id); // Debug log

        if (!card) {
          console.error('Cannot attach listeners to null card');
          return;
        }

        // Edit button
        const editButton = card.querySelector('.edit-leave-type');
        if (editButton) {
          console.log('Attaching edit button listener'); // Debug log
          editButton.onclick = async (e) => {
            e.preventDefault();
            const leaveTypeId = card.dataset.id;
            console.log('Edit button clicked for leave type:', leaveTypeId); // Debug log

            try {
              const response = await fetch(`/api/leave/types/${leaveTypeId}`, {
                headers: {
                  "CSRF-Token": document.querySelector('input[name="_csrf"]').value,
                },
              });

              if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
              }

              const leaveType = await response.json();
              console.log('Loaded leave type for editing:', leaveType); // Debug log

              // Clear any existing error messages
              clearErrorMessages();

              // Populate form with leave type data
              Object.entries(leaveType).forEach(([key, value]) => {
                const input = form.elements[key];
                if (input) {
                  if (input.type === "checkbox") {
                    input.checked = value;
                  } else {
                    input.value = value;
                  }
                }
              });

              // Handle custom leave type name
              if (leaveType.category === 'custom') {
                document.getElementById('customName').value = leaveType.name;
                document.getElementById('customNameField').style.display = 'block';
                document.getElementById('customName').required = true;
              } else {
                document.getElementById('customNameField').style.display = 'none';
                document.getElementById('customName').required = false;
              }

              // Update form action and modal title
              form.dataset.id = leaveTypeId;
              document.querySelector(".modal-title").textContent = "Edit Leave Type";
              modal.style.display = "block";
            } catch (error) {
              console.error("Error loading leave type details:", error);
              showToast("Failed to load leave type details", "error");
            }
          };
        } else {
          console.warn('Edit button not found in card'); // Debug log
        }

        // Toggle status button
        const toggleButton = card.querySelector('.toggle-status');
        if (toggleButton) {
          console.log('Attaching toggle button listener'); // Debug log
          toggleButton.onclick = async (e) => {
            e.preventDefault();
            const leaveTypeId = card.dataset.id;
            console.log('Toggle button clicked for leave type:', leaveTypeId); // Debug log

            if (confirm("Are you sure you want to toggle this leave type's status?")) {
              try {
                const response = await fetch(`/api/leave/types/${leaveTypeId}/toggle`, {
                  method: "PUT",
                  headers: {
                    "CSRF-Token": document.querySelector('input[name="_csrf"]').value,
                  },
                });

                if (response.ok) {
                  const updatedLeaveType = await response.json();
                  console.log('Leave type status toggled:', updatedLeaveType); // Debug log
                  showToast("Leave type status updated successfully!", "success");

                  // Update the status badge dynamically
                  updateLeaveTypeStatus(card, updatedLeaveType.active);
                } else {
                  console.error('Failed to toggle status, response not ok:', response.status);
                  showToast("Failed to toggle leave type status", "error");
                }
              } catch (error) {
                console.error("Error toggling leave type status:", error);
                showToast("Failed to toggle leave type status", "error");
              }
            }
          };
        } else {
          console.warn('Toggle button not found in card'); // Debug log
        }

        console.log('Event listeners attached successfully to card:', card.dataset.id); // Debug log
      }

      // Utility functions
      function clearErrorMessages() {
        const errorMessageContainer = document.getElementById("leaveTypeErrorMessage");
        errorMessageContainer.style.display = "none";
        errorMessageContainer.textContent = "";
      }

      function showErrorMessage(message) {
        const errorMessageContainer = document.getElementById("leaveTypeErrorMessage");
        errorMessageContainer.textContent = message;
        errorMessageContainer.style.display = "block";

        // Scroll to error message
        errorMessageContainer.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
      }

      // Real-time validation for custom name field
      document.getElementById("customName").addEventListener("input", function() {
        const value = this.value.trim();
        const errorContainer = document.getElementById("leaveTypeErrorMessage");

        if (value.length > 0 && value.length < 3) {
          this.setCustomValidity("Custom leave type name must be at least 3 characters long.");
        } else if (value.length > 0 && !/[a-zA-Z]/.test(value)) {
          this.setCustomValidity("Custom leave type name must contain letters.");
        } else {
          this.setCustomValidity("");
          if (errorContainer.style.display === "block") {
            clearErrorMessages();
          }
        }
      });

      // Enhanced form validation
      form.addEventListener("input", function(e) {
        // Clear error messages when user starts typing
        if (document.getElementById("leaveTypeErrorMessage").style.display === "block") {
          setTimeout(clearErrorMessages, 1000);
        }
      });
    </script>
  </body>
</html>
