<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>New Custom Deduction - <%= company.name %></title>
    <meta name="company-code" content="<%= company.companyCode %>" />
    <meta name="csrf-token" content="<%= csrfToken %>">

    <!-- Modern Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />

    <!-- Stylesheets -->
    <link rel="stylesheet" href="/css/styles.css" />
    <link rel="stylesheet" href="/css/customs.css" />
    <link rel="stylesheet" href="/css/employeeManagement.css" />
    <link rel="stylesheet" href="/css/employeeProfile.css" />
    <link rel="stylesheet" href="/css/header.css" />
    <link rel="stylesheet" href="/css/sidebar.css" />
    <link rel="stylesheet" href="/css/mobile-header.css" />
    <link rel="stylesheet" href="/css/mobile-nav.css" />
    <link rel="stylesheet" href="/css/custom-items.css" />
    <link rel="stylesheet" href="/css/notifications.css" />
    <link rel="stylesheet" href="/css/settings.css" />
    <link rel="stylesheet" href="/css/accounting-settings.css" />

    <!-- Icons -->
    <script src="https://unpkg.com/@phosphor-icons/web"></script>

    <style>
  <%- include('../partials/header') %>
  <div class="main-container">
    <%- include('../partials/sidebar') %>
    <main class="content">
      <div class="page-header">
        <div class="header-content">
          <div class="title-container">
            <h1>New Custom Deduction</h1>
            <p class="subtitle">Create a new custom deduction for your employees</p>
          </div>
          <div class="action-buttons">
            <a href="/clients/<%= company.companyCode %>/settings/custom-items" class="btn btn-outline-primary">
              <i class="ph ph-list mr-2"></i>View All Custom Items
            </a>
          </div>
        </div>
        <nav aria-label="breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item">Dashboard</li>
            <li class="breadcrumb-item">Settings</li>
            <li class="breadcrumb-item">Custom Items</li>
            <li class="breadcrumb-item active" aria-current="page">New Custom Deduction</li>
          </ol>
        </nav>
      </div>

      <div class="content-container">
        <div class="card premium-card">
          <div class="card-header">
            <div class="card-title-section">
              <div class="icon-title">
                <i class="ph ph-minus-circle ph-xl accent-icon"></i>
                <div>
                  <h2>Create New Custom Deduction</h2>
                  <p class="card-subtitle">Fill in the details below to create a new custom deduction</p>
                </div>
              </div>
            </div>
          </div>
          <div class="card-body">
            <form id="newCustomDeductionForm" action="/clients/<%= company.companyCode %>/settings/custom-items/deduction" method="POST">
              <input type="hidden" name="_csrf" value="<%= csrfToken %>">
              
              <div class="form-section">
                <h3 class="section-title">Basic Information</h3>
                <div class="form-row">
                  <div class="form-group col-md-6">
                    <label for="name" class="form-label">Name</label>
                    <input type="text" id="name" name="name" class="form-control premium-input" placeholder="Enter deduction name" required>
                    <small class="form-text text-muted">Enter a descriptive name for this deduction</small>
                  </div>
                  
                  <div class="form-group col-md-6">
                    <label for="inputType" class="form-label">Input Type</label>
                    <div class="select-wrapper">
                      <select id="inputType" name="inputType" class="form-control premium-select" required>
                        <option value="">Select an input type</option>
                        <option value="fixedAmount">Fixed Amount</option>
                        <option value="amountPerEmployee">Enter Amount per employee</option>
                        <option value="differentEveryPayslip">Different on every payslip</option>
                        <option value="hourlyRateFactor">Hourly Rate * Factor * hours</option>
                        <option value="customRateQuantity">Custom rate * quantity</option>
                        <option value="percentageOfIncome">% of income</option>
                        <option value="monthlyForNonMonthly">Monthly for non monthly employee</option>
                      </select>
                      <i class="ph ph-caret-down select-icon"></i>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="form-section">
                <h3 class="section-title">Additional Options</h3>
                <div class="form-row">
                  <div class="form-group col-md-6">
                    <div class="premium-checkbox">
                      <input type="checkbox" id="excludeFromAccounting" name="excludeFromAccounting">
                      <label for="excludeFromAccounting">
                        <span class="checkbox-icon"></span>
                        <span class="checkbox-text">Exclude from Accounting</span>
                      </label>
                      <small class="form-text text-muted">Check this if you don't want this deduction to appear in accounting reports</small>
                    </div>
                  </div>
                  
                  <div class="form-group col-md-6">
                    <div class="premium-checkbox">
                      <input type="checkbox" id="bargainingCouncilItem" name="bargainingCouncilItem">
                      <label for="bargainingCouncilItem">
                        <span class="checkbox-icon"></span>
                        <span class="checkbox-text">Bargaining Council Item</span>
                      </label>
                      <small class="form-text text-muted">Check this if this deduction is regulated by a bargaining council</small>
                    </div>
                  </div>
                </div>
              </div>
              
              <div id="fixedAmountFields" class="form-section" style="display: none;">
                <h3 class="section-title">Fixed Amount Settings</h3>
                <div class="form-row">
                  <div class="form-group col-md-6">
                    <label for="amount" class="form-label">Amount</label>
                    <div class="input-group">
                      <div class="input-group-prepend">
                        <span class="input-group-text">R</span>
                      </div>
                      <input type="number" id="amount" name="amount" class="form-control premium-input" placeholder="Enter fixed amount" step="0.01">
                    </div>
                    <small class="form-text text-muted">Enter the fixed amount for this deduction</small>
                  </div>
                  
                  <div class="form-group col-md-6">
                    <div class="premium-checkbox mt-4">
                      <input type="checkbox" id="enableProRata" name="enableProRata">
                      <label for="enableProRata">
                        <span class="checkbox-icon"></span>
                        <span class="checkbox-text">Enable Pro-Rata</span>
                      </label>
                      <small class="form-text text-muted">Can't be changed if used on a finalised payslip</small>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="form-actions">
                <button type="submit" class="btn btn-primary btn-lg">
                  <i class="ph ph-plus-circle mr-2"></i>Create Deduction
                </button>
                <a href="/clients/<%= company.companyCode %>/settings/custom-items" class="btn btn-outline-secondary btn-lg">
                  <i class="ph ph-arrow-left mr-2"></i>Cancel
                </a>
              </div>
            </form>
          </div>
        </div>
      </div>
    </main>
  </div>

    <style>
      /* Professional Custom Deduction Form Design - PandaPayroll Design System */
      :root {
        --primary-color: #6366f1;
        --secondary-color: #818cf8;
        --background-color: #f8fafc;
        --card-background: #ffffff;
        --text-primary: #1e293b;
        --text-secondary: #64748b;
        --border-color: #e2e8f0;
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --danger-color: #ef4444;
        --hover-color: rgba(99, 102, 241, 0.08);
        --focus-color: rgba(99, 102, 241, 0.2);
      }

      /* Layout wrapper improvements */
      .layout-wrapper {
        display: flex;
        min-height: 100vh;
        background: var(--background-color);
        font-family: 'Inter', sans-serif;
      }

      .content-wrapper {
        flex: 1;
        margin-left: 280px;
        display: flex;
        flex-direction: column;
      }

      /* Main container styling */
      .main-container {
        flex: 1;
        padding: 2rem;
        background: var(--background-color);
        min-height: calc(100vh - 80px);
      }

      /* Professional page header */
      .page-header {
        background: linear-gradient(135deg, rgba(99, 102, 241, 0.03), rgba(129, 140, 248, 0.05));
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.03);
        border: 1px solid rgba(99, 102, 241, 0.1);
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .header-content h1 {
        font-size: 1.875rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
        font-family: 'Inter', sans-serif;
      }

      .description {
        color: var(--text-secondary);
        font-size: 0.975rem;
        margin: 0;
        line-height: 1.5;
      }

      .company-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.25rem;
        background: rgba(99, 102, 241, 0.1);
        border-radius: 9999px;
        color: var(--primary-color);
        font-size: 0.875rem;
        font-weight: 500;
      }

      .company-badge i {
        font-size: 1.125rem;
      }

      /* Professional breadcrumb navigation */
      .breadcrumb-nav {
        margin-bottom: 1rem;
      }

      .back-link {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--text-secondary);
        text-decoration: none;
        font-size: 0.875rem;
        font-weight: 500;
        padding: 0.5rem 0;
        transition: all 0.2s ease;
      }

      .back-link:hover {
        color: var(--primary-color);
        transform: translateX(-2px);
      }

      .back-link i {
        font-size: 1rem;
      }

      /* Form container - Optimized for individual cards */
      .form-container {
        max-width: 1400px;
        margin: 0 auto;
        width: 100%;
        padding: 0 2rem;
      }

      /* Professional form styling - Optimized for individual cards */
      .custom-item-form {
        display: flex;
        flex-direction: column;
        gap: 0;
        width: 100%;
      }

      /* Individual form cards - Optimized standalone cards */
      .form-card {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: 20px;
        padding: 3rem 4rem;
        transition: all 0.2s ease;
        margin-bottom: 2rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.03);
        width: 100%;
        box-sizing: border-box;
      }

      .form-card:hover {
        border-color: var(--primary-color);
        box-shadow: 0 8px 25px rgba(99, 102, 241, 0.1);
        transform: translateY(-2px);
      }

      .form-card h3 {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 2.5rem 0;
        padding-bottom: 1.25rem;
        border-bottom: 1px solid var(--border-color);
        font-family: 'Inter', sans-serif;
      }

      /* Form grid layout - Optimized for better space utilization */
      .form-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 2.5rem;
        margin-top: 0.5rem;
        max-width: none;
      }

      .form-group.full-width {
        grid-column: 1 / -1;
      }

      /* Form group styling - Enhanced for wider fields */
      .form-group {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin-bottom: 0.75rem;
        width: 100%;
      }

      .form-group label {
        font-weight: 500;
        color: var(--text-primary);
        font-size: 0.9rem;
        font-family: 'Inter', sans-serif;
        margin-bottom: 0.25rem;
      }
      /* Input field styling - Optimized for better space utilization */
      .form-group input[type="text"],
      .form-group input[type="number"],
      .form-group select,
      .form-group textarea {
        width: 100%;
        max-width: none;
        padding: 1.25rem 1.5rem;
        border: 1px solid var(--border-color);
        border-radius: 12px;
        font-size: 1rem;
        font-family: 'Inter', sans-serif;
        transition: all 0.2s ease;
        background: var(--card-background);
        color: var(--text-primary);
        min-height: 3.25rem;
        box-sizing: border-box;
      }

      .form-group textarea {
        min-height: 4.5rem;
        resize: vertical;
      }

      .form-group input:focus,
      .form-group select:focus,
      .form-group textarea:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px var(--focus-color);
      }

      .form-group input:hover,
      .form-group select:hover,
      .form-group textarea:hover {
        border-color: var(--primary-color);
      }

      /* Small helper text - Enhanced spacing */
      .form-group small {
        color: var(--text-secondary);
        font-size: 0.8rem;
        line-height: 1.5;
        margin-top: 0.25rem;
      }

      /* Checkbox styling - Optimized for better space utilization */
      .checkbox-label {
        display: flex;
        align-items: flex-start;
        gap: 1.25rem;
        cursor: pointer;
        padding: 1rem 1.5rem;
        font-weight: 500;
        color: var(--text-primary);
        font-size: 1rem;
        background: rgba(99, 102, 241, 0.02);
        border-radius: 10px;
        border: 1px solid transparent;
        transition: all 0.2s ease;
        margin-bottom: 0.75rem;
        width: 100%;
        box-sizing: border-box;
      }

      .checkbox-label:hover {
        background: rgba(99, 102, 241, 0.05);
        border-color: var(--primary-color);
      }

      .checkbox-label input[type="checkbox"] {
        width: 1.375rem;
        height: 1.375rem;
        margin: 0;
        margin-top: 0.125rem;
        accent-color: var(--primary-color);
        flex-shrink: 0;
      }

      /* Form actions card */
      .form-actions-card {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: 20px;
        padding: 3rem 4rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.03);
        margin-bottom: 2rem;
        transition: all 0.2s ease;
      }

      .form-actions-card:hover {
        border-color: var(--primary-color);
        box-shadow: 0 8px 25px rgba(99, 102, 241, 0.1);
      }

      /* Professional form actions - Optimized spacing */
      .form-actions {
        display: flex;
        gap: 2rem;
        justify-content: flex-start;
        align-items: center;
        width: 100%;
        margin: 0;
        padding: 0;
        border: none;
      }
    
      .btn-submit {
        display: inline-flex;
        align-items: center;
        gap: 0.75rem;
        padding: 1.25rem 2.5rem;
        background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
        color: white;
        border: none;
        border-radius: 12px;
        font-size: 1rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        box-shadow: 0 2px 4px rgba(99, 102, 241, 0.1);
        font-family: 'Inter', sans-serif;
        min-height: 3.5rem;
      }

      .btn-submit:hover {
        background: linear-gradient(135deg, #4f46e5 0%, #4338ca 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(99, 102, 241, 0.2);
      }

      .btn-cancel {
        display: inline-flex;
        align-items: center;
        gap: 0.75rem;
        padding: 1.25rem 2.5rem;
        background: white;
        color: var(--text-secondary);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        font-size: 1rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        font-family: 'Inter', sans-serif;
        min-height: 3.5rem;
      }

      .btn-cancel:hover {
        background: #f8fafc;
        border-color: var(--text-secondary);
        color: var(--text-primary);
        transform: translateY(-1px);
      }

      /* Conditional fields styling */
      .conditional-field {
        transition: all 0.3s ease;
      }

      .conditional-field.hidden {
        display: none !important;
      }

      /* Floating Action Button */
      .fab-add-item {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        width: 3.5rem;
        height: 3.5rem;
        background: var(--primary-color);
        color: white;
        border: none;
        border-radius: 50%;
        box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        transition: all 0.2s ease;
        z-index: 100;
      }

      .fab-add-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4);
      }

      /* Responsive design - Optimized for individual cards */
      @media (max-width: 1024px) {
        .main-container {
          margin-left: 0;
          padding: 2rem;
        }

        .form-container {
          padding: 0 1.5rem;
        }

        .form-card,
        .form-actions-card {
          padding: 2.5rem 3rem;
        }
      }

      @media (max-width: 768px) {
        .main-container {
          padding: 1.5rem;
        }

        .page-header {
          flex-direction: column;
          gap: 1.5rem;
          text-align: center;
          padding: 2rem;
        }

        .header-content h1 {
          font-size: 1.5rem;
        }

        .company-badge {
          align-self: center;
        }

        .form-container {
          padding: 0 1rem;
        }

        .form-card,
        .form-actions-card {
          padding: 2rem 2.5rem;
          margin-bottom: 1.5rem;
        }

        .form-card h3 {
          font-size: 1.25rem;
          margin-bottom: 2rem;
        }

        .form-grid {
          grid-template-columns: 1fr;
          gap: 2rem;
        }

        .checkbox-label {
          padding: 1rem;
          font-size: 0.95rem;
        }

        .form-actions {
          flex-direction: column;
          gap: 1.25rem;
          padding-top: 2.5rem;
        }

        .btn-submit,
        .btn-cancel {
          width: 100%;
          justify-content: center;
          padding: 1.25rem 2rem;
        }
      }

      @media (max-width: 480px) {
        .main-container {
          padding: 1rem;
        }

        .page-header {
          padding: 1.5rem;
        }

        .header-content h1 {
          font-size: 1.25rem;
        }

        .form-container {
          padding: 0 0.75rem;
        }

        .form-card,
        .form-actions-card {
          padding: 1.5rem 2rem;
          margin-bottom: 1.25rem;
        }

        .form-card h3 {
          font-size: 1.125rem;
          margin-bottom: 1.5rem;
        }

        .form-grid {
          gap: 1.5rem;
        }

        .form-group {
          gap: 0.875rem;
        }

        .checkbox-label {
          padding: 0.875rem 1rem;
          font-size: 0.9rem;
        }

        .form-actions {
          gap: 1rem;
          padding-top: 2rem;
        }

        .btn-submit,
        .btn-cancel {
          padding: 1rem 1.5rem;
          font-size: 0.9rem;
        }

        .fab-add-item {
          bottom: 5rem;
          right: 1rem;
          width: 3rem;
          height: 3rem;
          font-size: 1.125rem;
        }
      }
    </style>
  </head>
  <body>
    <%- include('../partials/header') %>
    <%- include('../partials/sidebar', { company: company }) %>

    <main class="main-container">
      <!-- Page Header -->
      <div class="page-header">
        <div class="header-content">
          <div class="breadcrumb-nav">
            <a href="/clients/<%= company.companyCode %>/settings/add-new-custom-item" class="back-link">
              <i class="ph ph-arrow-left"></i>
              <span>Back to Item Types</span>
            </a>
          </div>
          <h1>Add New Custom Deduction</h1>
          <p class="description">
            Create a new custom deduction item for your payroll system
          </p>
        </div>
        <div class="company-badge">
          <i class="ph ph-buildings"></i>
          <span><%= company.name %></span>
        </div>
      </div>

      <div class="form-container">
        <form
          id="customItemForm"
          action="/clients/<%= company.companyCode %>/settings/custom-items/deduction"
          method="POST"
          class="custom-item-form"
        >
          <input type="hidden" name="_csrf" value="<%= csrfToken %>" />
          <input type="hidden" name="type" value="deduction" />

          <!-- Basic Information Card -->
          <div class="form-card">
            <h3>Basic Information</h3>
            <div class="form-grid">
              <div class="form-group full-width">
                <label for="name">Name *</label>
                <input type="text" id="name" name="name" required />
              </div>

              <div class="form-group full-width">
                <label for="inputType">Input Type *</label>
                <select id="inputType" name="inputType" required>
                  <option value="">Select input type</option>
                  <option value="fixedAmount">Fixed Amount</option>
                  <option value="amountPerEmployee">Enter Amount per employee</option>
                  <option value="differentEveryPayslip">Different on every payslip</option>
                  <option value="hourlyRateFactor">Hourly Rate * Factor * hours</option>
                  <option value="customRateQuantity">Custom rate * quantity</option>
                  <option value="percentageOfIncome">% of income</option>
                  <option value="monthlyForNonMonthly">Monthly for non monthly employee</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Conditional Fields Card -->
          <div class="form-card" id="conditionalFieldsCard" style="display: none;">
            <h3>Additional Configuration</h3>
            <div class="form-grid">
              <!-- Fixed Amount Fields -->
              <div class="form-group full-width conditional-field" id="amountField" style="display: none;">
                <label for="amount">Amount *</label>
                <input type="number" id="amount" name="amount" step="0.01" min="0" />
              </div>

              <!-- Pro-Rata Checkbox -->
              <div class="form-group full-width conditional-field" id="proRataField" style="display: none;">
                <label class="checkbox-label">
                  <input type="checkbox" id="enableProRata" name="enableProRata" />
                  Enable Pro-Rata
                  <small>Can't be changed if used on a finalised payslip</small>
                </label>
              </div>
            </div>
          </div>

          <!-- Deduction Options Card -->
          <div class="form-card">
            <h3>Deduction Configuration</h3>
            <div class="form-grid">
              <div class="form-group full-width">
                <label class="checkbox-label">
                  <input type="checkbox" id="excludeFromAccounting" name="excludeFromAccounting" />
                  Exclude from Accounting
                  <small>Check this if you don't want this deduction to appear in accounting reports</small>
                </label>
              </div>

              <div class="form-group full-width">
                <label class="checkbox-label">
                  <input type="checkbox" id="bargainingCouncilItem" name="bargainingCouncilItem" />
                  Bargaining Council Item
                  <small>Check this if this deduction is regulated by a bargaining council</small>
                </label>
              </div>
            </div>
          </div>

          <!-- Form Actions Card -->
          <div class="form-actions-card">
            <div class="form-actions">
              <a href="/clients/<%= company.companyCode %>/settings/custom-items" class="btn-cancel">Cancel</a>
              <button type="submit" class="btn-submit">Save</button>
            </div>
          </div>
        </form>
      </div>
    </main>

    <!-- Floating Action Button for Mobile -->
    <button class="fab-add-item" onclick="window.history.back()" aria-label="Go Back">
      <i class="ph ph-arrow-left"></i>
    </button>

    <%- include('../partials/mobile-bottom-nav', { req: { path: `/clients/${company.companyCode}/settings/custom-items` }, company: company }) %>

    <script>
      // Conditional field visibility logic
      document.addEventListener('DOMContentLoaded', function() {
        const inputTypeSelect = document.getElementById('inputType');
        const conditionalFieldsCard = document.getElementById('conditionalFieldsCard');

        // Define field mappings for each input type
        const fieldMappings = {
          'fixedAmount': ['amountField', 'proRataField'],
          'amountPerEmployee': ['proRataField'],
          'differentEveryPayslip': [],
          'hourlyRateFactor': [],
          'customRateQuantity': [],
          'percentageOfIncome': [],
          'monthlyForNonMonthly': []
        };

        // Function to show/hide conditional fields
        function updateConditionalFields() {
          const selectedType = inputTypeSelect.value;
          const fieldsToShow = fieldMappings[selectedType] || [];

          // Hide all conditional fields first
          document.querySelectorAll('.conditional-field').forEach(field => {
            field.style.display = 'none';
          });

          // Show/hide the conditional fields card
          if (fieldsToShow.length > 0) {
            conditionalFieldsCard.style.display = 'block';

            // Show relevant fields
            fieldsToShow.forEach(fieldId => {
              const field = document.getElementById(fieldId);
              if (field) {
                field.style.display = 'block';
              }
            });
          } else {
            conditionalFieldsCard.style.display = 'none';
          }

          // Update required attributes
          updateRequiredFields(selectedType);
        }

        // Function to update required attributes based on input type
        function updateRequiredFields(inputType) {
          // Remove all conditional required attributes first
          document.querySelectorAll('.conditional-field input, .conditional-field select').forEach(input => {
            input.removeAttribute('required');
          });

          // Add required attributes based on input type
          switch (inputType) {
            case 'fixedAmount':
              document.getElementById('amount').setAttribute('required', 'required');
              break;
          }
        }

        // Event listeners
        inputTypeSelect.addEventListener('change', updateConditionalFields);

        // Initialize on page load
        updateConditionalFields();
      });

      // Enhanced form submission handler
      document.getElementById('customItemForm').addEventListener('submit', async function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const data = {};

        // Process form data
        formData.forEach((value, key) => {
          if (key === 'excludeFromAccounting' || key === 'bargainingCouncilItem' || key === 'enableProRata') {
            data[key] = value === 'on';
          } else {
            data[key] = value;
          }
        });

        // Only include visible/relevant fields in submission
        const inputType = data.inputType;
        const visibleFields = ['name', 'inputType', 'type', '_csrf', 'excludeFromAccounting', 'bargainingCouncilItem'];

        // Add conditional fields based on input type
        switch (inputType) {
          case 'fixedAmount':
            visibleFields.push('amount', 'enableProRata');
            break;
          case 'amountPerEmployee':
            visibleFields.push('enableProRata');
            break;
        }

        // Filter data to only include visible fields
        const filteredData = {};
        visibleFields.forEach(field => {
          if (data.hasOwnProperty(field)) {
            filteredData[field] = data[field];
          }
        });

        try {
          const response = await fetch(this.action, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'CSRF-Token': document.querySelector('input[name="_csrf"]').value,
            },
            body: JSON.stringify(filteredData),
          });

          const result = await response.json();

          if (result.success) {
            window.location.href = `/clients/<%= company.companyCode %>/settings/custom-items`;
          } else {
            alert('Error: ' + (result.message || 'Failed to save custom deduction item'));
          }
        } catch (error) {
          console.error('Error:', error);
          alert('Error saving custom deduction item: ' + error.message);
        }
      });
    </script>
  </body>
</html>