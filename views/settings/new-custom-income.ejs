<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>New Custom Income - <%= company.name %></title>
    <meta name="company-code" content="<%= company.companyCode %>" />
    <meta name="csrf-token" content="<%= csrfToken %>">

    <!-- Modern Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />

    <!-- Stylesheets -->
    <link rel="stylesheet" href="/css/styles.css" />
    <link rel="stylesheet" href="/css/customs.css" />
    <link rel="stylesheet" href="/css/employeeManagement.css" />
    <link rel="stylesheet" href="/css/employeeProfile.css" />
    <link rel="stylesheet" href="/css/header.css" />
    <link rel="stylesheet" href="/css/sidebar.css" />
    <link rel="stylesheet" href="/css/mobile-header.css" />
    <link rel="stylesheet" href="/css/mobile-nav.css" />
    <link rel="stylesheet" href="/css/custom-items.css" />
    <link rel="stylesheet" href="/css/notifications.css" />
    <link rel="stylesheet" href="/css/settings.css" />
    <link rel="stylesheet" href="/css/accounting-settings.css" />

    <!-- Icons -->
    <script src="https://unpkg.com/@phosphor-icons/web"></script>

    <style>
      /* Professional Payroll Application Design - Conservative Business-Focused Design System */
      :root {
        /* Conservative Business Color Palette for Payroll Applications */
        --primary-color: #1e40af;
        --primary-dark: #1e3a8a;
        --primary-light: #3b82f6;
        --secondary-color: #374151;
        --accent-color: #059669;
        --background-color: #f9fafb;
        --card-background: #ffffff;
        --text-primary: #111827;
        --text-secondary: #6b7280;
        --text-muted: #9ca3af;
        --border-color: #e5e7eb;
        --border-light: #f3f4f6;
        --success-color: #059669;
        --warning-color: #d97706;
        --danger-color: #dc2626;
        --info-color: #0369a1;
        --hover-color: rgba(30, 64, 175, 0.05);
        --focus-color: rgba(30, 64, 175, 0.1);
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
      }

      /* Global Font Standardization - Enterprise Grade Typography */
      body,
      html,
      * {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }

      /* Layout wrapper improvements */
      .layout-wrapper {
        display: flex;
        min-height: 100vh;
        background: var(--background-color);
        font-family: 'Inter', sans-serif;
      }

      .content-wrapper {
        flex: 1;
        margin-left: 280px;
        display: flex;
        flex-direction: column;
      }

      /* Main container styling - Professional spacing and constraints */
      .main-container {
        flex: 1;
        padding: 2rem;
        background: var(--background-color);
        min-height: calc(100vh - 80px);
        max-width: 1200px;
        margin: 0 auto;
        width: 100%;
        box-sizing: border-box;
      }

      /* Professional Enterprise Page Header */
      .page-header {
        background: var(--card-background);
        border-radius: 8px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-sm);
        border: 1px solid var(--border-color);
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .header-content h1 {
        font-size: 1.875rem;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
        font-family: 'Inter', sans-serif;
        letter-spacing: -0.025em;
      }

      .description {
        color: var(--text-secondary);
        font-size: 0.9375rem;
        margin: 0;
        line-height: 1.5;
        font-weight: 400;
      }

      .company-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1rem;
        background: var(--border-light);
        border-radius: 6px;
        color: var(--text-secondary);
        font-size: 0.8125rem;
        font-weight: 500;
        border: 1px solid var(--border-color);
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }

      .company-badge i {
        font-size: 1rem;
        color: var(--primary-color);
      }

      /* Professional Enterprise Breadcrumb Navigation */
      .breadcrumb-nav {
        margin-bottom: 1rem;
      }

      .back-link {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--text-secondary);
        text-decoration: none;
        font-size: 0.8125rem;
        font-weight: 500;
        padding: 0.5rem 0;
        transition: all 0.2s ease;
      }

      .back-link:hover {
        color: var(--primary-color);
        transform: translateX(-2px);
      }

      .back-link i {
        font-size: 0.875rem;
      }

      /* Professional Enterprise Form Container */
      .form-container {
        max-width: 100%;
        margin: 0;
        width: 100%;
        padding: 0;
      }

      /* Professional form styling - Enterprise grade */
      .custom-item-form {
        display: flex;
        flex-direction: column;
        gap: 0;
        width: 100%;
      }

      /* Professional Enterprise Form Cards */
      .form-card {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: 8px;
        padding: 2rem;
        transition: all 0.2s ease;
        margin-bottom: 1.5rem;
        box-shadow: var(--shadow-sm);
        width: 100%;
        box-sizing: border-box;
      }

      .form-card:hover {
        border-color: var(--primary-light);
        box-shadow: var(--shadow-md);
        transform: translateY(-1px);
      }

      .form-card h3 {
        font-size: 1.125rem;
        font-weight: 700;
        color: var(--text-primary);
        margin: 0 0 1.5rem 0;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid var(--border-color);
        font-family: 'Inter', sans-serif;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        font-size: 0.875rem;
      }

      /* Remove section headers for cleaner cards */
      .form-card.no-header h3 {
        display: none;
      }

      .form-card.no-header {
        padding-top: 2rem;
      }

      /* Professional Enterprise Form Grid Layout */
      .form-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 1.5rem;
        margin-top: 0.5rem;
        max-width: none;
      }

      .form-group.full-width {
        grid-column: 1 / -1;
      }

      /* Professional Enterprise Form Group Styling */
      .form-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        margin-bottom: 1rem;
        width: 100%;
      }

      .form-group label {
        font-weight: 600;
        color: var(--text-primary);
        font-size: 0.875rem;
        font-family: 'Inter', sans-serif;
        margin-bottom: 0.25rem;
      }

      /* Professional Enterprise Input Field Styling */
      .form-group input[type="text"],
      .form-group input[type="number"],
      .form-group select,
      .form-group textarea {
        width: 100%;
        max-width: none;
        padding: 0.75rem 1rem;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        font-size: 0.875rem;
        font-family: 'Inter', sans-serif;
        transition: all 0.2s ease;
        background: var(--card-background);
        color: var(--text-primary);
        min-height: 2.75rem;
        box-sizing: border-box;
      }

      .form-group textarea {
        min-height: 3.5rem;
        resize: vertical;
      }

      .form-group input:focus,
      .form-group select:focus,
      .form-group textarea:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px var(--focus-color);
      }

      .form-group input:hover,
      .form-group select:hover,
      .form-group textarea:hover {
        border-color: var(--primary-light);
      }

      /* Professional Enterprise Helper Text */
      .form-group small {
        color: var(--text-secondary);
        font-size: 0.75rem;
        line-height: 1.4;
        margin-top: 0.25rem;
        font-weight: 400;
      }

      /* Professional Enterprise Checkbox Styling */
      .checkbox-label {
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
        cursor: pointer;
        padding: 0.75rem 1rem;
        font-weight: 500;
        color: var(--text-primary);
        font-size: 0.875rem;
        background: var(--border-light);
        border-radius: 6px;
        border: 1px solid var(--border-color);
        transition: all 0.2s ease;
        margin-bottom: 0.75rem;
        width: 100%;
        box-sizing: border-box;
      }

      .checkbox-label:hover {
        background: var(--hover-color);
        border-color: var(--primary-color);
      }

      .checkbox-label input[type="checkbox"] {
        width: 1.125rem;
        height: 1.125rem;
        margin: 0;
        margin-top: 0.125rem;
        accent-color: var(--primary-color);
        flex-shrink: 0;
      }

      /* Professional Enterprise Checkbox Group */
      .checkbox-group {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 0.75rem;
        margin-top: 0.75rem;
        padding: 0.5rem 0;
      }

      .checkbox-group label {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: normal;
        font-size: 0.8125rem;
        cursor: pointer;
        padding: 0.5rem 0.75rem;
        border-radius: 6px;
        transition: all 0.2s ease;
        border: 1px solid var(--border-color);
        background: var(--card-background);
      }

      .checkbox-group label:hover {
        background: var(--hover-color);
        border-color: var(--primary-color);
      }

      .checkbox-group input[type="checkbox"] {
        width: 1rem;
        height: 1rem;
        margin: 0;
        accent-color: var(--primary-color);
      }

      /* Professional Enterprise NEW Badge */
      .new-badge {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background: var(--success-color);
        color: white;
        font-size: 0.625rem;
        font-weight: 700;
        padding: 0.125rem 0.375rem;
        border-radius: 3px;
        margin-left: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }

      /* Professional Enterprise Formula Help Button */
      .formula-help {
        margin-top: 0.75rem;
      }

      .btn-help {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        background: var(--card-background);
        color: var(--primary-color);
        border: 1px solid var(--primary-color);
        border-radius: 6px;
        font-size: 0.75rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
      }

      .btn-help:hover {
        background: var(--primary-color);
        color: white;
        transform: translateY(-1px);
        box-shadow: var(--shadow-sm);
      }

      /* Professional Enterprise Form Actions Card */
      .form-actions-card {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: 8px;
        padding: 2rem;
        box-shadow: var(--shadow-sm);
        margin-bottom: 1.5rem;
        transition: all 0.2s ease;
      }

      .form-actions-card:hover {
        border-color: var(--primary-light);
        box-shadow: var(--shadow-md);
      }

      /* Professional Enterprise Form Actions */
      .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-start;
        align-items: center;
        width: 100%;
        margin: 0;
        padding: 0;
        border: none;
      }

      .btn-submit {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        background: var(--primary-color);
        color: white;
        border: none;
        border-radius: 6px;
        font-size: 0.875rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        box-shadow: var(--shadow-sm);
        font-family: 'Inter', sans-serif;
        min-height: 2.75rem;
      }

      .btn-submit:hover {
        background: var(--primary-dark);
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
      }

      .btn-cancel {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        background: var(--card-background);
        color: var(--text-secondary);
        border: 1px solid var(--border-color);
        border-radius: 6px;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        font-family: 'Inter', sans-serif;
        min-height: 2.75rem;
      }

      .btn-cancel:hover {
        background: var(--border-light);
        border-color: var(--text-secondary);
        color: var(--text-primary);
        transform: translateY(-1px);
      }

      /* Professional Enterprise Modal Styling */
      .modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
      }

      .modal-content {
        background: var(--card-background);
        border-radius: 8px;
        padding: 2rem;
        max-width: 600px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: var(--shadow-lg);
        border: 1px solid var(--border-color);
      }

      .modal-content h2 {
        color: var(--text-primary);
        margin-bottom: 1rem;
        font-family: 'Inter', sans-serif;
        font-weight: 700;
        font-size: 1.125rem;
      }

      .btn-close {
        background: var(--text-secondary);
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        cursor: pointer;
        margin-top: 1rem;
        font-family: 'Inter', sans-serif;
        font-size: 0.875rem;
        font-weight: 500;
      }

      .btn-close:hover {
        background: var(--text-primary);
      }

      /* Formula help content */
      .formula-help-content {
        color: var(--text-secondary);
        line-height: 1.6;
      }

      .formula-help-content h3 {
        color: var(--text-primary);
        margin-top: 1.5rem;
        margin-bottom: 0.5rem;
      }

      .formula-help-content ul {
        margin: 0.5rem 0;
        padding-left: 1.5rem;
      }

      .formula-help-content code {
        background: #f1f5f9;
        padding: 0.125rem 0.25rem;
        border-radius: 3px;
        font-family: 'Monaco', 'Menlo', monospace;
        font-size: 0.875rem;
      }

      .formula-help-content pre {
        background: #f1f5f9;
        padding: 1rem;
        border-radius: 6px;
        overflow-x: auto;
        font-family: 'Monaco', 'Menlo', monospace;
        font-size: 0.875rem;
      }

      /* Professional Enterprise Conditional Fields */
      .conditional-field {
        transition: all 0.3s ease;
      }

      .conditional-field.hidden {
        display: none !important;
      }

      /* Multi-select styling */
      select[multiple] {
        min-height: 200px;
        padding: 1rem;
        border: 1px solid var(--border-color);
        border-radius: 12px;
        font-family: 'Inter', sans-serif;
        background: var(--card-background);
      }

      select[multiple] option {
        padding: 0.5rem;
        margin: 0.125rem 0;
        border-radius: 6px;
        font-size: 0.9rem;
      }

      select[multiple] option:checked {
        background: var(--primary-color);
        color: white;
      }

      /* Tooltip styling */
      .tooltip-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 1.25rem;
        height: 1.25rem;
        background: var(--text-secondary);
        color: white;
        border-radius: 50%;
        font-size: 0.75rem;
        font-weight: 600;
        margin-left: 0.5rem;
        cursor: help;
        position: relative;
      }

      .tooltip-icon:hover::after {
        content: attr(title);
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        background: var(--text-primary);
        color: white;
        padding: 0.5rem 0.75rem;
        border-radius: 6px;
        font-size: 0.75rem;
        white-space: nowrap;
        z-index: 1000;
        margin-bottom: 0.25rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      }

      .tooltip-icon:hover::before {
        content: '';
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        border: 4px solid transparent;
        border-top-color: var(--text-primary);
        z-index: 1000;
      }

      /* Professional Enterprise Floating Action Button */
      .fab-add-item {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        width: 3rem;
        height: 3rem;
        background: var(--primary-color);
        color: white;
        border: none;
        border-radius: 6px;
        box-shadow: var(--shadow-md);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
        transition: all 0.2s ease;
        z-index: 100;
      }

      .fab-add-item:hover {
        background: var(--primary-dark);
        transform: translateY(-1px);
        box-shadow: var(--shadow-lg);
      }

      /* Professional Enterprise Responsive Design */
      @media (max-width: 1024px) {
        .main-container {
          margin-left: 0;
          padding: 1.5rem;
        }

        .form-container {
          padding: 0;
        }

        .form-card,
        .form-actions-card {
          padding: 1.5rem;
        }

        .page-header {
          flex-direction: column;
          gap: 1rem;
          text-align: center;
          padding: 1.5rem;
        }

        .company-badge {
          align-self: center;
        }
      }

      @media (max-width: 768px) {
        .main-container {
          padding: 1rem;
        }

        .page-header {
          flex-direction: column;
          gap: 1rem;
          text-align: center;
          padding: 1.5rem;
          border-radius: 6px;
        }

        .header-content h1 {
          font-size: 1.5rem;
        }

        .company-badge {
          align-self: center;
          padding: 0.5rem 0.75rem;
          font-size: 0.75rem;
        }

        .form-container {
          padding: 0;
        }

        .form-card,
        .form-actions-card {
          padding: 1.5rem;
          margin-bottom: 1rem;
          border-radius: 6px;
        }

        .form-card h3 {
          font-size: 0.8125rem;
          margin-bottom: 1rem;
        }

        .form-grid {
          grid-template-columns: 1fr;
          gap: 1rem;
        }

        .checkbox-label {
          padding: 0.625rem 0.875rem;
          font-size: 0.8125rem;
        }

        .form-actions {
          flex-direction: column;
          gap: 0.75rem;
        }

        .btn-submit,
        .btn-cancel {
          width: 100%;
          justify-content: center;
          padding: 0.75rem 1.5rem;
        }

        .modal-content {
          padding: 2rem;
          margin: 1rem;
        }
      }

      @media (max-width: 480px) {
        .main-container {
          padding: 0.75rem;
        }

        .page-header {
          padding: 1.25rem;
          border-radius: 6px;
        }

        .header-content h1 {
          font-size: 1.25rem;
        }

        .company-badge {
          padding: 0.375rem 0.625rem;
          font-size: 0.6875rem;
        }

        .form-container {
          padding: 0;
        }

        .form-card,
        .form-actions-card {
          padding: 1.25rem;
          margin-bottom: 0.875rem;
          border-radius: 6px;
        }

        .form-card h3 {
          font-size: 0.75rem;
          margin-bottom: 0.875rem;
        }

        .form-grid {
          gap: 0.875rem;
        }

        .form-group {
          gap: 0.5rem;
        }

        .checkbox-label {
          padding: 0.5rem 0.75rem;
          font-size: 0.75rem;
        }

        .form-actions {
          gap: 0.625rem;
        }

        .btn-submit,
        .btn-cancel {
          padding: 0.625rem 1.25rem;
          font-size: 0.8125rem;
        }

        .fab-add-item {
          bottom: 5rem;
          right: 0.75rem;
          width: 2.75rem;
          height: 2.75rem;
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <%- include('../partials/header') %>
    <%- include('../partials/sidebar', { company: company }) %>

    <main class="main-container">
      <!-- Page Header -->
      <div class="page-header">
        <div class="header-content">
          <div class="breadcrumb-nav">
            <a href="/clients/<%= company.companyCode %>/settings/add-new-custom-item" class="back-link">
              <i class="ph ph-arrow-left"></i>
              <span>Back to Item Types</span>
            </a>
          </div>
          <h1>Add New Custom Income</h1>
          <p class="description">
            Create a new custom income item for your payroll system
          </p>
        </div>
        <div class="company-badge">
          <i class="ph ph-buildings"></i>
          <span><%= company.name %></span>
        </div>
      </div>

      <div class="form-container">
        <form
          id="customItemForm"
          action="/clients/<%= company.companyCode %>/settings/custom-items/new"
          method="POST"
          class="custom-item-form"
        >
          <input type="hidden" name="_csrf" value="<%= csrfToken %>" />
          <input type="hidden" name="type" value="income" />

          <!-- Basic Information Card -->
          <div class="form-card">
            <h3>Basic Information</h3>
            <div class="form-grid">
              <div class="form-group full-width">
                <label for="name">Name *</label>
                <input type="text" id="name" name="name" required />
              </div>

              <div class="form-group full-width">
                <label for="inputType">Input Type *</label>
                <select id="inputType" name="inputType" required>
                  <option value="">Select input type</option>
                  <option value="fixedAmount">Fixed Amount</option>
                  <option value="amountPerEmployee">Enter Amount Per Employee</option>
                  <option value="differentEveryPayslip">Different on Every Payslip</option>
                  <option value="onceOffSpecificPayslips">Once Off for Specific Payslips</option>
                  <option value="customRateQuantity">Custom Rate × Quantity</option>
                  <option value="hourlyRateFactorsHours">Hourly Rate * factors * hours</option>
                  <option value="percentageOfIncome">% of Income</option>
                  <option value="monthlyForNonMonthly">Monthly for Non-Monthly Employees</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Conditional Fields Card -->
          <div class="form-card" id="conditionalFieldsCard" style="display: none;">
            <h3>Additional Configuration</h3>
            <div class="form-grid">
              <!-- Fixed Amount Fields -->
              <div class="form-group full-width conditional-field" id="amountField" style="display: none;">
                <label for="amount">Amount *</label>
                <input type="number" id="amount" name="amount" step="0.01" min="0" />
              </div>

              <!-- Pro-Rata Checkbox (for multiple input types) -->
              <div class="form-group full-width conditional-field" id="proRataField" style="display: none;">
                <label class="checkbox-label">
                  <input type="checkbox" id="enableProRata" name="enableProRata" />
                  Enable Pro-Rata
                  <span class="tooltip-icon" title="Can't be changed if used on a finalised payslip.">ⓘ</span>
                </label>
              </div>

              <!-- Custom Rate × Quantity Fields -->
              <div class="form-group full-width conditional-field" id="hoursWorkedFactorField" style="display: none;">
                <label for="hoursWorkedFactor">Hours Worked Factor</label>
                <input type="number" id="hoursWorkedFactor" name="hoursWorkedFactor" step="0.01" min="0" value="1" />
              </div>

              <div class="form-group full-width conditional-field" id="differentRateField" style="display: none;">
                <label class="checkbox-label">
                  <input type="checkbox" id="differentRateForEveryEmployee" name="differentRateForEveryEmployee" />
                  Different Rate for Every Employee
                </label>
              </div>

              <div class="form-group full-width conditional-field" id="customRateField" style="display: none;">
                <label for="customRate">Custom Rate</label>
                <input type="number" id="customRate" name="customRate" step="0.01" min="0" />
              </div>

              <!-- Hourly Rate * factors * hours Fields -->
              <div class="form-group full-width conditional-field" id="rateFactorField" style="display: none;">
                <label for="rateFactor">
                  Rate Factor
                  <span class="tooltip-icon" title="e.g. 1.5 for 1.5x hourly rate">ⓘ</span>
                </label>
                <input type="number" id="rateFactor" name="rateFactor" step="0.01" min="0" />
              </div>

              <div class="form-group full-width conditional-field" id="hoursWorkedFactorFieldNew" style="display: none;">
                <label for="hoursWorkedFactorNew">
                  Hours Worked Factor
                  <span class="tooltip-icon" title="This should be 1 for most cases">ⓘ</span>
                </label>
                <input type="number" id="hoursWorkedFactorNew" name="hoursWorkedFactorNew" step="0.01" min="0" value="1" />
              </div>

              <!-- % of Income Fields -->
              <div class="form-group full-width conditional-field" id="incomeItemsField" style="display: none;">
                <label for="incomeItems">Select Income Items *</label>
                <select id="incomeItems" name="incomeItems" multiple size="8">
                  <option value="basicSalary">Basic Salary</option>
                  <option value="basicHourlyPay">Basic Hourly Pay</option>
                  <option value="overtime">Overtime</option>
                  <option value="shortTime">Short Time</option>
                  <option value="sundayPay">Sunday Pay</option>
                  <option value="sundayOvertime">Sunday Overtime</option>
                  <option value="publicHolidayWorked">Public Holiday - Worked</option>
                  <option value="publicHolidayNotWorked">Public Holiday - Not Worked</option>
                  <option value="annualLeavePay">Annual Leave Pay</option>
                  <option value="sickLeavePay">Sick Leave Pay</option>
                  <option value="familyResponsibilityPay">Family Responsibility Pay</option>
                  <option value="annualLeavePayExtra">Annual Leave Pay Extra</option>
                  <option value="unpaidLeave">Unpaid Leave</option>
                  <option value="customRates">Custom Rates</option>
                </select>
                <small>Hold Ctrl (Cmd on Mac) to select multiple items</small>
              </div>

              <!-- Monthly for Non-Monthly Amount Field -->
              <div class="form-group full-width conditional-field" id="monthlyAmountField" style="display: none;">
                <label for="monthlyAmount">Amount *</label>
                <input type="number" id="monthlyAmount" name="monthlyAmount" step="0.01" min="0" />
              </div>
            </div>
          </div>

          <!-- Income Options Card -->
          <div class="form-card">
            <h3>Income Configuration</h3>
            <div class="form-grid">
              <div class="form-group full-width">
                <label class="checkbox-label">
                  <input type="checkbox" id="taxedAnnually" name="taxedAnnually" />
                  Taxed Annually (Irregular Income)
                </label>
              </div>

              <div class="form-group full-width">
                <label class="checkbox-label">
                  <input type="checkbox" id="includeInFluctuatingLeaveRate" name="includeInFluctuatingLeaveRate" />
                  Include in Fluctuating Leave Rate
                </label>
              </div>

              <div class="form-group full-width">
                <label class="checkbox-label">
                  <input type="checkbox" id="overtime" name="overtime" />
                  Overtime
                </label>
                <small>Only applicable from 2020 tax year.</small>
              </div>

              <div class="form-group full-width">
                <label class="checkbox-label">
                  <input type="checkbox" id="affectsWageForETI" name="affectsWageForETI" />
                  Affects Wage for ETI Purposes
                </label>
              </div>

              <div class="form-group full-width">
                <label class="checkbox-label">
                  <input type="checkbox" id="includeInCostToCompany" name="includeInCostToCompany" />
                  Include in Cost to Company Calculation
                  <span class="new-badge">NEW</span>
                </label>
              </div>
            </div>
          </div>

          <!-- Form Actions Card -->
          <div class="form-actions-card">
            <div class="form-actions">
              <a href="/clients/<%= company.companyCode %>/settings/custom-items" class="btn-cancel">Cancel</a>
              <button type="submit" class="btn-submit">Save</button>
            </div>
          </div>
        </form>
      </div>
    </main>

    <!-- Floating Action Button for Mobile -->
    <button class="fab-add-item" onclick="window.history.back()" aria-label="Go Back">
      <i class="ph ph-arrow-left"></i>
    </button>

    <%- include('../partials/mobile-bottom-nav', { req: { path: `/clients/${company.companyCode}/settings/custom-items` }, company: company }) %>

    <script>
      // Conditional field visibility logic
      document.addEventListener('DOMContentLoaded', function() {
        const inputTypeSelect = document.getElementById('inputType');
        const conditionalFieldsCard = document.getElementById('conditionalFieldsCard');
        const differentRateCheckbox = document.getElementById('differentRateForEveryEmployee');
        const customRateField = document.getElementById('customRateField');

        // Define field mappings for each input type
        const fieldMappings = {
          'fixedAmount': ['amountField', 'proRataField'],
          'amountPerEmployee': ['proRataField'],
          'differentEveryPayslip': [],
          'onceOffSpecificPayslips': [],
          'customRateQuantity': ['hoursWorkedFactorField', 'differentRateField', 'customRateField'],
          'hourlyRateFactorsHours': ['rateFactorField', 'hoursWorkedFactorFieldNew'],
          'percentageOfIncome': ['incomeItemsField'],
          'monthlyForNonMonthly': ['monthlyAmountField']
        };

        // Function to show/hide conditional fields
        function updateConditionalFields() {
          const selectedType = inputTypeSelect.value;
          const fieldsToShow = fieldMappings[selectedType] || [];

          // Hide all conditional fields first
          document.querySelectorAll('.conditional-field').forEach(field => {
            field.style.display = 'none';
          });

          // Show/hide the conditional fields card
          if (fieldsToShow.length > 0) {
            conditionalFieldsCard.style.display = 'block';

            // Show relevant fields
            fieldsToShow.forEach(fieldId => {
              const field = document.getElementById(fieldId);
              if (field) {
                field.style.display = 'block';
              }
            });
          } else {
            conditionalFieldsCard.style.display = 'none';
          }

          // Handle nested logic for Custom Rate × Quantity
          if (selectedType === 'customRateQuantity') {
            updateCustomRateVisibility();
          }

          // Update required attributes
          updateRequiredFields(selectedType);
        }

        // Function to handle custom rate visibility based on checkbox
        function updateCustomRateVisibility() {
          if (differentRateCheckbox.checked) {
            customRateField.style.display = 'none';
            document.getElementById('customRate').removeAttribute('required');
          } else {
            customRateField.style.display = 'block';
          }
        }

        // Function to update required attributes based on input type
        function updateRequiredFields(inputType) {
          // Remove all conditional required attributes first
          document.querySelectorAll('.conditional-field input, .conditional-field select').forEach(input => {
            input.removeAttribute('required');
          });

          // Add required attributes based on input type
          switch (inputType) {
            case 'fixedAmount':
              document.getElementById('amount').setAttribute('required', 'required');
              break;
            case 'percentageOfIncome':
              document.getElementById('incomeItems').setAttribute('required', 'required');
              break;
            case 'monthlyForNonMonthly':
              document.getElementById('monthlyAmount').setAttribute('required', 'required');
              break;
          }
        }

        // Event listeners
        inputTypeSelect.addEventListener('change', updateConditionalFields);
        differentRateCheckbox.addEventListener('change', updateCustomRateVisibility);

        // Initialize on page load
        updateConditionalFields();
      });

      // Enhanced form submission handler
      document.getElementById('customItemForm').addEventListener('submit', async function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const data = {};

        // Process form data
        formData.forEach((value, key) => {
          if (key === 'taxedAnnually' || key === 'includeInFluctuatingLeaveRate' ||
              key === 'overtime' || key === 'affectsWageForETI' || key === 'includeInCostToCompany' ||
              key === 'enableProRata' || key === 'differentRateForEveryEmployee') {
            data[key] = value === 'on';
          } else if (key === 'incomeItems') {
            // Handle multi-select values
            if (!data[key]) data[key] = [];
            data[key].push(value);
          } else {
            data[key] = value;
          }
        });

        // Only include visible/relevant fields in submission
        const inputType = data.inputType;
        const visibleFields = ['name', 'inputType', 'type', '_csrf', 'taxedAnnually',
                              'includeInFluctuatingLeaveRate', 'overtime', 'affectsWageForETI',
                              'includeInCostToCompany'];

        // Add conditional fields based on input type
        switch (inputType) {
          case 'fixedAmount':
            visibleFields.push('amount', 'enableProRata');
            break;
          case 'amountPerEmployee':
            visibleFields.push('enableProRata');
            break;
          case 'customRateQuantity':
            visibleFields.push('hoursWorkedFactor', 'differentRateForEveryEmployee');
            if (!data.differentRateForEveryEmployee) {
              visibleFields.push('customRate');
            }
            break;
          case 'hourlyRateFactorsHours':
            visibleFields.push('rateFactor', 'hoursWorkedFactorNew');
            break;
          case 'percentageOfIncome':
            visibleFields.push('incomeItems');
            break;
          case 'monthlyForNonMonthly':
            visibleFields.push('monthlyAmount');
            break;
        }

        // Filter data to only include visible fields
        const filteredData = {};
        visibleFields.forEach(field => {
          if (data.hasOwnProperty(field)) {
            filteredData[field] = data[field];
          }
        });

        try {
          const response = await fetch(this.action, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'CSRF-Token': document.querySelector('input[name="_csrf"]').value,
            },
            body: JSON.stringify(filteredData),
          });

          const result = await response.json();

          if (result.success) {
            window.location.href = `/clients/<%= company.companyCode %>/settings/custom-items`;
          } else {
            alert('Error: ' + (result.message || 'Failed to save custom income item'));
          }
        } catch (error) {
          console.error('Error:', error);
          alert('Error saving custom income item: ' + error.message);
        }
      });
    </script>
  </body>
</html> 