<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Settings - <%= company.name %></title>
    
    <!-- Modern Font -->
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <!-- Phosphor Icons -->
    <script src="https://unpkg.com/@phosphor-icons/web"></script>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/styles.css" />
    <link rel="stylesheet" href="/css/onboarding.css" />
    <link rel="stylesheet" href="/css/employeeManagement.css" />
    <link rel="stylesheet" href="/css/header.css" />
    <link rel="stylesheet" href="/css/sidebar.css" />
    <link rel="stylesheet" href="/css/settings.css" />
    <link rel="stylesheet" href="/css/accounting-settings.css" />
    <link rel="stylesheet" href="/css/mobile-nav.css" />
    <link rel="stylesheet" href="/css/feature-status.css" />
    <link rel="stylesheet" href="/css/dark-mode.css" />
  </head>

  <body>
    <div class="layout-wrapper">
      <%- include('../partials/sidebar', { user: user, company: company }) %>
      <div class="content-wrapper">
        <%- include('../partials/header', { user: user, company: company, currentPage: 'settings' }) %>

        <main class="main-container">
          <!-- Feature Status Banner -->
          <div style="margin-top: 6.5rem;">
            <%- include('../partials/feature-status', { featureKey: 'OTHER_SETTINGS' }) %>
          </div>

          <!-- Tabs Section -->
          <div id="tabs-section">
        <div class="tab-row main-tabs">
          <a
            href="/clients/<%= company.companyCode %>/settings/accounting"
            class="tab-button"
          >
            <i class="ph ph-calculator"></i>
            Accounting
          </a>
          <a
            href="/clients/<%= company.companyCode %>/settings/employee"
            class="tab-button"
          >
            <i class="ph ph-users"></i>
            Employee
          </a>
          <a
            href="/clients/<%= company.companyCode %>/settings/payroll"
            class="tab-button"
          >
            <i class="ph ph-money"></i>
            Payroll
          </a>
          <a
            href="/clients/<%= company.companyCode %>/settings/other"
            class="tab-button <%= activeTab === 'other' ? 'active' : '' %>"
          >
            <i class="ph ph-gear"></i>
            Other
          </a>
        </div>
      </div>

      <!-- Settings Grid -->
      <div class="settings-grid">
        <% if (isFeatureAvailable('OTHER_SETTINGS')) { %>
          <a
            href="/clients/<%= company.companyCode %>/settings/other/templates"
            class="settings-card"
          >
            <h3>
              <i class="ph ph-file-text"></i>
              Document Templates
            </h3>
            <p>Manage email and document templates</p>
          </a>

          <a
            href="/clients/<%= company.companyCode %>/settings/other/advanced"
            class="settings-card"
          >
            <h3>
              <i class="ph ph-sliders"></i>
              Advanced
            </h3>
            <p>Configure advanced system settings</p>
          </a>
        <% } else { %>
          <div class="settings-card disabled" title="Feature not available">
            <h3>
              <i class="ph ph-file-text"></i>
              Document Templates
            </h3>
            <p>Manage email and document templates</p>
            <div class="feature-overlay">
              <i class="ph ph-lock"></i>
            </div>
          </div>

          <div class="settings-card disabled" title="Feature not available">
            <h3>
              <i class="ph ph-sliders"></i>
              Advanced
            </h3>
            <p>Configure advanced system settings</p>
            <div class="feature-overlay">
              <i class="ph ph-lock"></i>
            </div>
          </div>
        <% } %>
        <!-- WhatsApp card always enabled -->
        <a
          href="/clients/<%= company.companyCode %>/settings/other/whatsapp"
          class="settings-card"
        >
          <h3>
            <i class="ph ph-whatsapp-logo"></i>
            WhatsApp
          </h3>
          <p>Configure WhatsApp integration and notifications</p>
        </a>
      </div>
        </main>
      </div>
    </div>

    <%- include('../partials/mobile-bottom-nav', { req: { path: `/clients/${company.companyCode}/settings/other` }, company: company }) %>

    <script src="/include.js"></script>
    <script src="/script.js"></script>
    <script src="/main.js"></script>
    <script src="/tab.js"></script>
  </body>
</html>
