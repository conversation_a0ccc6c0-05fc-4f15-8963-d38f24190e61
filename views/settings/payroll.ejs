<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Settings - <%= company.name %></title>
    
    <!-- Modern Font -->
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <!-- Phosphor Icons -->
    <script src="https://unpkg.com/@phosphor-icons/web"></script>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/styles.css" />
    <link rel="stylesheet" href="/css/onboarding.css" />
    <link rel="stylesheet" href="/css/employeeManagement.css" />
    <link rel="stylesheet" href="/css/header.css" />
    <link rel="stylesheet" href="/css/sidebar.css" />
    <link rel="stylesheet" href="/css/settings.css" />
    <link rel="stylesheet" href="/css/accounting-settings.css" />
    <link rel="stylesheet" href="/css/mobile-nav.css" />
    <link rel="stylesheet" href="/css/feature-status.css" />
    <link rel="stylesheet" href="/css/dark-mode.css" />
  </head>

  <body>
    <div class="layout-wrapper">
      <%- include('../partials/sidebar', { user: user, company: company }) %>
      <div class="content-wrapper">
        <%- include('../partials/header', { user: user, company: company, currentPage: 'settings' }) %>

        <main class="main-container">
          <!-- Tabs Section -->
          <div id="tabs-section" style="margin-top: 6.5rem;">
        <div class="tab-row main-tabs">
          <a
            href="/clients/<%= company.companyCode %>/settings/accounting"
            class="tab-button"
          >
            <i class="ph ph-calculator"></i>
            <span>Accounting</span>
          </a>
          <a
            href="/clients/<%= company.companyCode %>/settings/employee"
            class="tab-button"
          >
            <i class="ph ph-users"></i>
            <span>Employee</span>
          </a>
          <a
            href="/clients/<%= company.companyCode %>/settings/payroll"
            class="tab-button <%= activeTab === 'payroll' ? 'active' : '' %>"
          >
            <i class="ph ph-money"></i>
            <span>Payroll</span>
          </a>
          <a
            href="/clients/<%= company.companyCode %>/settings/other"
            class="tab-button"
          >
            <i class="ph ph-gear"></i>
            <span>Other</span>
          </a>
        </div>
      </div>

      <!-- Settings Grid -->
      <div class="settings-grid">
        <!-- Leave Settings (Active) -->
        <a
          href="/clients/<%= company.companyCode %>/settings/payroll/leave"
          class="settings-card"
        >
          <div class="card-header">
            <i class="ph ph-calendar"></i>
            <h3>Leave</h3>
          </div>
          <p>Configure leave types and policies</p>
        </a>

        <!-- Pay Frequencies Settings (Active) -->
        <a
          href="/clients/<%= company.companyCode %>/settings/payroll/pay-frequencies"
          class="settings-card"
        >
          <div class="card-header">
            <i class="ph ph-clock"></i>
            <h3>Pay Frequencies</h3>
          </div>
          <p>Set up payment frequency options</p>
        </a>

        <!-- Pay Points Settings (Locked) -->
        <% if (isFeatureAvailable('PAY_POINTS_SETTINGS')) { %>
          <a
            href="/clients/<%= company.companyCode %>/settings/payroll/pay-points"
            class="settings-card"
          >
            <div class="card-header">
              <i class="ph ph-map-pin"></i>
              <h3>Pay Points</h3>
            </div>
            <p>Manage payment points and locations</p>
          </a>
        <% } else { %>
          <div class="settings-card disabled" title="Feature not available">
            <div class="card-header">
              <i class="ph ph-map-pin"></i>
              <h3>Pay Points</h3>
            </div>
            <p>Manage payment points and locations</p>
            <div class="feature-overlay">
              <i class="ph ph-lock"></i>
            </div>
            <%- include('../partials/feature-status', { featureKey: 'PAY_POINTS_SETTINGS' }) %>
          </div>
        <% } %>

        <!-- Payroll Calculations Settings (Locked) -->
        <% if (isFeatureAvailable('PAYROLL_CALCULATIONS_SETTINGS')) { %>
          <a
            href="/clients/<%= company.companyCode %>/settings/payroll/payroll-calculations"
            class="settings-card"
          >
            <div class="card-header">
              <i class="ph ph-calculator"></i>
              <h3>Payroll Calculations</h3>
            </div>
            <p>Configure calculation rules and formulas</p>
          </a>
        <% } else { %>
          <div class="settings-card disabled" title="Feature not available">
            <div class="card-header">
              <i class="ph ph-calculator"></i>
              <h3>Payroll Calculations</h3>
            </div>
            <p>Configure calculation rules and formulas</p>
            <div class="feature-overlay">
              <i class="ph ph-lock"></i>
            </div>
            <%- include('../partials/feature-status', { featureKey: 'PAYROLL_CALCULATIONS_SETTINGS' }) %>
          </div>
        <% } %>

        <!-- Payslip Settings (Locked) -->
        <% if (isFeatureAvailable('PAYSLIP_SETTINGS')) { %>
          <a
            href="/clients/<%= company.companyCode %>/settings/payroll/payslip"
            class="settings-card"
          >
            <div class="card-header">
              <i class="ph ph-file-text"></i>
              <h3>Payslip</h3>
            </div>
            <p>Customize payslip layout and content</p>
          </a>
        <% } else { %>
          <div class="settings-card disabled" title="Feature not available">
            <div class="card-header">
              <i class="ph ph-file-text"></i>
              <h3>Payslip</h3>
            </div>
            <p>Customize payslip layout and content</p>
            <div class="feature-overlay">
              <i class="ph ph-lock"></i>
            </div>
            <%- include('../partials/feature-status', { featureKey: 'PAYSLIP_SETTINGS' }) %>
          </div>
        <% } %>
      </div>
        </main>
      </div>
    </div>

    <%- include('../partials/mobile-bottom-nav', { req: { path: `/clients/${company.companyCode}/settings/payroll` }, company: company }) %>

    <script src="/include.js"></script>
    <script src="/script.js"></script>
    <script src="/main.js"></script>
    <script src="/tab.js"></script>
  </body>
</html>
