<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>WhatsApp Settings | <%= company.name %></title>

    <!-- Modern Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />

    <!-- Styles -->
    <link rel="stylesheet" href="/css/styles.css" />
    <link rel="stylesheet" href="/css/onboarding.css" />
    <link rel="stylesheet" href="/css/employeeManagement.css" />
    <link rel="stylesheet" href="/css/header.css" />
    <link rel="stylesheet" href="/css/sidebar.css" />
    <link rel="stylesheet" href="/css/mobile-header.css" />
    <link rel="stylesheet" href="/css/mobile-nav.css" />
    <link rel="stylesheet" href="/css/mobile-employee.css" />

    <!-- Icons -->
    <script type="module" src="https://unpkg.com/@phosphor-icons/web@2.1.5"></script>
    <style>
      .ph {
        display: inline-block;
        font-size: 1.2em;
        line-height: 1;
        vertical-align: middle;
      }
      .tab-button.active {
        background: #6366f1;
        color: #fff;
      }
      .tab-content { display: none; }
      .tab-content.active { display: block; }
      /* Modal Styles */
      .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100vw;
        height: 100vh;
        overflow: auto;
        background: rgba(30, 41, 59, 0.35);
        backdrop-filter: blur(2px);
        justify-content: center;
        align-items: center;
        transition: opacity 0.2s;
      }
      .modal.show {
        display: flex !important;
      }
      .modal-content {
        background: #fff;
        margin: auto;
        border-radius: 1rem;
        padding: 2rem 2rem 1.5rem 2rem;
        box-shadow: 0 8px 32px rgba(0,0,0,0.18);
        max-width: 500px;
        width: 100%;
        position: relative;
        animation: modalIn 0.2s;
      }
      @keyframes modalIn {
        from { transform: translateY(40px) scale(0.98); opacity: 0; }
        to { transform: translateY(0) scale(1); opacity: 1; }
      }
      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
      }
      .modal-header h2 {
        margin: 0;
        font-size: 1.5rem;
        font-weight: 600;
        color: #1f2937;
      }
      .modal-header .close {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: #6b7280;
        transition: color 0.2s;
      }
      .modal-header .close:hover {
        color: #1f2937;
      }
      .modal-body {
        margin-bottom: 1.5rem;
      }
      .modal-footer {
        display: flex;
        justify-content: flex-end;
        gap: 1rem;
        margin-top: 1.5rem;
      }
      .primary-btn {
        background: #8B5CF6;
        color: #fff;
        border: none;
        border-radius: 0.5rem;
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
        font-weight: 500;
        cursor: pointer;
        transition: background 0.2s;
      }
      .primary-btn:disabled {
        background: #a5b4fc;
        cursor: not-allowed;
      }
      .primary-btn:hover:not(:disabled) {
        background: #4338ca;
      }
      .secondary-btn {
        background: #f3f4f6;
        color: #374151;
        border: none;
        border-radius: 0.5rem;
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
        font-weight: 500;
        cursor: pointer;
        transition: background 0.2s;
      }
      .secondary-btn:hover {
        background: #e5e7eb;
      }
      /* Employee List Styles */
      #employeeCheckboxList {
        max-height: 300px;
        overflow-y: auto;
        margin: 1rem 0;
        padding: 0.5rem;
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
      }
      .employee-checkbox-item {
        padding: 0.75rem;
        border-bottom: 1px solid #e5e7eb;
      }
      .employee-checkbox-item:last-child {
        border-bottom: none;
      }
      .employee-checkbox-item label {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        cursor: pointer;
      }
      .employee-checkbox-item input[type="checkbox"] {
        width: 1.25rem;
        height: 1.25rem;
        border-radius: 0.25rem;
        border: 2px solid #d1d5db;
        cursor: pointer;
      }
      .no-employees-message {
        padding: 1rem;
        text-align: center;
        color: #6b7280;
        font-style: italic;
      }
      /* Select All Checkbox */
      #selectAllEmployees {
        margin-bottom: 1rem;
        padding: 0.5rem;
        border-bottom: 1px solid #e5e7eb;
      }
      #selectAllEmployees label {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 500;
        color: #374151;
      }
      #selectAllEmployees input[type="checkbox"] {
        width: 1.25rem;
        height: 1.25rem;
        border-radius: 0.25rem;
        border: 2px solid #d1d5db;
      }
      .invite-actions {
        display: flex;
        gap: 1rem;
        margin-bottom: 1.5rem;
        align-items: center;
      }
      .primary-btn, .secondary-btn {
        padding: 0.5rem 1.2rem;
        font-size: 1rem;
        min-width: 120px;
      }
      #inviteAllBtn i, #openInviteSelectedModal i {
        margin-right: 0.5em;
      }
      /* Toast notification styles */
      #toast-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
      }
      .toast {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        margin-bottom: 10px;
        border-radius: 4px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        background: white;
        color: #333;
        opacity: 0;
        transform: translateX(100%);
        transition: opacity 0.3s, transform 0.3s;
      }
      .toast.show {
        opacity: 1;
        transform: translateX(0);
      }
      .toast-success {
        background: #d4edda;
        color: #155724;
      }
      .toast-error {
        background: #f8d7da;
        color: #721c24;
      }
      .toast-warning {
        background: #fff3cd;
        color: #856404;
      }
      .toast-info {
        background: #d1ecf1;
        color: #0c5460;
      }
      .toast i {
        margin-right: 8px;
      }
      .no-data {
        text-align: center;
        padding: 20px;
        color: #6b7280;
        font-style: italic;
      }
      .status-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.875rem;
        font-weight: 500;
        text-transform: capitalize;
      }
      .status-badge.success {
        background-color: #d1fae5;
        color: #065f46;
      }
      .status-badge.failed {
        background-color: #fee2e2;
        color: #991b1b;
      }
    </style>
    <% if (typeof csrfToken !== 'undefined') { %>
      <meta name="csrf-token" content="<%= csrfToken %>">
    <% } %>
  </head>
  <body>
    <div class="layout-wrapper">
      <%- include('../partials/sidebar', { user: user, company: company }) %>
      <div class="content-wrapper">
        <%- include('../partials/header', { user: user, company: company, currentPage: 'settings' }) %>
        <main class="main-container">

          <!-- Invite Actions Bar -->
          <div class="invite-actions" style="margin-top: 6.5rem;">
            <button id="inviteAllBtn" class="primary-btn" data-company-code="<%= company.companyCode %>" title="Invite all employees with a WhatsApp number">
              <i class="ph ph-whatsapp-logo"></i> Invite All
            </button>
            <button id="openInviteSelectedModal" class="secondary-btn" type="button">
              <i class="ph ph-user-list"></i> Invite Specific
            </button>
          </div>

          <!-- Invite Confirmation Modal -->
          <div id="inviteModal" class="modal" style="display:none;">
            <div class="modal-content">
              <div class="modal-header">
                <h3>Invite All Employees to WhatsApp</h3>
                <button class="close" id="closeInviteModal">&times;</button>
              </div>
              <div class="modal-body">
                <p>This will send a WhatsApp message to all employees with cellphone numbers, inviting them to use the ESS portal and explaining what they can do here. Are you sure you want to continue?</p>
              </div>
              <div class="modal-footer">
                <button id="sendInvitesBtn" class="primary-btn">Send Invites</button>
                <button id="cancelInviteBtn" class="secondary-btn">Cancel</button>
              </div>
            </div>
          </div>

          <!-- WhatsApp Invite Selected Employees Modal -->
          <div id="inviteSelectedModal" class="modal" style="display:none;">
            <div class="modal-content">
              <span id="closeInviteSelectedModal" class="close">&times;</span>
              <h2>Invite Specific Employees to WhatsApp</h2>
              <form id="inviteSelectedForm">
                <input type="hidden" name="_csrf" value="<%= csrfToken %>">
                <div>
                  <label><input type="checkbox" id="selectAllEmployees"> Select All</label>
                </div>
                <div id="employeeCheckboxList">
                  <% if (typeof employeesWithWhatsApp !== 'undefined' && employeesWithWhatsApp.length > 0) { %>
                    <% employeesWithWhatsApp.forEach(function(emp) { %>
                      <div class="employee-checkbox-item">
                        <label>
                          <input type="checkbox" name="employeeIds" value="<%= emp._id %>">
                          <%= emp.firstName %> <%= emp.lastName %> (<%= emp.mobileDisplay %>)
                        </label>
                      </div>
                    <% }); %>
                  <% } else { %>
                    <div class="no-employees-message">No eligible employees found with WhatsApp numbers.</div>
                  <% } %>
                </div>
                <div class="modal-footer">
                  <button type="submit" class="primary-btn">Send Invites</button>
                  <button type="button" class="secondary-btn" id="cancelInviteSelectedBtn">Cancel</button>
                </div>
              </form>
            </div>
          </div>

          <!-- Action Tabs -->
          <div class="action-tabs">
            <button class="tab-button active" onclick="showTab('payslipsTab')">
              <i class="ph ph-file-text"></i>
              Payslips
            </button>
            <button class="tab-button" onclick="showTab('irp5Tab')">
              <i class="ph ph-file"></i>
              IRP5's
            </button>
            <button class="tab-button" onclick="showTab('leaveTab')">
              <i class="ph ph-calendar"></i>
              Leave Request
            </button>
          </div>

          <!-- Tab Contents -->
          <%
          let _payslipRequests = (typeof payslipRequests !== 'undefined') ? payslipRequests : [];
          let _irp5Requests = (typeof irp5Requests !== 'undefined') ? irp5Requests : [];
          let _leaveRequests = (typeof leaveRequests !== 'undefined') ? leaveRequests : [];
          %>
          <div id="payslipsTab" class="tab-content active">
            <div class="table-container">
              <table class="modern-table">
                <thead>
                  <tr>
                    <th>Employee First Name</th>
                    <th>Employee Last Name</th>
                    <th>Mobile Number</th>
                    <th>Request Text</th>
                    <th>Timestamp</th>
                    <th>Status</th>
                    <th>Response</th>
                  </tr>
                </thead>
                <tbody>
                  <% if (_payslipRequests && _payslipRequests.length > 0) { %>
                    <% _payslipRequests.forEach(function(req) { 
                      // Safely get employee data
                      const employee = req.employee || {};
                      const firstName = employee.firstName || 'N/A';
                      const lastName = employee.lastName || 'N/A';
                      const mobileNumber = req.mobileNumber || 'N/A';
                      const requestText = req.requestText || 'N/A';
                      const timestamp = req.timestamp ? new Date(req.timestamp).toLocaleString() : 'N/A';
                      const status = req.status || 'unknown';
                      let response = 'N/A';
                      
                      // Try to parse response if it's a stringified JSON
                      try {
                        if (typeof req.response === 'string') {
                          const parsed = JSON.parse(req.response);
                          response = parsed.message || JSON.stringify(parsed);
                        } else if (req.response) {
                          response = typeof req.response === 'object' ? 
                                    (req.response.message || JSON.stringify(req.response)) : 
                                    String(req.response);
                        }
                      } catch (e) {
                        response = String(req.response || 'N/A');
                      }
                    %>
                      <tr>
                        <td><%= firstName %></td>
                        <td><%= lastName %></td>
                        <td><%= mobileNumber %></td>
                        <td><%= requestText %></td>
                        <td><%= timestamp %></td>
                        <td><span class="status-badge <%= status.toLowerCase() %>"><%= status %></span></td>
                        <td title="<%= response %>"><%= response.length > 50 ? response.substring(0, 50) + '...' : response %></td>
                      </tr>
                    <% }); %>
                  <% } else { %>
                    <tr>
                      <td colspan="7" class="no-data">No payslip requests found</td>
                    </tr>
                  <% } %>
                </tbody>
              </table>
            </div>
          </div>
          <div id="irp5Tab" class="tab-content">
            <div class="table-container">
              <table class="modern-table">
                <thead>
                  <tr>
                    <th>Employee First Name</th>
                    <th>Employee Last Name</th>
                    <th>Mobile Number</th>
                    <th>Request Text</th>
                    <th>Timestamp</th>
                    <th>Status</th>
                    <th>Response</th>
                  </tr>
                </thead>
                <tbody>
                  <% if (_irp5Requests && _irp5Requests.length > 0) { %>
                    <% _irp5Requests.forEach(function(req) { 
                      // Safely get employee data
                      const employee = req.employee || {};
                      const firstName = employee.firstName || 'N/A';
                      const lastName = employee.lastName || 'N/A';
                      const mobileNumber = req.mobileNumber || 'N/A';
                      const requestText = req.requestText || 'N/A';
                      const timestamp = req.timestamp ? new Date(req.timestamp).toLocaleString() : 'N/A';
                      const status = req.status || 'unknown';
                      let response = 'N/A';
                      
                      // Try to parse response if it's a stringified JSON
                      try {
                        if (typeof req.response === 'string') {
                          const parsed = JSON.parse(req.response);
                          response = parsed.message || JSON.stringify(parsed);
                        } else if (req.response) {
                          response = typeof req.response === 'object' ? 
                                    (req.response.message || JSON.stringify(req.response)) : 
                                    String(req.response);
                        }
                      } catch (e) {
                        response = String(req.response || 'N/A');
                      }
                    %>
                      <tr>
                        <td><%= firstName %></td>
                        <td><%= lastName %></td>
                        <td><%= mobileNumber %></td>
                        <td><%= requestText %></td>
                        <td><%= timestamp %></td>
                        <td><span class="status-badge <%= status.toLowerCase() %>"><%= status %></span></td>
                        <td title="<%= response %>"><%= response.length > 50 ? response.substring(0, 50) + '...' : response %></td>
                      </tr>
                    <% }); %>
                  <% } else { %>
                    <tr>
                      <td colspan="7" class="no-data">No IRP5 requests found</td>
                    </tr>
                  <% } %>
                </tbody>
              </table>
            </div>
          </div>
          <div id="leaveTab" class="tab-content">
            <div class="table-container">
              <table class="modern-table">
                <thead>
                  <tr>
                    <th>Employee First Name</th>
                    <th>Employee Last Name</th>
                    <th>Mobile Number</th>
                    <th>Request Text</th>
                    <th>Timestamp</th>
                    <th>Status</th>
                    <th>Response</th>
                    <th>Leave Request ID</th>
                  </tr>
                </thead>
                <tbody>
                  <% if (_leaveRequests && _leaveRequests.length > 0) { %>
                    <% _leaveRequests.forEach(function(req) { 
                      // Safely get employee data
                      const employee = req.employee || {};
                      const firstName = employee.firstName || 'N/A';
                      const lastName = employee.lastName || 'N/A';
                      const mobileNumber = req.mobileNumber || 'N/A';
                      const requestText = req.requestText || 'N/A';
                      const timestamp = req.timestamp ? new Date(req.timestamp).toLocaleString() : 'N/A';
                      const status = req.status || 'unknown';
                      let response = 'N/A';
                      
                      // Try to parse response if it's a stringified JSON
                      try {
                        if (typeof req.response === 'string') {
                          const parsed = JSON.parse(req.response);
                          response = parsed.message || JSON.stringify(parsed);
                        } else if (req.response) {
                          response = typeof req.response === 'object' ? 
                                    (req.response.message || JSON.stringify(req.response)) : 
                                    String(req.response);
                        }
                      } catch (e) {
                        response = String(req.response || 'N/A');
                      }
                    %>
                      <tr>
                        <td><%= firstName %></td>
                        <td><%= lastName %></td>
                        <td><%= mobileNumber %></td>
                        <td><%= requestText %></td>
                        <td><%= timestamp %></td>
                        <td><span class="status-badge <%= status.toLowerCase() %>"><%= status %></span></td>
                        <td title="<%= response %>"><%= response.length > 50 ? response.substring(0, 50) + '...' : response %></td>
                        <td><%= req.leaveRequest ? req.leaveRequest.toString() : 'N/A' %></td>
                      </tr>
                    <% }); %>
                  <% } else { %>
                    <tr>
                      <td colspan="8" class="no-data">No leave requests found</td>
                    </tr>
                  <% } %>
                </tbody>
              </table>
            </div>
          </div>
        </main>
      </div>
    </div>
    <%- include('../partials/mobile-bottom-nav', { req: { path: `/clients/${company.companyCode}/settings/other/whatsapp` }, company: company }) %>
    <script>
      function showTab(tabId) {
        document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.tab-content').forEach(tab => tab.classList.remove('active'));
        if (tabId === 'payslipsTab') {
          document.querySelector('.tab-button:nth-child(1)').classList.add('active');
        } else if (tabId === 'irp5Tab') {
          document.querySelector('.tab-button:nth-child(2)').classList.add('active');
        } else if (tabId === 'leaveTab') {
          document.querySelector('.tab-button:nth-child(3)').classList.add('active');
        }
        document.getElementById(tabId).classList.add('active');
      }
    </script>

    <script>
      // Expose server-side data to client-side script
      const payslipRequests = <%- JSON.stringify(_payslipRequests) %>;
      const irp5Requests = <%- JSON.stringify(_irp5Requests) %>;
      const leaveRequests = <%- JSON.stringify(_leaveRequests) %>;
      const employeesWithWhatsApp = <%- JSON.stringify(typeof employeesWithWhatsApp !== 'undefined' ? employeesWithWhatsApp : []) %>;

      console.log("WhatsApp settings data:", {
        payslipRequests: payslipRequests,
        irp5Requests: irp5Requests,
        leaveRequests: leaveRequests,
        employeesWithWhatsApp: employeesWithWhatsApp
      });

      // Debug log for employeesWithWhatsApp
      console.log("Employees with WhatsApp:", employeesWithWhatsApp);
      console.log("Number of employees:", employeesWithWhatsApp.length);
      if (employeesWithWhatsApp.length > 0) {
        console.log("First employee:", employeesWithWhatsApp[0]);
      }
    </script>

    <script>
      // Toast notification function
      function showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        
        // Create icon based on type
        const icon = document.createElement('i');
        icon.className = type === 'success' ? 'ph ph-check-circle' :
                        type === 'error' ? 'ph ph-x-circle' :
                        type === 'warning' ? 'ph ph-warning' :
                        'ph ph-info';
        
        // Create message element
        const messageEl = document.createElement('span');
        messageEl.textContent = message;
        
        // Assemble toast
        toast.appendChild(icon);
        toast.appendChild(messageEl);
        
        // Add to container or create one
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
          toastContainer = document.createElement('div');
          toastContainer.id = 'toast-container';
          document.body.appendChild(toastContainer);
        }
        toastContainer.appendChild(toast);

        // Trigger animation
        setTimeout(() => toast.classList.add('show'), 100);

        // Remove after delay
        setTimeout(() => {
          toast.classList.remove('show');
          setTimeout(() => toast.remove(), 300);
        }, 3000);
      }

      // Initialize WhatsApp invite functionality
      document.addEventListener('DOMContentLoaded', function() {
        // Invite All functionality
        const inviteBtn = document.getElementById('inviteAllBtn');
        const companyCode = inviteBtn ? inviteBtn.getAttribute('data-company-code') : '';
        const inviteModal = document.getElementById('inviteModal');
        const closeInviteModal = document.getElementById('closeInviteModal');
        const sendInvitesBtn = document.getElementById('sendInvitesBtn');
        const cancelInviteBtn = document.getElementById('cancelInviteBtn');
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

        if (inviteBtn) {
          inviteBtn.addEventListener('click', function() {
            console.log('[InviteAllBtn] Clicked');
            if (inviteModal) {
              console.log('[InviteModal] Opening modal');
              inviteModal.classList.add('show');
            } else {
              console.log('[InviteModal] Modal element not found');
            }
          });
        }

        function closeModal() {
          console.log('[InviteModal] Closing modal');
          if (inviteModal) inviteModal.classList.remove('show');
        }

        if (closeInviteModal) closeInviteModal.onclick = closeModal;
        if (cancelInviteBtn) cancelInviteBtn.onclick = closeModal;

        if (sendInvitesBtn) {
          sendInvitesBtn.onclick = async function() {
            console.log('[InviteModal] Send Invites button clicked');
            sendInvitesBtn.disabled = true;
            sendInvitesBtn.innerHTML = '<i class="ph ph-whatsapp-logo"></i> Sending...';
            try {
              const res = await fetch('/clients/' + companyCode + '/settings/whatsapp/invite', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ _csrf: csrfToken })
              });
              const data = await res.json();
              if (res.ok) {
                showToast(data.message || 'Invitations sent successfully!', 'success');
                closeModal();
              } else {
                showToast(data.error || 'Failed to send invitations.', 'error');
              }
            } catch (err) {
              showToast('Network error. Please try again.', 'error');
            } finally {
              sendInvitesBtn.disabled = false;
              sendInvitesBtn.innerHTML = 'Send Invites';
            }
          };
        }

        if (inviteModal) {
          inviteModal.addEventListener('click', function(e) {
            if (e.target === inviteModal) closeModal();
          });
        }

        // Invite Selected Modal
        const inviteSelectedModal = document.getElementById('inviteSelectedModal');
        const openInviteSelectedModal = document.getElementById('openInviteSelectedModal');
        const closeInviteSelectedModal = document.getElementById('closeInviteSelectedModal');
        const cancelInviteSelectedBtn = document.getElementById('cancelInviteSelectedBtn');
        const selectAllEmployees = document.getElementById('selectAllEmployees');
        const employeeCheckboxes = document.querySelectorAll('input[name="employeeIds"]');
        const inviteSelectedForm = document.getElementById('inviteSelectedForm');

        // Open modal
        if (openInviteSelectedModal) {
          openInviteSelectedModal.addEventListener('click', function() {
            console.log('[InviteSelectedModal] Opening modal');
            if (inviteSelectedModal) {
              inviteSelectedModal.classList.add('show');
            }
          });
        }

        // Close modal
        function closeSelectedModal() {
          console.log('[InviteSelectedModal] Closing modal');
          if (inviteSelectedModal) {
            inviteSelectedModal.classList.remove('show');
          }
        }

        if (closeInviteSelectedModal) {
          closeInviteSelectedModal.addEventListener('click', closeSelectedModal);
        }

        if (cancelInviteSelectedBtn) {
          cancelInviteSelectedBtn.addEventListener('click', closeSelectedModal);
        }

        // Select All functionality
        if (selectAllEmployees) {
          selectAllEmployees.addEventListener('change', function() {
            const isChecked = this.checked;
            employeeCheckboxes.forEach(checkbox => {
              checkbox.checked = isChecked;
            });
          });
        }

        // Form submission
        if (inviteSelectedForm) {
          inviteSelectedForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            const selectedEmployees = Array.from(employeeCheckboxes)
              .filter(cb => cb.checked)
              .map(cb => cb.value);

            if (selectedEmployees.length === 0) {
              showToast('Please select at least one employee', 'warning');
              return;
            }

            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.disabled = true;
            submitBtn.textContent = 'Sending...';

            try {
              const response = await fetch(`/clients/${companyCode}/settings/whatsapp/invite-selected`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  employeeIds: selectedEmployees,
                  _csrf: document.querySelector('meta[name="csrf-token"]').content
                })
              });

              const result = await response.json();
              
              if (result.success) {
                showToast(result.message, 'success');
                closeSelectedModal();
              } else {
                showToast(result.error || 'Failed to send invites', 'error');
              }
            } catch (error) {
              console.error('Error sending invites:', error);
              showToast('Failed to send invites. Please try again.', 'error');
            } finally {
              submitBtn.disabled = false;
              submitBtn.textContent = 'Send Invites';
            }
          });
        }
      });
    </script>
  </body>
</html> 